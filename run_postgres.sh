arg=$1
name=postgres
password=${PGPASSWORD:-password}

status=$(docker inspect $name | grep Status | cut -d '"' -f 4)

if [ ! -z "$status" ]; then
  if [ "$status" != "running" ]; then
    docker start $name
  fi
  echo "Already running."
  exit 0
else
  echo "Container not found; attempt to start"
fi

if [[ "$arg" == '-i' ]]; then
  docker run -p 5432:5432 --name $name -e POSTGRES_PASSWORD=$password -e PGDATA=/var/lib/postgresql/data/pgdata \
   --rm -it -v $HOME/pgdata:/var/lib/postgresql/data/pgdata:z postgres:16.6
else
  docker run -p 5432:5432 --name $name -e POSTGRES_PASSWORD=$password -e PGDATA=/var/lib/postgresql/data/pgdata \
   -d -v $HOME/pgdata:/var/lib/postgresql/data/pgdata:z postgres:16.6
fi
