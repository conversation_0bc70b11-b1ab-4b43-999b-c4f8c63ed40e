"""
Test for the MarkdownTokenTextSplitter with file loading.
"""
import sys
import os
import tempfile

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


from ira_chat.services import file_formats


def test_markdown_file_splitter():
    """Test the MarkdownTokenTextSplitter with file loading."""
    # Create a sample markdown text
    markdown_text = """**1. Introduction **

This is a sample markdown document that we'll use to test our MarkdownTokenTextSplitter.

## Section 1

This is the first section of our document. It contains some text that should be split based on markdown headers.

### Subsection 1.1

This is a subsection with more detailed information.

## Section 2

This is the second section of our document.

```python
# This is a code block
def hello_world():
    print("Hello, world!")
```
some out of chunk words.

**3.** **Indications thérapeutiques Délivrance**

This is the third section with a list:

- Item 1
- Item 2
- Item 3

### Subsection 3.1

More content here to test the splitter.

#### Even deeper heading

This is a level 4 heading with content.
"""

    # Create a temporary markdown file
    with tempfile.NamedTemporaryFile(suffix='.md', delete=False) as temp_file:
        temp_file.write(markdown_text.encode('utf-8'))
        temp_file_path = temp_file.name

    try:
        # Load and split the file
        all_docs, split_docs = file_formats.load_and_split_file(
            markdown_text.encode('utf-8'), 
            'test.md',
            additional_meta={'test': True},
            # Use a small chunk size to force multiple chunks
            splitter=file_formats.get_text_splitter(file_ext='.md', chunk_size=50, chunk_overlap=10)
        )

        # Print the results
        print(f"Original document count: {len(all_docs)}")
        print(f"Number of chunks after splitting: {len(split_docs)}")
        
        for i, chunk in enumerate(split_docs):
            print(f"\nChunk {i+1}:")
            print("-" * 40)
            print(chunk.page_content)
            print("-" * 40)
            print(f"Metadata: {chunk.metadata}")

    finally:
        # Clean up the temporary file
        os.unlink(temp_file_path)


# def test_markdown_file_splitter_real_file():
#     """Test the MarkdownTokenTextSplitter with file loading."""
#     file_path = '/home/<USER>/Downloads/datasets/RCP_all_md/61464022.md'
#     with open(file_path, 'r') as f:
#         markdown_text = f.read()
#
#     # Load and split the file
#     all_docs, split_docs = file_formats.load_and_split_file(
#         markdown_text.encode('utf-8'),
#         'test.md',
#         additional_meta={'test': True},
#         # Use a small chunk size to force multiple chunks
#         splitter=file_formats.get_text_splitter(file_ext='.md', chunk_size=600, chunk_overlap=60)
#     )
#     print(f"Original document count: {len(all_docs)}")
#     print(f"Number of chunks after splitting: {len(split_docs)}")
#     for i, chunk in enumerate(split_docs):
#         print(f"\nChunk {i+1}:")
#         print("-" * 40)
#         print(chunk.page_content)
#         print("-" * 40)
#         print(f"Metadata: {chunk.metadata}")


if __name__ == "__main__":
    test_markdown_file_splitter()
