import unittest
from langchain_core.documents import Document
from ira_chat.services.rrf import apply_rrf_reranking


class TestRRF(unittest.TestCase):
    def test_rrf_reranking_with_different_k_values(self):
        # Create test documents
        docs = [
            Document(page_content="Doc 1", metadata={"source": "source1", "score": 0.91, '_id': 1}),
            Document(page_content="Doc 2", metadata={"source": "source2", "score": 0.9, '_id': 2}),
            Document(page_content="Doc 3", metadata={"source": "source3", "score": 0.89, '_id': 3}),
            Document(page_content="Doc 4", metadata={"source": "source4", "score": 0.8, '_id': 4}),
        ]

        # Create graded documents with different grade scores
        # This will create a different ranking than the original order
        graded_docs = docs.copy()
        graded_docs[0].metadata["grade_score"] = 7  # Doc 1: grade 7
        graded_docs[1].metadata["grade_score"] = 9  # Doc 2: grade 9
        graded_docs[2].metadata["grade_score"] = 8  # Doc 3: grade 8
        graded_docs[3].metadata["grade_score"] = 5  # Doc 4: grade 6

        # Test different k values
        k_values = [60, 30, 10, 7, 5, 2]

        print("\nTesting different k values for RRF:")
        for k in k_values:
            print(f"\nWith k={k}:")
            # Apply RRF reranking
            reranked_docs = apply_rrf_reranking(graded_docs, rrf_k=k)

            # Print the actual order for debugging
            for i, doc in enumerate(reranked_docs):
                print(f"{i+1}. {doc.page_content} (initial_rank={doc.metadata.get('initial_rank')}, "
                      f"graded_rank={doc.metadata.get('graded_rank')}, "
                      f"rrf_score={doc.metadata.get('rrf_score')})")

            # For the last k value, perform assertions
            if k == k_values[-1]:
                # The expected order based on RRF scores should be:
                # 1. Doc 2 (initial rank 2, grade rank 1)
                # 2. Doc 3 (initial rank 3, grade rank 2) or Doc 1 (initial rank 1, grade rank 3)
                # 3. Doc 1 (initial rank 1, grade rank 3) or Doc 3 (initial rank 3, grade rank 2)
                # 4. Doc 4 (initial rank 4, grade rank 4)
                self.assertEqual(reranked_docs[0].page_content, "Doc 2")

                # Since Doc 1 and Doc 3 might have very close RRF scores, we'll just check that Doc 4 is last
                self.assertEqual(reranked_docs[3].page_content, "Doc 4")

                # Check that metadata is preserved
                self.assertEqual(reranked_docs[0].metadata["source"], "source2")

                # Check that RRF scores are stored in metadata
                self.assertTrue("rrf_score" in reranked_docs[0].metadata)
                self.assertTrue("initial_rank" in reranked_docs[0].metadata)
                self.assertTrue("graded_rank" in reranked_docs[0].metadata)

        # Based on the results, recommend a good k value
        print("\nRecommendation: Use a smaller k value (like k=5 or k=10) for more distinguishable RRF scores.")


if __name__ == "__main__":
    unittest.main()
