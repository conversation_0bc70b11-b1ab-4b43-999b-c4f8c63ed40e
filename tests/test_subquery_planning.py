import asyncio
import logging

from langchain_openai import ChatOpenAI

from ira_chat.services import subquery_agent
from ira_chat.services.agents import AgentState

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_smart_query_planning():
    # real LLM
    llm = ChatOpenAI(model_name='gpt-4o-mini', temperature=0.5)

    test_query = (
        'Write a quick fact sheet on a drug interaction between PRAVASTATIN BIOGARAN 20 mg, scored film-coated '
        'tablet (69511004) and SMECTA 3 g ORANGE-VANILLA, powder for oral suspension in sachet (69584073). '
        'Follow the following structure with fixed headings: "Interaction detected" "Mechanism of interaction" "Action to be taken" '
        '"Therapeutic alternative". '
        'Each section must be written in the form of short sentences (no lists, no bullet points). '
        'Use professional vocabulary accessible to a pharmacy technician. '
        'The tone must be clear, direct, and concise. '
        'The text must be readable in less than a minute. '
        'Example of expected style: One or two sentences maximum per section. Simple medical terms '
        '(e.g., "risk of bleeding", "INR monitoring", "antiplatelet effect"). '
        'Make the main information immediately understandable to facilitate rapid decision-making in the pharmacy.'
    )
    generated_query = (
        'Create a concise fact sheet on the drug interaction between PRAVASTATIN BIOGARAN 20 mg and SMECTA 3 g ORANGE-VANILLA, '
        'focusing on interaction detected, mechanism, action to be taken, and therapeutic alternatives, '
        'using pharmacy technician-friendly language.'
    )

    test_agent_state = AgentState(
        messages=[("human", test_query)],
        question=test_query,
        generated_question=generated_query,
        path="",
        answer="",
        initial_documents=[],
        source_documents=[],
        rewritten=False,
        input_ok=True,
        output_ok=True,
        output_check_score=0,
        not_answer=False,
        not_answer_reason="",
        query_plan={},
        subqueries={},
        combined_summary="",
        topic_group="",
        topic="",
        topic_confidence=0,
        cache_hit=False,
        org_id=1,
        workspace_id=1,
        dataset_id=1,
    )

    async def run_test():
        smart_query_planning_function = subquery_agent.smart_query_planning_fn(llm, {})
        return await smart_query_planning_function(test_agent_state)

    query_plan = asyncio.run(run_test())
    print(query_plan)


if __name__ == "__main__":
    test_smart_query_planning()
