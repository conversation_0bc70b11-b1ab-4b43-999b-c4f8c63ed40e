import unittest
import asyncio
from langchain_core.documents import Document
from ira_chat.services.agents import rerank_docs_fn


class TestRerankNode(unittest.TestCase):
    def test_rerank_node(self):
        # Create test documents with grade scores
        docs = [
            Document(page_content="Doc 1", metadata={"source": "source1", "score": 0.9, "grade_score": 7, "_id": "1"}),
            Document(page_content="Doc 2", metadata={"source": "source2", "score": 0.8, "grade_score": 9, "_id": "2"}),
            Document(page_content="Doc 3", metadata={"source": "source3", "score": 0.7, "grade_score": 8, "_id": "3"}),
            Document(page_content="Doc 4", metadata={"source": "source4", "score": 0.6, "grade_score": 6, "_id": "4"}),
        ]

        # Create a mock state
        state = {
            "source_documents": docs,
            "initial_documents": docs,
        }

        # Create the rerank function
        config = {"rrf_k": 10}
        rerank_fn = rerank_docs_fn(config)

        # Run the rerank function
        result = asyncio.run(rerank_fn(state))

        # Check that the documents are reranked
        reranked_docs = result["source_documents"]

        # Print the reranked documents for debugging
        print("Reranked documents:")
        for i, doc in enumerate(reranked_docs):
            print(f"{i+1}. {doc.page_content} (grade_score={doc.metadata.get('grade_score')}, "
                  f"rrf_score={doc.metadata.get('rrf_score')})")

        # Check that the documents have RRF scores
        self.assertTrue("rrf_score" in reranked_docs[0].metadata)

        # Check that the highest graded document is first
        self.assertEqual(reranked_docs[0].page_content, "Doc 2")  # Doc 2 has grade_score 9


if __name__ == "__main__":
    unittest.main()
