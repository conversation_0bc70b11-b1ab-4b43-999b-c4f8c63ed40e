"""
Test for the MarkdownTokenTextSplitter.
"""
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ira_chat.services.text_splitters import MarkdownTokenTextSplitter
from langchain_core.documents import Document

def test_markdown_token_splitter():
    """Test the MarkdownTokenTextSplitter."""
    # Create a sample markdown text
    markdown_text = """# Introduction

This is a sample markdown document that we'll use to test our MarkdownTokenTextSplitter.

## Section 1

This is the first section of our document. It contains some text that should be split based on markdown headers.

### Subsection 1.1

This is a subsection with more detailed information.

## Section 2

This is the second section of our document.

```python
# This is a code block
def hello_world():
    print("Hello, world!")
```

## Section 3

This is the third section with a list:

- Item 1
- Item 2
- Item 3

### Subsection 3.1

More content here to test the splitter.

#### Even deeper heading

This is a level 4 heading with content.
"""

    # Create a document
    doc = Document(page_content=markdown_text)

    # Create the splitter with a small chunk size to force multiple chunks
    splitter = MarkdownTokenTextSplitter(
        chunk_size=100,  # Small chunk size for testing
        chunk_overlap=20
    )

    # Split the document
    chunks = splitter.split_documents([doc])

    # Print the results
    print(f"Original document token count: {splitter.count_tokens(markdown_text)}")
    print(f"Number of chunks: {len(chunks)}")
    
    for i, chunk in enumerate(chunks):
        print(f"\nChunk {i+1} (tokens: {splitter.count_tokens(chunk.page_content)}):")
        print("-" * 40)
        print(chunk.page_content[:100] + "..." if len(chunk.page_content) > 100 else chunk.page_content)
        print("-" * 40)

if __name__ == "__main__":
    test_markdown_token_splitter()
