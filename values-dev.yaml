api:
  Image:
    Name: kuberlab/ira-chat-api
    Tag: latest
  baseUrl: https://dev-hotline.kibernetika.io
  db:
    dbname: ira
    enabled: true
    host: ira-chat-db
    password: Privet1961sneg
    user: postgres
  dns:
  - dev-hotline.kibernetika.io
  envPrefix: dev
  envVars:
  - name: dummy
    value: value
  - name: VECTORSTORE
    value: qdrant
  - name: VECTORSTORE_ON_DISK
    value: "true"
  - name: LANGFUSE_SECRET_KEY
    value: ******************************************
  - name: LANGFUSE_PUBLIC_KEY
    value: pk-lf-48cac7ec-5607-4ebb-8636-a0a553362dd0
  replicas: 3
resources:
   limits:
     memory: 4G
   requests:
     memory: 1G
credentials:
  admins: WzQsNSw2XQo=
  smtp: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  google: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
crypto: jS2fKKFSlfco3mxLdfckd0ew
qdrant:
  nameOverride: "qdrant"
  fullnameOverride: "qdrant"
  resources:
    limits:
  #   cpu: 100m
      memory: 8Gi
    requests:
  #   cpu: 100m
      memory: 1Gi
  persistence:
    size: 20G
    storageClass: premium-rwo

  image:
    repository: docker.io/qdrant/qdrant
    pullPolicy: IfNotPresent
    tag: "v1.13.2"

  # api key for authentication at qdrant
  # false: no api key will be configured
  # true: an api key will be auto-generated
  # string: the given string will be set as an apikey
  apiKey: false

rabbitmq:
  auth:
    username: kibernetika
    password: kibernetika
    erlangCookie: kibernetika
  resourcesPreset: small
  replicaCount: 1
queueWorker:
  replicas: 1
  resources:
    limits:
      memory: 1500Mi
    requests:
      memory: 500Mi

persistence:
  db:
    size: 30G
    storageClass: premium-rwo
  files:
    size: 50G
    storageClass: standard
ui:
  Image:
    Name: kuberlab/ira-chat-ui
    Tag: latest
  replicas: 1
wildcard:
  dns: '*.dev-hotline.kibernetika.io'
  enabled: true
  googleProject: lucid-timing-151005
  serviceAccountJson: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
