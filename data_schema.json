{"data_schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"accounting_method": {"type": ["string", "null"]}, "active_since": {"type": ["string", "null"]}, "agr_signed": {"type": ["string", "null"]}, "agreement_sum": {"type": ["number", "null"]}, "billing_method": {"type": ["string", "null"]}, "bookkeeping": {"type": ["boolean", "null"]}, "name": {"type": "string"}, "company_phone": {"type": ["string", "null"]}, "cpa": {"type": ["string", "null"]}, "description": {"type": ["string", "null"]}, "dissolution_date": {"type": ["string", "null"]}, "ein": {"type": ["string", "null"]}, "fedtaxforms": {"type": ["string", "null"]}, "financial_year_end": {"type": ["string", "null"]}, "financial_year_end_for_subsidiary": {"type": ["string", "null"]}, "incorp_by": {"type": ["string", "null"]}, "legal_ent_type": {"type": ["string", "null"]}, "login": {"type": ["string", "null"]}, "monthly_bill": {"type": ["number", "null"]}, "naicscode": {"type": ["string", "null"]}, "notes_accounting": {"type": ["string", "null"]}, "notes_address": {"type": ["string", "null"]}, "notes_agreement": {"type": ["string", "null"]}, "notes_contacts": {"type": ["string", "null"]}, "notes_main": {"type": ["string", "null"]}, "notes_shareholders": {"type": ["string", "null"]}, "optional_share_count": {"type": ["integer", "null"]}, "paid_by": {"type": ["string", "null"]}, "paid_by_mail": {"type": ["string", "null"]}, "password": {"type": ["string", "null"]}, "payroll": {"type": ["boolean", "null"]}, "renewal_date": {"type": ["string", "null"]}, "renewal_date_mail": {"type": ["string", "null"]}, "since": {"type": ["string", "null"]}, "statetaxforms": {"type": ["string", "null"]}, "status": {"type": ["string", "null"]}, "subjurisd": {"type": ["string", "null"]}, "subsidiary_legal_entity_type": {"type": ["string", "null"]}, "subsidiary_to_consolidate": {"type": ["string", "null"]}, "total_shares": {"type": ["integer", "null"]}, "withdrawal_date": {"type": ["string", "null"]}, "addresses": {"type": "array", "items": [{"type": "object", "properties": {"address_type": {"type": "string"}, "renewal_date": {"type": ["string", "null"]}, "phone": {"type": ["string", "null"]}, "paid_by": {"type": ["string", "null"]}, "note": {"type": ["string", "null"]}, "address": {"type": ["object", "null"], "properties": {"street": {"type": "string"}, "country": {"type": "string"}, "city": {"type": ["string", "null"]}, "full_address": {"type": ["string", "null"]}, "pobox": {"type": ["string", "null"]}, "state": {"type": ["string", "null"]}, "zip": {"type": ["string", "null"]}}, "required": ["full_address", "street", "pobox", "city", "state", "zip", "country"]}}, "required": ["address_type", "renewal_date", "phone", "paid_by", "note", "address"]}]}, "authorized_signers": {"type": "array", "items": [{"type": "object", "properties": {"note": {"type": ["string", "null"]}, "person": {"type": "object", "properties": {"firstname": {"type": "string"}, "lastname": {"type": "string"}, "email": {"type": ["string", "null"]}, "phone": {"type": ["string", "null"]}, "pcm": {"type": ["string", "null"]}, "citizenship": {"type": ["string", "null"]}, "address": {"type": ["string", "null"]}, "companies": {"type": ["string", "null"]}}, "required": ["firstname", "lastname", "email", "phone", "pcm", "citizenship", "address", "companies"]}}, "required": ["note", "person"]}]}, "bank_accounts": {"type": "array", "items": [{"type": "object", "properties": {"bank_name": {"type": "string"}, "aba_number": {"type": "string"}, "account_number": {"type": ["string", "null"]}, "bank_contact": {"type": "string"}, "controlled_by": {"type": ["string", "null"]}, "date_opened": {"type": ["string", "null"]}, "last_renewal": {"type": ["string", "null"]}, "notes": {"type": ["string", "null"]}, "authorized_signer_persons": {"type": "array", "items": [{"type": "object", "properties": {"firstname": {"type": "string"}, "lastname": {"type": "string"}, "email": {"type": ["string", "null"]}, "phone": {"type": ["string", "null"]}, "pcm": {"type": ["string", "null"]}, "citizenship": {"type": ["string", "null"]}, "address": {"type": ["string", "null"]}, "companies": {"type": ["string", "null"]}}, "required": ["firstname", "lastname", "email", "phone", "pcm", "citizenship", "address", "companies"]}]}, "person": {"type": ["object", "null"], "properties": {"firstname": {"type": "string"}, "lastname": {"type": "string"}, "email": {"type": ["string", "null"]}, "phone": {"type": ["string", "null"]}, "pcm": {"type": ["string", "null"]}, "citizenship": {"type": ["string", "null"]}, "address": {"type": ["string", "null"]}, "companies": {"type": ["string", "null"]}}, "required": ["firstname", "lastname", "email", "phone", "pcm", "citizenship", "address", "companies"]}}, "required": ["bank_name", "aba_number", "account_number", "bank_contact", "controlled_by", "date_opened", "last_renewal", "notes", "authorized_signer_persons"]}]}, "contacts": {"type": "array", "items": [{"type": "object", "properties": {"position": {"type": "string"}, "email": {"type": ["string", "null"]}, "phone": {"type": ["string", "null"]}, "pcm": {"type": ["string", "null"]}, "note": {"type": ["string", "null"]}, "person": {"type": "object", "properties": {"firstname": {"type": "string"}, "lastname": {"type": "string"}, "email": {"type": ["string", "null"]}, "phone": {"type": ["string", "null"]}, "pcm": {"type": ["string", "null"]}, "citizenship": {"type": ["string", "null"]}, "address": {"type": ["string", "null"]}, "companies": {"type": ["string", "null"]}}, "required": ["firstname", "lastname", "email", "phone", "pcm", "citizenship", "address", "companies"]}}, "required": ["position", "email", "phone", "pcm", "note", "person"]}]}, "payment_cards": {"type": "array", "items": [{"type": "object", "properties": {"debit_card": {"type": "string"}, "last_4_digits": {"type": "string"}, "expired_at": {"type": "string"}, "cid": {"type": "string"}, "linked_to": {"type": "string"}, "card_holder": {"type": "string"}, "exp": {"type": "string"}}, "required": ["debit_card", "last_4_digits", "expired_at", "cid", "linked_to", "card_holder", "exp"]}]}, "payment_services": {"type": "array", "items": [{"type": "object", "properties": {"payment_system": {"type": "string"}, "date_opened": {"type": ["string", "null"]}, "opened_by": {"type": ["string", "null"]}, "email_connected": {"type": ["string", "null"]}, "responsible_person": {"type": ["string", "null"]}, "note": {"type": ["string", "null"]}}, "required": ["payment_system", "date_opened", "opened_by", "email_connected", "responsible_person", "note"]}]}, "primary_registration": {"type": "object", "properties": {"reg_date": {"type": ["string", "null"]}, "deregister_date": {"type": ["string", "null"]}, "last_renewal_date": {"type": ["string", "null"]}, "reg_state": {"type": "string"}, "reg_pay_by": {"type": ["string", "null"]}, "last_soi_filed": {"type": ["string", "null"]}, "state_entity": {"type": ["string", "null"]}, "notes": {"type": ["string", "null"]}, "reg_agent": {"type": ["object", "null"], "properties": {"address": {"type": "string"}, "title": {"type": "string"}, "nickname": {"type": "string"}}, "required": ["address", "title", "nickname"]}}, "required": ["reg_date", "deregister_date", "last_renewal_date", "reg_state", "reg_pay_by", "last_soi_filed", "state_entity", "notes", "reg_agent"]}, "secondary_registrations": {"type": "array", "items": [{"type": "object", "properties": {"reg_date": {"type": ["string", "null"]}, "deregister_date": {"type": ["string", "null"]}, "last_renewal_date": {"type": ["string", "null"]}, "reg_state": {"type": "string"}, "reg_pay_by": {"type": ["string", "null"]}, "last_soi_filed": {"type": ["string", "null"]}, "state_entity": {"type": ["string", "null"]}, "notes": {"type": ["string", "null"]}, "reg_agent": {"type": ["object", "null"], "properties": {"address": {"type": "string"}, "title": {"type": "string"}, "nickname": {"type": "string"}}, "required": ["address", "title", "nickname"]}}, "required": ["reg_date", "deregister_date", "last_renewal_date", "reg_state", "reg_pay_by", "last_soi_filed", "state_entity", "notes", "reg_agent"]}]}, "share_classes": {"type": "array", "items": [{"type": "object", "properties": {"stock_authorized": {"type": "integer"}, "stock_issued": {"type": "integer"}, "shares_authorized_preferred": {"type": "string"}, "shares_issued_preferred": {"type": "integer"}, "notes": {"type": ["string", "null"]}}, "required": ["stock_authorized", "stock_issued", "shares_authorized_preferred", "shares_issued_preferred", "notes"]}]}, "shareholders": {"type": "array", "items": [{"type": "object", "properties": {"position": {"type": "string"}, "ownership": {"type": "string"}, "note": {"type": ["string", "null"]}}, "required": ["position", "ownership", "note"]}]}, "tax_reporting": {"type": "array", "items": [{"type": "object", "properties": {"year": {"type": "string"}, "reporting_1099": {"type": "string"}, "tax_return_by": {"type": "string"}, "note": {"type": ["string", "null"]}, "files": {"type": ["string", "null"]}}, "required": ["year", "reporting_1099", "tax_return_by", "note", "files"]}]}}, "required": ["accounting_method", "active_since", "agr_signed", "agreement_sum", "billing_method", "bookkeeping", "name", "company_phone", "cpa", "description", "dissolution_date", "ein", "fedtaxforms", "financial_year_end", "financial_year_end_for_subsidiary", "incorp_by", "legal_ent_type", "monthly_bill", "naicscode", "notes_accounting", "notes_address", "notes_agreement", "notes_contacts", "notes_main", "notes_shareholders", "optional_share_count", "paid_by", "paid_by_mail", "password", "payroll", "renewal_date", "renewal_date_mail", "since", "statetaxforms", "status", "subjurisd", "subsidiary_legal_entity_type", "subsidiary_to_consolidate", "total_shares", "withdrawal_date", "addresses", "authorized_signers", "bank_accounts", "contacts", "payment_cards", "payment_services", "primary_registration", "secondary_registrations", "share_classes", "shareholders", "tax_reporting"]}}