--- /usr/local/lib/python3.12/dist-packages/langchain_community/document_loaders/parsers/pdf.py 2025-02-03 17:18:04.311306156 -0800
+++ pdf.py      2025-02-03 17:45:42.217436993 -0800
@@ -735,6 +735,8 @@
             if self.images_parser:
                 xref = img[0]
                 pix = pymupdf.Pixmap(doc, xref)
+                if pix.height < 8 or pix.width < 8:
+                    continue
                 image = np.frombuffer(pix.samples, dtype=np.uint8).reshape(
                     pix.height, pix.width, -1
                 )

