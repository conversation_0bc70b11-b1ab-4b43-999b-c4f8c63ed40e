--- /usr/local/lib/python3.12/dist-packages/ragas/evaluation.py	2025-05-07 16:59:21.205771788 -0700
+++ evaluation.py	2025-05-07 17:01:34.771989163 -0700
@@ -54,7 +54,7 @@
 
 
 @track_was_completed
-def evaluate(
+async def evaluate(
     dataset: t.Union[Dataset, EvaluationDataset],
     metrics: t.Optional[t.Sequence[Metric]] = None,
     llm: t.Optional[BaseRagasLLM | LangchainLLM] = None,
@@ -291,7 +291,7 @@
     scores: t.List[t.Dict[str, t.Any]] = []
     try:
         # get the results
-        results = executor.results()
+        results = await executor.aresults()
         if results == []:
             raise ExceptionInRunner()
 
