{{- if .Values.wildcard.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ws-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/issuer: ira-chat-ws-issuer
    nginx.ingress.kubernetes.io/proxy-body-size: 256m
spec:
  tls:
  - hosts:
    - '{{ .Values.wildcard.dns }}'
    secretName: wildcard-ira-app-tls
  rules:
  - host: '{{ .Values.wildcard.dns }}'
    http:
      paths:
        - path: /docs
          pathType: Prefix
          backend:
            service:
              name: ira-chat-api
              port:
                number: 8083
        - path: /openapi.json
          pathType: Prefix
          backend:
            service:
              name: ira-chat-api
              port:
                number: 8083
        - path: /api
          pathType: Prefix
          backend:
            service:
              name: ira-chat-api
              port:
                number: 8083
        - path: /
          pathType: Prefix
          backend:
            service:
              name: ira-chat-ui
              port:
                number: 80
{{- end }}