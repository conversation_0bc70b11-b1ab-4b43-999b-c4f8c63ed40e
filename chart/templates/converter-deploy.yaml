apiVersion: apps/v1
kind: Deployment
metadata:
  name: doc-converter
spec:
  selector:
    matchLabels:
      app: ira-chat
      component: doc-converter
  replicas: 1
  template:
    metadata:
      labels:
        app: ira-chat
        component: doc-converter
    spec:
      containers:
        - name:  doc-converter
          image: "{{ printf "%s:%s" .Values.converter.Image.Name .Values.converter.Image.Tag}}"
          readinessProbe:
            httpGet:
              path: /probe
              port: 8085
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3
          env:
            - name: DATA_DIR
              value: '/data'
          ports:
            - containerPort: 8085
              name: http
