apiVersion: apps/v1
kind: Deployment
metadata:
  name: ira-chat-ui
spec:
  replicas: {{ .Values.ui.replicas }}
  selector:
    matchLabels:
      app: ira-chat
      component: ira-chat-ui
  template:
    metadata:
      labels:
        app: ira-chat
        component: ira-chat-ui
    spec:
      containers:
        - name: ira-chat-ui
          image: "{{ printf "%s:%s" .Values.ui.Image.Name .Values.ui.Image.Tag}}"
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3
          ports:
            - containerPort: 80
              name: http
