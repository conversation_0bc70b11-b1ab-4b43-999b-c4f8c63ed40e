apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
spec:
  selector:
    matchLabels:
      app: ira-chat
      component: redis
  template:
    metadata:
      labels:
        app: ira-chat
        component: redis
    spec:
      {{- if .Values.tolerations }}
      tolerations:
      {{- range .Values.tolerations }}
      - effect: {{ .effect }}
        key: "{{ .key }}"
        operator: Equal
        value: "{{ .value }}"
      {{- end }}
      {{- end }}
      {{- if .Values.nodeSelector }}
      nodeSelector:
        {{- range $k, $v := .Values.nodeSelector }}
        {{ $k }}: "{{ $v }}"
        {{- end }}
      {{- end }}
      containers:
        - name: redis
          image: redis:6.2-alpine
          ports:
            - containerPort: 6379
              name: redis
