apiVersion: apps/v1
kind: Deployment
metadata:
  name: queue-worker
spec:
  selector:
    matchLabels:
      app: ira-chat
      component: queue-worker
  replicas: {{ .Values.queueWorker.replicas }}
  template:
    metadata:
      labels:
        app: ira-chat
        component: queue-worker
    spec:
      containers:
        - name:  queue-worker
          image: "{{ printf "%s:%s" .Values.api.Image.Name .Values.api.Image.Tag}}"
          command:
            - taskiq
            - worker
            - 'ira_chat.config.broker:get_broker'
            - -tp
            - '**/**/*_tasks.py'
            - -fsd
            - --max-async-tasks
            - '10'
            - --shutdown-timeout
            - '30'
          resources:
            {{- toYaml .Values.queueWorker.resources | nindent 12 }}
          env:
            - name: PGHOST
            {{- if .Values.api.db.enabled }}
              value: ira-chat-db
            {{- else }}
              value: {{ .Values.api.db.host }}
            {{- end }}
            - name: PGUSER
              value: {{ .Values.api.db.user }}
            - name: PGPASSWORD
              value: {{ .Values.api.db.password }}
            - name: PGDATABASE
              value: {{ .Values.api.db.dbname }}
            - name: BASE_URL
              value: {{ .Values.api.baseUrl }}
            - name: FILE_API_URL
              value: http://chat-file-api:8084
            - name: CONVERTER_API_URL
              value: http://doc-converter:8085
            - name: ENV_PREFIX
              value: {{ .Values.api.envPrefix }}
            - name: CREDENTIALS
              value: "/go/credentials"
            - name: REDIS_HOST
              value: "redis"
            - name: RABBITMQ_URL
              value: "{{ printf "amqp://%s:%s@%s-rabbitmq:5672" .Values.rabbitmq.auth.username .Values.rabbitmq.auth.password .Release.Name }}"
            {{- range .Values.api.envVars }}
            - name: {{ .name }}
              value: '{{ .value }}'
            {{- end }}
            - name: BLOWFISH_SALT
              valueFrom:
                secretKeyRef:
                  name: crypto
                  key: blowfish-salt
          ports:
            - containerPort: 8083
              name: http
          volumeMounts:
            - mountPath: /go/credentials
              name: credentials
            #- mountPath: /data
            #  name: data
      terminationGracePeriodSeconds: 90
      volumes:
        - name: credentials
          secret:
            secretName: ira-chat-credentials
            items:
              {{- range $key, $value := .Values.credentials }}
              - key: {{ $key }}.json
                path: {{ $key }}.json
              {{- end }}
