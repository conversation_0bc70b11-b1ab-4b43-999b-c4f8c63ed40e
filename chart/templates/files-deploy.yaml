apiVersion: apps/v1
kind: Deployment
metadata:
  name: chat-file-api
spec:
  selector:
    matchLabels:
      app: chat-file
      component: chat-file-api
  replicas: 1
  template:
    metadata:
      labels:
        app: chat-file
        component: chat-file-api
    spec:
      containers:
        - name:  chat-file-api
          image: "{{ printf "%s:%s" .Values.files.Image.Name .Values.files.Image.Tag}}"
          readinessProbe:
            httpGet:
              path: /probe
              port: 8084
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3
          env:
            - name: DATA_DIR
              value: '/data'
          ports:
            - containerPort: 8084
              name: http
          volumeMounts:
            - mountPath: /data
              name: data
      volumes:
        - name: data
          persistentVolumeClaim:
            claimName: chat-file-data