{{- if .Values.wildcard.enabled }}
---
apiVersion: v1
kind: Secret
metadata:
  name: api-key
type: Opaque
data:
  service-account.json: '{{ .Values.wildcard.serviceAccountJson }}'
---
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: ira-chat-ws-issuer
spec:
  acme:
    # ACME Server
    # prod : https://acme-v02.api.letsencrypt.org/directory
    # staging : https://acme-staging-v02.api.letsencrypt.org/directory
    server: https://acme-v02.api.letsencrypt.org/directory
    # ACME Email address
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-production # staging or production
    solvers:
      - selector:
          dnsZones:
          - '{{ trimPrefix "*." .Values.wildcard.dns }}'
        dns01:
          cloudDNS:
            project: {{ .Values.wildcard.googleProject }}
            serviceAccountSecretRef:
              name: api-key
              key: service-account.json
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: wildcard-ira-app
spec:
  secretName: wildcard-ira-app-tls
  renewBefore: 240h
  commonName: '{{ .Values.wildcard.dns }}'
  dnsNames:
  - '{{ .Values.wildcard.dns }}'
  issuerRef:
    name: ira-chat-ws-issuer
    kind: Issuer
{{- end }}