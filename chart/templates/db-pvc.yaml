{{- if .Values.api.db.enabled }}
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: ira-chat-db
  labels:
    app: ira-chat
    component: db
spec:
  {{- if .Values.persistence.db.storageClass }}
  storageClassName: {{ .Values.persistence.db.storageClass | quote }}
  {{- end }}
  accessModes:
    - "ReadWriteOnce"
  resources:
    requests:
      storage: {{ .Values.persistence.db.size }}
{{- end }}