apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ira-chat-tls
  annotations:
    kubernetes.io/ingress.class: "nginx"
    certmanager.k8s.io/cluster-issuer: kibernetika-cert-issuer
    cert-manager.io/cluster-issuer: kibernetika-cert-issuer
    nginx.ingress.kubernetes.io/proxy-body-size: 256m
    {{ if .Values.api.redirectDomain }}
    nginx.ingress.kubernetes.io/rewrite-target: "https://{{ .Values.api.redirectDomain }}/$1"
    {{- end }}
spec:
  tls:
    - hosts:
      {{- range $i, $dns := .Values.api.dns }}
      - "{{ $dns }}"
      {{- end }}
      secretName: ira-chat-tls
  rules:
  {{- range $i, $dns := .Values.api.dns }}
  - host: {{ $dns }}
    http:
      paths:
        - path: /docs
          pathType: Prefix
          backend:
            service:
              name: ira-chat-api
              port:
                number: 8083
        - path: /openapi.json
          pathType: Prefix
          backend:
            service:
              name: ira-chat-api
              port:
                number: 8083
        - path: /api
          pathType: Prefix
          backend:
            service:
              name: ira-chat-api
              port:
                number: 8083
        - path: /
          pathType: Prefix
          backend:
            service:
              name: ira-chat-ui
              port:
                number: 80
  {{- end }}
