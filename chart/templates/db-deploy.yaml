{{- if .Values.api.db.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ira-chat-db
spec:
  selector:
    matchLabels:
      app: ira-chat
      component: ira-chat-db
  template:
    metadata:
      labels:
        app: ira-chat
        component: ira-chat-db
    spec:
      containers:
        - name:  ira-chat-db
          image: postgres:17.4
          env:
            - name: POSTGRES_USER
              value: {{ .Values.api.db.user }}
            - name: PGUSER
              value: {{ .Values.api.db.user }}
            - name: POSTGRES_PASSWORD
              value: {{ .Values.api.db.password }}
            - name: POSTGRES_DB
              value: {{ .Values.api.db.dbname }}
            - name: PGDATA
              value: /var/lib/postgresql/data/pgdata
          ports:
            - containerPort: 5432
              name: psql
          volumeMounts:
            - name: db
              mountPath: /var/lib/postgresql/data/pgdata
              subPath: ira-chat-db
      volumes:
        - name: db
          persistentVolumeClaim:
            claimName: ira-chat-db
{{- end }}