apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "qdrant.fullname" . }}
  labels:
    {{- include "qdrant.labels" . | nindent 4 }}
{{- with .Values.additionalLabels }}
{{- toYaml . | nindent 4 }}
{{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  podManagementPolicy: Parallel
  selector:
    matchLabels:
      {{- include "qdrant.selectorLabels" . | nindent 6 }}
  serviceName: {{ include "qdrant.fullname" . }}-headless
  template:
    metadata:
      annotations:
      {{- if (default .Values.updateConfigurationOnChange false) }}
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      {{- end }}
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "qdrant.selectorLabels" . | nindent 8 }}
        {{- with .Values.podLabels }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.priorityClassName }}
      priorityClassName: "{{ .Values.priorityClassName }}"
      {{- end }}
      initContainers:
      {{- if .Values.updateVolumeFsOwnership }}
      {{- if and .Values.containerSecurityContext .Values.containerSecurityContext.runAsUser }}
      - name: ensure-storage-dir-ownership
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
        command:
          - chown
          - -R
          - {{ .Values.containerSecurityContext.runAsUser }}:{{ .Values.podSecurityContext.fsGroup }}
          - /qdrant/storage
        volumeMounts:
          - name: qdrant-storage
            mountPath: /qdrant/storage
      {{- if .Values.snapshotRestoration.enabled }}
      - name: ensure-snapshots-dir-ownership
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
        command:
          - chown
          - -R
          - {{ .Values.containerSecurityContext.runAsUser }}:{{ .Values.podSecurityContext.fsGroup }}
          - /qdrant/snapshots
        volumeMounts:
          - name: qdrant-snapshots
            mountPath: /qdrant/snapshots
      {{- end }}
      {{- end }}
      {{- end }}
      containers:
        {{- if .Values.sidecarContainers -}}
        {{- toYaml .Values.sidecarContainers | trim | nindent 8 }}
        {{- end}}
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}{{ .Values.image.useUnprivilegedImage | ternary "-unprivileged" "" }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: QDRANT_INIT_FILE_PATH
              value: /qdrant/init/.qdrant-initialized
          {{- range .Values.env }}
            - name: {{ .name }}
              value: {{ .value | quote }}
          {{- end }}
          command: ["/bin/bash", "-c"]
          {{- with .Values.args }}
          args:
            {{- toYaml . | nindent 10 }}
          {{- end }}
          ports:
          {{- range .Values.service.ports }}
            - name: {{ .name }}
              containerPort: {{ .targetPort }}
              protocol: {{ .protocol }}
          {{- end }}

          {{- $values := .Values -}}
          {{- range .Values.service.ports }}
          {{- if and $values.livenessProbe.enabled .checksEnabled }}
          livenessProbe:
            {{- if eq .name "grpc"}}
            grpc:
              port: {{ .targetPort }}
            {{- end }}
            {{- if eq .name "http"}}
            httpGet:
              path: /
              port: {{ .targetPort }}
              {{- if and $values.config.service $values.config.service.enable_tls }}
              scheme: HTTPS
              {{- end }}
            {{- end }}
            initialDelaySeconds: {{ $values.livenessProbe.initialDelaySeconds }}
            timeoutSeconds: {{ $values.livenessProbe.timeoutSeconds }}
            periodSeconds: {{ $values.livenessProbe.periodSeconds }}
            successThreshold: {{ $values.livenessProbe.successThreshold }}
            failureThreshold: {{ $values.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if and $values.readinessProbe.enabled .checksEnabled }}
          readinessProbe:
            {{- if eq .name "grpc"}}
            grpc:
              port: {{ .targetPort }}
            {{- end }}
            {{- if eq .name "http"}}
            httpGet:
              path: "{{- if semverCompare ">=1.7.3" ($.Values.image.tag | default $.Chart.AppVersion) -}}/readyz{{else}}/{{end}}"
              port: {{ .targetPort }}
              {{- if and $values.config.service $values.config.service.enable_tls }}
              scheme: HTTPS
              {{- end }}
            {{- end }}
            initialDelaySeconds: {{ $values.readinessProbe.initialDelaySeconds }}
            timeoutSeconds: {{ $values.readinessProbe.timeoutSeconds }}
            periodSeconds: {{ $values.readinessProbe.periodSeconds }}
            successThreshold: {{ $values.readinessProbe.successThreshold }}
            failureThreshold: {{ $values.readinessProbe.failureThreshold }}
          {{- end }}
          {{- if and $values.startupProbe.enabled .checksEnabled }}
          startupProbe:
            {{- if eq .name "grpc"}}
            grpc:
              port: {{ .targetPort }}
            {{- end }}
            {{- if eq .name "http"}}
            httpGet:
              path: /
              port: {{ .targetPort }}
              {{- if and $values.config.service $values.config.service.enable_tls }}
              scheme: HTTPS
              {{- end }}
            {{- end }}
            initialDelaySeconds: {{ $values.startupProbe.initialDelaySeconds }}
            timeoutSeconds: {{ $values.startupProbe.timeoutSeconds }}
            periodSeconds: {{ $values.startupProbe.periodSeconds }}
            successThreshold: {{ $values.startupProbe.successThreshold }}
            failureThreshold: {{ $values.startupProbe.failureThreshold }}
          {{- end }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- with .Values.containerSecurityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          lifecycle:
            preStop:
              exec:
                command: ["sleep", "3"]
          volumeMounts:
          - name: qdrant-storage
            mountPath: /qdrant/storage
          - name: qdrant-config
            mountPath: /qdrant/config/initialize.sh
            subPath: initialize.sh
          - name: qdrant-config
            mountPath: /qdrant/config/production.yaml
            subPath: production.yaml
          {{- if .Values.apiKey }}
          - name: qdrant-secret
            mountPath: /qdrant/config/local.yaml
            subPath: local.yaml
          {{- end }}
          - name: qdrant-snapshots
            mountPath: /qdrant/snapshots
          - name: qdrant-init
            mountPath: /qdrant/init
          {{- if .Values.additionalVolumeMounts }}
{{- toYaml .Values.additionalVolumeMounts | default "" | nindent 10 }}
          {{- end}}
      {{- with .Values.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints}}
      topologySpreadConstraints:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "qdrant.fullname" . }}
      volumes:
        - name: qdrant-config
          configMap:
            name: {{ include "qdrant.fullname" . }}
            defaultMode: 0755
        {{- if .Values.snapshotRestoration.enabled }}
        - name: qdrant-snapshots
          persistentVolumeClaim:
            claimName: {{ .Values.snapshotRestoration.pvcName }}
        {{- else }}
        - name: qdrant-snapshots
          emptyDir: {}
        {{- end }}
        - name: qdrant-init
          emptyDir: {}
        {{- if .Values.apiKey }}
        - name: qdrant-secret
          secret:
            secretName: {{ include "qdrant.fullname" . }}-apikey
            defaultMode: 0600
        {{- end }}
        {{- if .Values.additionalVolumes }}
{{- toYaml .Values.additionalVolumes | default "" | nindent 8 }}
        {{- end}}
  volumeClaimTemplates:
    - metadata:
        name: qdrant-storage
        labels:
          app: {{ template "qdrant.name" . }}
        {{- with .Values.persistence.annotations }}
        annotations:
        {{- toYaml . | nindent 10 }}
        {{- end }}
      spec:
        storageClassName: {{ .Values.persistence.storageClassName }}
        accessModes:
        {{- range .Values.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.persistence.size | quote }}
