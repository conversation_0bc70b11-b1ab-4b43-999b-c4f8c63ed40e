apiVersion: v1
kind: Service
metadata:
  name: {{ include "qdrant.fullname" . }}
  labels:
    {{- include "qdrant.labels" . | nindent 4 }}
{{- with .Values.service.additionalLabels }}
{{- toYaml . | nindent 4 }}
{{- end }}
{{- if .Values.service.annotations }}
  annotations:
{{ toYaml .Values.service.annotations | indent 4 }}
{{- end }}
spec:
  type: {{ .Values.service.type | default "ClusterIP" }}
  {{- if .Values.service.loadBalancerIP }}
    loadBalancerIP: { .Values.service.loadBalancerIP }}
  {{- end }}
  ports:
    {{- range .Values.service.ports }}
    - name: {{ .name }}
      port: {{ .port }}
      targetPort: {{ .targetPort }}
      protocol: {{ .protocol | default "TCP" }}
    {{- end }}
  selector:
    {{- include "qdrant.selectorLabels" . | nindent 4 }}
