{{- if .Values.podDisruptionBudget.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "qdrant.fullname" . }}
  labels:
    {{- include "qdrant.labels" . | nindent 4 }}
spec:
  {{- with  .Values.podDisruptionBudget.maxUnavailable}}
  maxUnavailable: {{ . }}
  {{- end }}
  {{- with  .Values.podDisruptionBudget.minAvailable }}
  minAvailable: {{ . }}
  {{- end }}
  {{- with  .Values.podDisruptionBudget.unhealthyPodEvictionPolicy }}
  unhealthyPodEvictionPolicy: {{ . }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "qdrant.selectorLabels" . | nindent 6 }}
{{- end }}
