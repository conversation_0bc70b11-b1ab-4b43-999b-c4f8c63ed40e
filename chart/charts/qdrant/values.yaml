replicaCount: 1

image:
  repository: docker.io/qdrant/qdrant
  pullPolicy: IfNotPresent
  tag: "v1.13.2"
  useUnprivilegedImage: false

imagePullSecrets: []
nameOverride: "qdrant"
fullnameOverride: "qdrant"
args: ["./config/initialize.sh"]
env: {}
  # - name: QDRANT_ALLOW_RECOVERY_MODE
  #   value: true

# checks - Readiness and liveness checks can only be enabled for either http (REST) or grpc (multiple checks not supported)
# grpc checks are only available from k8s 1.24+ so as of per default we check http
service:
  type: ClusterIP
  additionalLabels: {}
  annotations: {}
  loadBalancerIP: ""
  ports:
    - name: http
      port: 6333
      targetPort: 6333
      protocol: TCP
      checksEnabled: true
    - name: grpc
      port: 6334
      targetPort: 6334
      protocol: TCP
      checksEnabled: false
    - name: p2p
      port: 6335
      targetPort: 6335
      protocol: TCP
      checksEnabled: false

ingress:
  enabled: false
  ingressClassName: ""
  additionalLabels: {}
  annotations: {}
    # kubernetes.io/ingress.class: alb
  hosts:
    - host: example-domain.com
      paths:
        - path: /
          pathType: Prefix
          servicePort: 6333
  tls: []
    # - hosts:
    #    - example-domain.com
    #   secretName: tls-secret-name

livenessProbe:
  enabled: false
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 1
  failureThreshold: 6
  successThreshold: 1

readinessProbe:
  enabled: true
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 1
  failureThreshold: 6
  successThreshold: 1

startupProbe:
  enabled: false
  initialDelaySeconds: 10
  periodSeconds: 5
  timeoutSeconds: 1
  failureThreshold: 30
  successThreshold: 1

additionalLabels: {}
podAnnotations: {}
podLabels: {}

resources: {}
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

containerSecurityContext:
  runAsNonRoot: true
  runAsUser: 1000
  runAsGroup: 2000
  allowPrivilegeEscalation: false
  privileged: false
  readOnlyRootFilesystem: true

podSecurityContext:
  fsGroup: 3000
  fsGroupChangePolicy: Always

# If true ensures that the pre-existing files on the storage and snapshot volume are owned by the container's
# user and fsGroup
updateVolumeFsOwnership: true

nodeSelector: {}

tolerations: []

affinity: {}

topologySpreadConstraints: []

persistence:
  accessModes: ["ReadWriteOnce"]
  size: 20Gi
  annotations: {}
  # storageClassName: local-path

# only supported for single node qdrant clusters.
snapshotRestoration:
  enabled: false
  pvcName: snapshots-pvc
  snapshots:
  #  - /qdrant/snapshots/test_collection/test_collection-2022-10-24-13-56-50.snapshot:test_collection

# modification example for configuration to overwrite defaults
config:
  cluster:
    enabled: true
    p2p:
      port: 6335
    consensus:
      tick_period_ms: 100
  service:
    enable_tls: false

  storage:
    optimizers:
      # max_segment_size_kb: 65536
      memmap_threshold_kb: 65536
    wal:
      wal_capacity_mb: 64
      wal_segments_ahead: 1

sidecarContainers: []
# sidecarContainers:
#   - name: my-sidecar
#     image: qdrant/my-sidecar-image
#     imagePullPolicy: Always
#     ports:
#     - name: my-port
#       containerPort: 5000
#       protocol: TCP
#     resources:
#       requests:
#         memory: 10Mi
#         cpu: 10m
#       limits:
#         memory: 100Mi
#         cpu: 100m

updateConfigurationOnChange: false

metrics:
  serviceMonitor:
    enabled: false
    additionalLabels: {}
    scrapeInterval: 30s
    scrapeTimeout: 10s
    targetPort: http
    targetPath: "/metrics"
    ## MetricRelabelConfigs to apply to samples after scraping, but before ingestion.
    ## ref: https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/api.md#relabelconfig
    ##
    metricRelabelings: []
    ## RelabelConfigs to apply to samples before scraping
    ## ref: https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/api.md#relabelconfig
    ##
    relabelings: []

serviceAccount:
  annotations: {}

priorityClassName: ""

podDisruptionBudget:
  enabled: false
  maxUnavailable: 1
  # do not enable if you are using not in 1.27
  unhealthyPodEvictionPolicy: ""
  # minAvailable: 1

# api key for authentication at qdrant
# false: no api key will be configured
# true: an api key will be auto-generated
# string: the given string will be set as an apikey
apiKey: false

additionalVolumes: []
# - name: volumeName
#   emptyDir: {}

additionalVolumeMounts: []
# - name: volumeName
#   mountPath: "/mount/path"
