# Changelog

## 16.0.8 (2025-06-13)

* [bitnami/rabbitmq] :zap: :arrow_up: Update dependency references ([#34484](https://github.com/bitnami/charts/pull/34484))

## <small>16.0.7 (2025-06-09)</small>

* [bitnami/rabbitmq] :zap: :arrow_up: Update dependency references (#34276) ([8e7d44a](https://github.com/bitnami/charts/commit/8e7d44a8fd17b6ab8c32c904fcc34c4aa5b23375)), closes [#34276](https://github.com/bitnami/charts/issues/34276)

## <small>16.0.6 (2025-06-05)</small>

* [bitnami/rabbitmq] :zap: :arrow_up: Update dependency references (#34122) ([073e68a](https://github.com/bitnami/charts/commit/073e68ae186cf2844d608663f413b37c6732d830)), closes [#34122](https://github.com/bitnami/charts/issues/34122)

## <small>16.0.5 (2025-06-02)</small>

* [bitnami/rabbitmq] :zap: :arrow_up: Update dependency references (#34031) ([8e3fe0b](https://github.com/bitnami/charts/commit/8e3fe0b70144eb8c11df19e525c53d1b63c0c52c)), closes [#34031](https://github.com/bitnami/charts/issues/34031)

## <small>16.0.4 (2025-05-30)</small>

* [bitnami/rabbitmq] Update dependencies (#33985) ([f882b31](https://github.com/bitnami/charts/commit/f882b31ca02acff4767a3ee142edce535057d90c)), closes [#33985](https://github.com/bitnami/charts/issues/33985)

## <small>16.0.3 (2025-05-23)</small>

* [bitnami/*] Add CNAB link for charts on Azure MP (#33695) ([6312371](https://github.com/bitnami/charts/commit/63123718de94dbedd798d380807b57031e98ed4f)), closes [#33695](https://github.com/bitnami/charts/issues/33695)
* [bitnami/*] Update CNAB tip (#33741) ([2bc74f3](https://github.com/bitnami/charts/commit/2bc74f3f539481ceaa12833c114047583912b748)), closes [#33741](https://github.com/bitnami/charts/issues/33741)
* [bitnami/kubeapps] Deprecation followup (#33579) ([77e312c](https://github.com/bitnami/charts/commit/77e312c1772d4d7c4dc5d3ac0e80f4e452e3a062)), closes [#33579](https://github.com/bitnami/charts/issues/33579)
* [bitnami/rabbitmq] :zap: :arrow_up: Update dependency references (#33860) ([fd7de7d](https://github.com/bitnami/charts/commit/fd7de7d00b67501ed1a50d8abf1b725c234cbd59)), closes [#33860](https://github.com/bitnami/charts/issues/33860)

## <small>16.0.2 (2025-05-06)</small>

* [bitnami/rabbitmq] chore: :recycle: :arrow_up: Update common and remove k8s < 1.23 references (#3342 ([9ca5aa5](https://github.com/bitnami/charts/commit/9ca5aa5e3312617ce24d85efe33cc1d696495a25)), closes [#33426](https://github.com/bitnami/charts/issues/33426)
* [bitnami/rabbitmq] fixed validation for memoryHighWatermark using resourcesPreset (#33186) ([c864596](https://github.com/bitnami/charts/commit/c86459615a6a5bf186b08e5c70cf7d9b74dbfbea)), closes [#33186](https://github.com/bitnami/charts/issues/33186)

## 16.0.0 (2025-04-23)

* [bitnami/rabbitmq] Release 16.0.0 (#33138) ([7ba0ac5](https://github.com/bitnami/charts/commit/7ba0ac515175806c1617f5509d18e395cc4ddb6b)), closes [#33138](https://github.com/bitnami/charts/issues/33138)

## <small>15.5.3 (2025-04-14)</small>

* [bitnami/rabbitmq] Release 15.5.3 (#32997) ([d3ea559](https://github.com/bitnami/charts/commit/d3ea559a3f698dd1aee57d179598cd59d31ea260)), closes [#32997](https://github.com/bitnami/charts/issues/32997)

## <small>15.5.2 (2025-04-14)</small>

* [bitnami/rabbitmq] Release 15.5.2 (#32995) ([b9930e0](https://github.com/bitnami/charts/commit/b9930e0f17873bea04e5bceae164ac954bf00004)), closes [#32995](https://github.com/bitnami/charts/issues/32995)

## <small>15.5.1 (2025-04-09)</small>

* [bitnami/rabbitmq] Release 15.5.1 (#32894) ([ab383a2](https://github.com/bitnami/charts/commit/ab383a2f6ec2e7c14a1b57e73860fbe086b982bb)), closes [#32894](https://github.com/bitnami/charts/issues/32894)

## 15.5.0 (2025-04-04)

* [bitnami/rabbitmq] Set `usePasswordFiles=true` by default (#32768) ([4a88674](https://github.com/bitnami/charts/commit/4a886740c6abb96f510eb6736de03737e60e8835)), closes [#32768](https://github.com/bitnami/charts/issues/32768)

## <small>15.4.2 (2025-04-03)</small>

* [bitnami/rabbitmq] Release 15.4.2 (#32793) ([b6e252b](https://github.com/bitnami/charts/commit/b6e252bd85d43b7686ff0c9b525b91563e557af8)), closes [#32793](https://github.com/bitnami/charts/issues/32793)

## <small>15.4.1 (2025-03-28)</small>

* [bitnami/rabbitmq] Release 15.4.1 (#32679) ([b941af0](https://github.com/bitnami/charts/commit/b941af0e5e720e5bbd7c34cf16f3c65ca5e6be50)), closes [#32679](https://github.com/bitnami/charts/issues/32679)

## 15.4.0 (2025-03-19)

* [bitnami/*] Add tanzuCategory annotation (#32409) ([a8fba5c](https://github.com/bitnami/charts/commit/a8fba5cb01f6f4464ca7f69c50b0fbe97d837a95)), closes [#32409](https://github.com/bitnami/charts/issues/32409)
* [bitnami/rabbitmq] allow to configure trafficDistribution (#32443) ([49e8182](https://github.com/bitnami/charts/commit/49e81822b9ebe29f544f6276d367489cdb0c121d)), closes [#32443](https://github.com/bitnami/charts/issues/32443)

## <small>15.3.3 (2025-02-26)</small>

* [bitnami/rabbitmq] Release 15.3.3 (#32187) ([e4af5c8](https://github.com/bitnami/charts/commit/e4af5c8b3510c166a38cd5bc03074a39bf63da4d)), closes [#32187](https://github.com/bitnami/charts/issues/32187)

## <small>15.3.2 (2025-02-21)</small>

* [bitnami/*] Use CDN url for the Bitnami Application Icons (#31881) ([d9bb11a](https://github.com/bitnami/charts/commit/d9bb11a9076b9bfdcc70ea022c25ef50e9713657)), closes [#31881](https://github.com/bitnami/charts/issues/31881)
* [bitnami/rabbitmq] Release 15.3.2 (#32101) ([6779add](https://github.com/bitnami/charts/commit/6779add4a0aeb64feb6e53d8b7123e94b367e017)), closes [#32101](https://github.com/bitnami/charts/issues/32101)

## <small>15.3.1 (2025-02-11)</small>

* [bitnami/rabbitmq] Release 15.3.1 (#31873) ([6857694](https://github.com/bitnami/charts/commit/68576941a396c697cd98b74f6fece6201b18f41e)), closes [#31873](https://github.com/bitnami/charts/issues/31873)

## 15.3.0 (2025-02-10)

* [bitnami/rabbitmq]: add hostPort support to the chart (#31836) ([5bcbebf](https://github.com/bitnami/charts/commit/5bcbebf3d6c35048bf2dad13abecbdf57744a928)), closes [#31836](https://github.com/bitnami/charts/issues/31836)

## <small>15.2.5 (2025-02-03)</small>

* [bitnami/rabbitmq] Release 15.2.5 (#31684) ([9f2b108](https://github.com/bitnami/charts/commit/9f2b108e450d4bc837335432333fde78d946c031)), closes [#31684](https://github.com/bitnami/charts/issues/31684)
* Update copyright year (#31682) ([e9f02f5](https://github.com/bitnami/charts/commit/e9f02f5007068751f7eb2270fece811e685c99b6)), closes [#31682](https://github.com/bitnami/charts/issues/31682)

## <small>15.2.4 (2025-01-25)</small>

* [bitnami/rabbitmq] Release 15.2.4 (#31593) ([30b43f3](https://github.com/bitnami/charts/commit/30b43f32ddb93639ae7a714c7a9cbd686280d302)), closes [#31593](https://github.com/bitnami/charts/issues/31593)

## <small>15.2.3 (2025-01-15)</small>

* [bitnami/rabbitmq] Fix incorrect configuration of TCP listen options by memoryHighWatermark settings ([7432a31](https://github.com/bitnami/charts/commit/7432a317f4126ac2ca2b3a56e731d483a92b752a)), closes [#31336](https://github.com/bitnami/charts/issues/31336)

## <small>15.2.2 (2025-01-07)</small>

* [bitnami/*] Fix typo in README (#31052) ([b41a51d](https://github.com/bitnami/charts/commit/b41a51d1bd04841fc108b78d3b8357a5292771c8)), closes [#31052](https://github.com/bitnami/charts/issues/31052)
* [bitnami/rabbitmq] remove undefined rts key from network policy template (#31210) ([1e98bac](https://github.com/bitnami/charts/commit/1e98bacbb4153a6a57690d1cc96b7f76e66c6abe)), closes [#31210](https://github.com/bitnami/charts/issues/31210)

## <small>15.2.1 (2024-12-16)</small>

* [bitnami/rabbitmq] Release 15.2.1 (#31049) ([973da81](https://github.com/bitnami/charts/commit/973da81d773b2b834c83e8b7b90553d915899bc5)), closes [#31049](https://github.com/bitnami/charts/issues/31049)

## 15.2.0 (2024-12-10)

* [bitnami/*] Add Bitnami Premium to NOTES.txt (#30854) ([3dfc003](https://github.com/bitnami/charts/commit/3dfc00376df6631f0ce54b8d440d477f6caa6186)), closes [#30854](https://github.com/bitnami/charts/issues/30854)
* [bitnami/*] docs: :memo: Add "Backup & Restore" section (#30711) ([35ab536](https://github.com/bitnami/charts/commit/35ab5363741e7548f4076f04da6e62d10153c60c)), closes [#30711](https://github.com/bitnami/charts/issues/30711)
* [bitnami/*] docs: :memo: Add "Prometheus metrics" (batch 5) (#30674) ([ed2a546](https://github.com/bitnami/charts/commit/ed2a54617faf763169e6b01a89100b9db32e1000)), closes [#30674](https://github.com/bitnami/charts/issues/30674)
* [bitnami/*] docs: :memo: Unify "Securing Traffic using TLS" section (#30707) ([b572333](https://github.com/bitnami/charts/commit/b57233336e4fe9af928ecb4f2a5f334011efb1bc)), closes [#30707](https://github.com/bitnami/charts/issues/30707)
* [bitnami/rabbitmq] Detect non-standard images (#30940) ([4dade21](https://github.com/bitnami/charts/commit/4dade214ff39b6741a9f1c1239c51a4ab13c1a4d)), closes [#30940](https://github.com/bitnami/charts/issues/30940)

## 15.1.0 (2024-11-25)

* [bitnami/rabbitmq] feat: :sparkles: Allow password updates (#30615) ([d98aecc](https://github.com/bitnami/charts/commit/d98aecce08aebd795cdace9bc1f814296a9aa873)), closes [#30615](https://github.com/bitnami/charts/issues/30615)

## <small>15.0.7 (2024-11-22)</small>

* [bitnami/rabbitmq] Release 15.0.7 (#30572) ([212993b](https://github.com/bitnami/charts/commit/212993b0c06a0f0a3ccafdc8c253fe260b525b73)), closes [#30572](https://github.com/bitnami/charts/issues/30572)

## <small>15.0.6 (2024-11-08)</small>

* [bitnami/rabbitmq] Unify seLinuxOptions default value (#30327) ([9537964](https://github.com/bitnami/charts/commit/95379640c83645c2b37e8cebc18823cf5984308b)), closes [#30327](https://github.com/bitnami/charts/issues/30327)

## <small>15.0.5 (2024-10-31)</small>

* [bitnami/rabbitmq] Release 15.0.5 (#30152) ([2dba9cb](https://github.com/bitnami/charts/commit/2dba9cb5e390c6bef2a0d5ee9b4a401cffea0070)), closes [#30152](https://github.com/bitnami/charts/issues/30152)

## <small>15.0.4 (2024-10-29)</small>

* [bitnami/*] Remove wrong comment about imagePullPolicy (#30107) ([a51f9e4](https://github.com/bitnami/charts/commit/a51f9e4bb0fbf77199512d35de7ac8abe055d026)), closes [#30107](https://github.com/bitnami/charts/issues/30107)
* [bitnami/rabbitmq] Allow missing memory limit when memoryHighWatermark is absolute (#30079) ([b92af12](https://github.com/bitnami/charts/commit/b92af12ad55e92c8b1377987b222090787d23b02)), closes [#30079](https://github.com/bitnami/charts/issues/30079)
* Update documentation links to techdocs.broadcom.com (#29931) ([f0d9ad7](https://github.com/bitnami/charts/commit/f0d9ad78f39f633d275fc576d32eae78ded4d0b8)), closes [#29931](https://github.com/bitnami/charts/issues/29931)

## <small>15.0.3 (2024-10-14)</small>

* [bitnami/rabbitmq] Use common password/secrets manager to handle credentials (#29879) ([456bfb2](https://github.com/bitnami/charts/commit/456bfb2a2762fb78e96ae09d55d82cfaacc74d45)), closes [#29879](https://github.com/bitnami/charts/issues/29879)

## <small>15.0.2 (2024-10-07)</small>

* [Bitnami/rabbitmq] - Fix duplicate key issue with Helm/FluxCD integration (#29780) ([cea9e6c](https://github.com/bitnami/charts/commit/cea9e6c9ea4b97b5ec48c5156d8d6c9fcd1ded13)), closes [#29780](https://github.com/bitnami/charts/issues/29780)

## <small>15.0.1 (2024-09-25)</small>

* [bitnami/rabbitmq] docs: :memo: Add upgrade notes for version 15.x.x ([012685d](https://github.com/bitnami/charts/commit/012685db9831eefe1a77be9122fbcdf5933198a1))
* [bitnami/rabbitmq] Release 15.0.1 (#29600) ([f0bcc5c](https://github.com/bitnami/charts/commit/f0bcc5cb258b5329bcb6cacd998d4f4170d3b06a)), closes [#29600](https://github.com/bitnami/charts/issues/29600)

## 15.0.0 (2024-09-20)

* [bitnami/rabbitmq] Release 15.0.0 (#29555) ([595df29](https://github.com/bitnami/charts/commit/595df29e55c616de44a5f0794738bf7520e6eb58)), closes [#29555](https://github.com/bitnami/charts/issues/29555)

## 14.7.0 (2024-09-12)

* [bitnami/rabbitmq] Breaking out RabbitMQ metrics endpoints to configure separately (#28891) ([3b9cfe9](https://github.com/bitnami/charts/commit/3b9cfe99e23c485ea95bbeb128dc44961374d434)), closes [#28891](https://github.com/bitnami/charts/issues/28891)

## <small>14.6.10 (2024-09-11)</small>

* [bitnami/rabbitmq] Allow rendering resources values (#29347) ([7479317](https://github.com/bitnami/charts/commit/747931744d2242abe11ff29ea8f499ed292e731a)), closes [#29347](https://github.com/bitnami/charts/issues/29347)

## <small>14.6.9 (2024-08-28)</small>

* [bitnami/rabbitmq] Release 14.6.9 (#29092) ([711e2f7](https://github.com/bitnami/charts/commit/711e2f720d92e936b9a67bbdc269d57c33a9dca6)), closes [#29092](https://github.com/bitnami/charts/issues/29092)

## <small>14.6.8 (2024-08-28)</small>

* [bitnami/rabbitmq] Improve Ginkgo test (#29082) ([778a269](https://github.com/bitnami/charts/commit/778a269c23fd9e6534c3c15e85f016c38f498e81)), closes [#29082](https://github.com/bitnami/charts/issues/29082)

## <small>14.6.7 (2024-08-26)</small>

* [bitnami/rabbitmq] Release 14.6.7 (#29018) ([8eb9085](https://github.com/bitnami/charts/commit/8eb9085b47699e839bae9f0238a775c1d0b0dfa5)), closes [#29018](https://github.com/bitnami/charts/issues/29018)

## <small>14.6.6 (2024-08-08)</small>

* [bitnami/rabbitmq] Fix rabbitmq pods not being restarted after changing init scripts (#27900) ([95148a9](https://github.com/bitnami/charts/commit/95148a9ae1363f9da6415b64085aadfd8add1413)), closes [#27900](https://github.com/bitnami/charts/issues/27900)

## <small>14.6.5 (2024-07-25)</small>

* [bitnami/rabbitmq] Release 14.6.5 (#28473) ([40abb28](https://github.com/bitnami/charts/commit/40abb284adcd42c0c27190899e9e3a115dbf46e8)), closes [#28473](https://github.com/bitnami/charts/issues/28473)

## <small>14.6.4 (2024-07-24)</small>

* [bitnami/rabbitmq] Release 14.6.4 (#28361) ([ab8d156](https://github.com/bitnami/charts/commit/ab8d1562d3da46daeca8f0a70119b57aa5f402c5)), closes [#28361](https://github.com/bitnami/charts/issues/28361)

## <small>14.6.3 (2024-07-23)</small>

* [bitnami/rabbitmq] Release 14.6.3 (#28204) ([5a213c4](https://github.com/bitnami/charts/commit/5a213c4352403949d91fb28f30361bb676121dc3)), closes [#28204](https://github.com/bitnami/charts/issues/28204)

## <small>14.6.2 (2024-07-22)</small>

* [bitnami/rabbitmq] Release 14.6.2 (#28192) ([69ced0b](https://github.com/bitnami/charts/commit/69ced0b8f41f3a3ebeeb595df864880a79cbdff2)), closes [#28192](https://github.com/bitnami/charts/issues/28192)

## <small>14.6.1 (2024-07-18)</small>

* [bitnami/rabbitmq] Global StorageClass as default value (#28087) ([a4143c5](https://github.com/bitnami/charts/commit/a4143c5e397df6a5124381f7531d2d508f526221)), closes [#28087](https://github.com/bitnami/charts/issues/28087)

## 14.6.0 (2024-07-17)

* [bitnami/rabbitmq] make queue_master_locator configurable (#27990) ([7266748](https://github.com/bitnami/charts/commit/726674875655c7f90ccd17370950c858f951a96a)), closes [#27990](https://github.com/bitnami/charts/issues/27990)

## 14.5.0 (2024-07-09)

* [bitnami/rabbitmq] Replace extraPorts with extraPortsHeadless in headless service (#27853) ([b205f7a](https://github.com/bitnami/charts/commit/b205f7a39c528e0fe365568f955c25c460649c4f)), closes [#27853](https://github.com/bitnami/charts/issues/27853)

## <small>14.4.6 (2024-07-03)</small>

* [bitnami/rabbitmq] Release 14.4.6 (#27739) ([891935d](https://github.com/bitnami/charts/commit/891935dae482369fc75a9be9cdf13e9a148fdb3f)), closes [#27739](https://github.com/bitnami/charts/issues/27739)

## <small>14.4.5 (2024-07-03)</small>

* [bitnami/*] Update README changing TAC wording (#27530) ([52dfed6](https://github.com/bitnami/charts/commit/52dfed6bac44d791efabfaf06f15daddc4fefb0c)), closes [#27530](https://github.com/bitnami/charts/issues/27530)
* [bitnami/rabbitmq] Release 14.4.5 (#27713) ([c54e941](https://github.com/bitnami/charts/commit/c54e941e790295203771c7574c907c72a00aef80)), closes [#27713](https://github.com/bitnami/charts/issues/27713)

## <small>14.4.4 (2024-06-18)</small>

* [bitnami/rabbitmq] Release 14.4.4 (#27409) ([e809d66](https://github.com/bitnami/charts/commit/e809d66db6e5bbd2d90fdb7fce9b67d2fb9cf64d)), closes [#27409](https://github.com/bitnami/charts/issues/27409)

## <small>14.4.3 (2024-06-17)</small>

* [bitnami/rabbitmq] Release 14.4.3 (#27276) ([7fc3346](https://github.com/bitnami/charts/commit/7fc3346f8835f684a1e87c763e9f37c69834eba2)), closes [#27276](https://github.com/bitnami/charts/issues/27276)

## <small>14.4.2 (2024-06-13)</small>

* [bitnami/rabbitmq] Align ulimitNofiles default value on containerd runc runtime default ulimit -n va ([34a4923](https://github.com/bitnami/charts/commit/34a4923799d19456818466a6ca3f42b207071868)), closes [#27052](https://github.com/bitnami/charts/issues/27052)
* RabbitMQ: explain how to safely avoid a deployment deadlock (#25931) ([3117b57](https://github.com/bitnami/charts/commit/3117b573147401387321a2a0e1f587978cef0ed2)), closes [#25931](https://github.com/bitnami/charts/issues/25931)

## <small>14.4.1 (2024-06-06)</small>

* [bitnami/rabbitmq] Release 14.4.1 (#27010) ([1769eb5](https://github.com/bitnami/charts/commit/1769eb5722a47299d4e5b1051e5ad3826861d9ff)), closes [#27010](https://github.com/bitnami/charts/issues/27010)

## 14.4.0 (2024-06-06)

* [bitnami/rabbitmq] Enable PodDisruptionBudgets (#26222) ([afa165f](https://github.com/bitnami/charts/commit/afa165feb72347547666eabe57c81d21c2d092f9)), closes [#26222](https://github.com/bitnami/charts/issues/26222)

## <small>14.3.3 (2024-06-04)</small>

* [bitnami/rabbitmq] Bump chart version (#26664) ([95ffecb](https://github.com/bitnami/charts/commit/95ffecbeb93eae95ead8a2c67a77776ba8c9c87d)), closes [#26664](https://github.com/bitnami/charts/issues/26664)

## <small>14.3.2 (2024-06-01)</small>

* [bitnami/rabbitmq] Release 14.3.2 (#26589) ([9c11377](https://github.com/bitnami/charts/commit/9c113773f3b8120f62b683cab62fc4ca75f89484)), closes [#26589](https://github.com/bitnami/charts/issues/26589)

## <small>14.3.1 (2024-05-27)</small>

* fix: pass service.extraPorts to the headless service (#26136) ([346eddf](https://github.com/bitnami/charts/commit/346eddfa97253f7a390a68e05ea78c2f604f0b65)), closes [#26136](https://github.com/bitnami/charts/issues/26136)

## 14.3.0 (2024-05-22)

* [bitnami/rabbitmq] Network policy review (#25900) ([4e28b72](https://github.com/bitnami/charts/commit/4e28b72cff6cb8ec15b81cf6b32446b56736bb5b)), closes [#25900](https://github.com/bitnami/charts/issues/25900) [#25519](https://github.com/bitnami/charts/issues/25519)

## 14.2.0 (2024-05-21)

* [bitnami/*] ci: :construction_worker: Add tag and changelog support (#25359) ([91c707c](https://github.com/bitnami/charts/commit/91c707c9e4e574725a09505d2d313fb93f1b4c0a)), closes [#25359](https://github.com/bitnami/charts/issues/25359)
* [bitnami/rabbitmq] feat: :sparkles: :lock: Add warning when original images are replaced (#26269) ([17516c1](https://github.com/bitnami/charts/commit/17516c15a92435ba6f2aa2ce6254f0717dfe5b8b)), closes [#26269](https://github.com/bitnami/charts/issues/26269)

## <small>14.1.5 (2024-05-18)</small>

* [bitnami/rabbitmq] Release 14.1.5 updating components versions (#26071) ([e2f55cb](https://github.com/bitnami/charts/commit/e2f55cbf30c6c5c8cd7ddb2e34d9896755f8c143)), closes [#26071](https://github.com/bitnami/charts/issues/26071)

## <small>14.1.4 (2024-05-14)</small>

* [bitnami/rabbitmq] Release 14.1.4 updating components versions (#25817) ([1f6861b](https://github.com/bitnami/charts/commit/1f6861bce4398a7db69b041e5fd3e5f5d014e467)), closes [#25817](https://github.com/bitnami/charts/issues/25817)

## <small>14.1.3 (2024-05-13)</small>

* [bitnami/*] Change non-root and rolling-tags doc URLs (#25628) ([b067c94](https://github.com/bitnami/charts/commit/b067c94f6bcde427863c197fd355f0b5ba12ff5b)), closes [#25628](https://github.com/bitnami/charts/issues/25628)
* [bitnami/*] Set new header/owner (#25558) ([8d1dc11](https://github.com/bitnami/charts/commit/8d1dc11f5fb30db6fba50c43d7af59d2f79deed3)), closes [#25558](https://github.com/bitnami/charts/issues/25558)
* [bitnami/rabbitmq] Release 14.1.3 updating components versions (#25713) ([57b655e](https://github.com/bitnami/charts/commit/57b655e390635da8be03b7e88f2c573d1edb1dfa)), closes [#25713](https://github.com/bitnami/charts/issues/25713)

## <small>14.1.2 (2024-05-06)</small>

* [bitnami/rabbitmq] Remove unicode characters (#25548) ([adb080c](https://github.com/bitnami/charts/commit/adb080cb6582bd771ba28fae89bbf5c509f8c3bb)), closes [#25548](https://github.com/bitnami/charts/issues/25548)

## <small>14.1.1 (2024-05-01)</small>

* [bitnami/rabbitmq] Release 14.1.1 updating components versions (#25480) ([0ea1ef5](https://github.com/bitnami/charts/commit/0ea1ef5aa23730823b19ee63ebf641f4500add0f)), closes [#25480](https://github.com/bitnami/charts/issues/25480)

## 14.1.0 (2024-04-30)

* [bitnami/multiple charts] Fix typo: "NetworkPolice" vs "NetworkPolicy" (#25348) ([6970c1b](https://github.com/bitnami/charts/commit/6970c1ba245873506e73d459c6eac1e4919b778f)), closes [#25348](https://github.com/bitnami/charts/issues/25348)
* [bitnami/rabbitmq] Add rbac.rules (#25442) ([d4f4745](https://github.com/bitnami/charts/commit/d4f47451aa76db57d9648a00ebf2e4a02f611a05)), closes [#25442](https://github.com/bitnami/charts/issues/25442)
* Replace VMware by Broadcom copyright text (#25306) ([a5e4bd0](https://github.com/bitnami/charts/commit/a5e4bd0e35e419203793976a78d9d0a13de92c76)), closes [#25306](https://github.com/bitnami/charts/issues/25306)

## <small>14.0.2 (2024-04-23)</small>

* [bitnami/rabbitmq]: Fix custom plugins installation (#25316) ([a3c7c6e](https://github.com/bitnami/charts/commit/a3c7c6e5bc685b2587a6302770e20c6890ebd72d)), closes [#25316](https://github.com/bitnami/charts/issues/25316)

## <small>14.0.1 (2024-04-15)</small>

* [bitnami/rabbitmq] Add upgrade notes for version 3.13.x (#25095) ([7d3c0c6](https://github.com/bitnami/charts/commit/7d3c0c610062d5eceb01786027a1643e964efb34)), closes [#25095](https://github.com/bitnami/charts/issues/25095)
* [bitnami/rabbitmq] fix: :bug: :lock: Expose missing ports in deployment spec (#25135) ([103b903](https://github.com/bitnami/charts/commit/103b903a1bc7014e518c3d1df0d4f521632667a3)), closes [#25135](https://github.com/bitnami/charts/issues/25135)

## 14.0.0 (2024-04-10)

* [bitnami/rabbitmq] Release 14.0.0 updating components versions (#25093) ([51f843e](https://github.com/bitnami/charts/commit/51f843e58a1f3f5a3bb12dcd173128b518407ec5)), closes [#25093](https://github.com/bitnami/charts/issues/25093)

## <small>13.0.3 (2024-04-05)</small>

* [bitnami/rabbitmq] Fix misleading memoryHighWatermark.value description in values (#24866) ([50dd177](https://github.com/bitnami/charts/commit/50dd17797910b5751a9a4993a9e5fad83c128cab)), closes [#24866](https://github.com/bitnami/charts/issues/24866)
* Update resourcesPreset comments (#24467) ([92e3e8a](https://github.com/bitnami/charts/commit/92e3e8a507326d2a20a8f10ab3e7746a2ec5c554)), closes [#24467](https://github.com/bitnami/charts/issues/24467)

## <small>13.0.2 (2024-04-02)</small>

* [bitnami/rabbitmq] Release 13.0.2 updating components versions (#24805) ([88edbf0](https://github.com/bitnami/charts/commit/88edbf0c42ef8b40654def9bc30bb4258919fe81)), closes [#24805](https://github.com/bitnami/charts/issues/24805)

## <small>13.0.1 (2024-04-02)</small>

* [bitnami/*] Reorder Chart sections (#24455) ([0cf4048](https://github.com/bitnami/charts/commit/0cf4048e8743f70a9753d460655bd030cbff6824)), closes [#24455](https://github.com/bitnami/charts/issues/24455)
* Fix invalid secret pointer in RabbitMQ ServiceAccount (#24750) ([d85830c](https://github.com/bitnami/charts/commit/d85830ca1aa43439c9f086bdfaf5a9390cdbc60d)), closes [#24750](https://github.com/bitnami/charts/issues/24750) [#24738](https://github.com/bitnami/charts/issues/24738)

## 13.0.0 (2024-03-15)

* [bitnami/rabbitmq] docs: :memo: Add upgrade notes for 12.10.0 (#24322) ([65e3aad](https://github.com/bitnami/charts/commit/65e3aad31fe90c2024611e888741c094cad86557)), closes [#24322](https://github.com/bitnami/charts/issues/24322)
* [bitnami/rabbitmq] feat!: 🔒 💥 Improve security defaults (#24336) ([54e671f](https://github.com/bitnami/charts/commit/54e671f09d6153ac23a6d96e576ea0e35148809a)), closes [#24336](https://github.com/bitnami/charts/issues/24336)

## 12.15.0 (2024-03-06)

* [bitnami/rabbitmq] feat: :sparkles: :lock: Add automatic adaptation for Openshift restricted-v2 SCC  ([63a0cc3](https://github.com/bitnami/charts/commit/63a0cc3dbb16869516a31e1139a2049a8d927449)), closes [#24146](https://github.com/bitnami/charts/issues/24146)

## <small>12.14.1 (2024-03-05)</small>

* [bitnami/rabbitmq] fix error dereferencing nil resources (#23616) ([f75c6a8](https://github.com/bitnami/charts/commit/f75c6a8f5900de9600a9b31ca7886bf2d388b564)), closes [#23616](https://github.com/bitnami/charts/issues/23616) [#23607](https://github.com/bitnami/charts/issues/23607)

## 12.14.0 (2024-02-23)

* [bitnami/rabbitmq] feat: :sparkles: :lock: Add readOnlyRootFilesystem support (#23745) ([68a606e](https://github.com/bitnami/charts/commit/68a606e1283b35a268f17d8f9bf10f74b3d6bccc)), closes [#23745](https://github.com/bitnami/charts/issues/23745)

## <small>12.13.2 (2024-02-22)</small>

* [bitnami/rabbitmq] Release 12.13.2 updating components versions (#23825) ([5dbad69](https://github.com/bitnami/charts/commit/5dbad692f9a7117b15986da184e870b31775b4f4)), closes [#23825](https://github.com/bitnami/charts/issues/23825)

## <small>12.13.1 (2024-02-21)</small>

* [bitnami/rabbitmq] Release 12.13.1 updating components versions (#23691) ([1bf88ab](https://github.com/bitnami/charts/commit/1bf88ab5d09b6b1be29995b955b39431259f8de4)), closes [#23691](https://github.com/bitnami/charts/issues/23691)

## 12.13.0 (2024-02-20)

* [bitnami/*] Bump all versions (#23602) ([b70ee2a](https://github.com/bitnami/charts/commit/b70ee2a30e4dc256bf0ac52928fb2fa7a70f049b)), closes [#23602](https://github.com/bitnami/charts/issues/23602)

## 12.12.0 (2024-02-19)

* [bitnami/rabbitmq] Allow specifying service loadBalancerClass (#23561) ([5e8cda2](https://github.com/bitnami/charts/commit/5e8cda22610d48a70783f296d52c6b5717207298)), closes [#23561](https://github.com/bitnami/charts/issues/23561)

## 12.11.0 (2024-02-15)

* [bitnami/rabbitmq] feat: :sparkles: :lock: Add resource preset support (#23514) ([b9339c7](https://github.com/bitnami/charts/commit/b9339c73fbff437da149410a2a50209b69b507ec)), closes [#23514](https://github.com/bitnami/charts/issues/23514)

## 12.10.0 (2024-02-07)

* [bitnami/rabbitmq] fix: :bug: Add allowExternalEgress to avoid breaking istio (#22972) ([b525212](https://github.com/bitnami/charts/commit/b525212eaefd879b9eb5136c511945f5a4b8bc82)), closes [#22972](https://github.com/bitnami/charts/issues/22972)

## <small>12.9.4 (2024-02-06)</small>

* [bitnami/rabbitmq] fix: make toBytes handle numbers with a decimal dot (#22557) ([90f6e7f](https://github.com/bitnami/charts/commit/90f6e7f3e6c341fd11cd04874be33e606e56fa48)), closes [#22557](https://github.com/bitnami/charts/issues/22557)

## <small>12.9.3 (2024-02-03)</small>

* [bitnami/rabbitmq] Release 12.9.3 updating components versions (#23134) ([8b2dd63](https://github.com/bitnami/charts/commit/8b2dd6354387aa8689c2441311b7ddba1d48fa16)), closes [#23134](https://github.com/bitnami/charts/issues/23134)

## <small>12.9.2 (2024-02-01)</small>

* [bitnami/rabbitmq] Fix variable naming in helpers template file (#22924) ([d828506](https://github.com/bitnami/charts/commit/d828506f0b9c7874d3a5137f431a72627807df08)), closes [#22924](https://github.com/bitnami/charts/issues/22924)

## <small>12.9.1 (2024-02-01)</small>

* [bitnami/rabbitmq] Release 12.9.1 updating components versions (#22998) ([47d3748](https://github.com/bitnami/charts/commit/47d3748a58316f61fdee7be71b6b3dd46c8e6d96)), closes [#22998](https://github.com/bitnami/charts/issues/22998)

## 12.9.0 (2024-01-30)

* [bitnami/rabbitmq] feat: :lock: Enable networkPolicy (#22741) ([a5a6c6c](https://github.com/bitnami/charts/commit/a5a6c6c6afeb8b9d89874da167447ad0341b9358)), closes [#22741](https://github.com/bitnami/charts/issues/22741)

## <small>12.8.2 (2024-01-29)</small>

* [bitnami/rabbitmq] Disable memoryHighWatermark by default in RabbitMQ (#22818) ([962aec3](https://github.com/bitnami/charts/commit/962aec32690f2dc4224cadbd64d213de1f2bbd2f)), closes [#22818](https://github.com/bitnami/charts/issues/22818)

## <small>12.8.1 (2024-01-26)</small>

* [bitnami/*] Move documentation sections from docs.bitnami.com back to the README (#22203) ([7564f36](https://github.com/bitnami/charts/commit/7564f36ca1e95ff30ee686652b7ab8690561a707)), closes [#22203](https://github.com/bitnami/charts/issues/22203)
* [bitnami/rabbitmq] fix: :bug: Set seLinuxOptions to null for Openshift compatibility (#22651) ([92239a1](https://github.com/bitnami/charts/commit/92239a1a1f5d139e56109f47b9b36cf8b2c636cb)), closes [#22651](https://github.com/bitnami/charts/issues/22651)
* #21359 Added erlang secret key for external sealed secrets (#22016) ([6154b1d](https://github.com/bitnami/charts/commit/6154b1dca3551d4afdbc38a63282ae1a75e0e88d)), closes [#21359](https://github.com/bitnami/charts/issues/21359) [#22016](https://github.com/bitnami/charts/issues/22016)

## 12.8.0 (2024-01-19)

* [bitnami/rabbitmq] fix: :lock: Move service-account token auto-mount to pod declaration (#22453) ([0f92db5](https://github.com/bitnami/charts/commit/0f92db5dd4f9a95f7dd12e4044a42d99bf81f9b1)), closes [#22453](https://github.com/bitnami/charts/issues/22453)

## <small>12.7.1 (2024-01-17)</small>

* [bitnami/rabbitmq] fix: :lock: Improve podSecurityContext and containerSecurityContext with essentia ([4dab813](https://github.com/bitnami/charts/commit/4dab813c3a63a9c7ddaa8b7cae7e400cc558ced5)), closes [#22182](https://github.com/bitnami/charts/issues/22182)
* [bitnami/rabbitmq] Release 12.7.1 updating components versions (#22328) ([f153cd4](https://github.com/bitnami/charts/commit/f153cd489c75734ca6322672f77c0b5ee7e2d6f3)), closes [#22328](https://github.com/bitnami/charts/issues/22328)

## 12.7.0 (2024-01-16)

* [bitnami/rabbitmq] Add auth.existingSecret to support external sealed secrets (#21891) ([950dc61](https://github.com/bitnami/charts/commit/950dc610e5c62eb1500de507887b78655d50a577)), closes [#21891](https://github.com/bitnami/charts/issues/21891)

## <small>12.6.3 (2024-01-12)</small>

* [bitnami/*] Fix docs.bitnami.com broken links (#21901) ([f35506d](https://github.com/bitnami/charts/commit/f35506d2dadee4f097986e7792df1f53ab215b5d)), closes [#21901](https://github.com/bitnami/charts/issues/21901)
* [bitnami/*] Fix ref links (in comments) (#21822) ([e4fa296](https://github.com/bitnami/charts/commit/e4fa296106b225cf8c82445727c675c7c725e380)), closes [#21822](https://github.com/bitnami/charts/issues/21822)
* Fix rendering of vm_memory_high_watermark param (#22017) ([f12f2b2](https://github.com/bitnami/charts/commit/f12f2b229c27e43ad9c7bc6a369934fbe10a1558)), closes [#22017](https://github.com/bitnami/charts/issues/22017)

## <small>12.6.2 (2024-01-05)</small>

* [bitnami/*] Update copyright: Year and company (#21815) ([6c4bf75](https://github.com/bitnami/charts/commit/6c4bf75dec58fc7c9aee9f089777b1a858c17d5b)), closes [#21815](https://github.com/bitnami/charts/issues/21815)
* [bitnami/rabbitmq] Release 12.6.2 updating components versions (#21868) ([a98d15c](https://github.com/bitnami/charts/commit/a98d15c1b27daa01aa3f7379bc2dfe2c8bd8b929)), closes [#21868](https://github.com/bitnami/charts/issues/21868)

## <small>12.6.1 (2023-12-22)</small>

* [bitnami/rabbitmq] Release 12.6.1 updating components versions (#21726) ([4960f0c](https://github.com/bitnami/charts/commit/4960f0cad285d0f57c789f4285b5dd1873589264)), closes [#21726](https://github.com/bitnami/charts/issues/21726)

## 12.6.0 (2023-12-19)

* [bitnami/rabbitmq] Add support for defining cluster name (#21621) ([c50a384](https://github.com/bitnami/charts/commit/c50a384e40f24d0d0ad534c33994d86af3c334d9)), closes [#21621](https://github.com/bitnami/charts/issues/21621)

## <small>12.5.7 (2023-12-17)</small>

* [bitnami/rabbitmq] Release 12.5.7 updating components versions (#21604) ([a2b246f](https://github.com/bitnami/charts/commit/a2b246f17ca246a10bcb9e150d1e5e1727e5ed43)), closes [#21604](https://github.com/bitnami/charts/issues/21604)

## <small>12.5.6 (2023-12-05)</small>

* [bitnami/rabbitmq] Replace deprecated pull secret partial (#21395) ([7ce2a11](https://github.com/bitnami/charts/commit/7ce2a11dd31f49c23e03c518a54d19837a5e437c)), closes [#21395](https://github.com/bitnami/charts/issues/21395)

## <small>12.5.5 (2023-12-01)</small>

* [bitnami/rabbitmq] Release 12.5.5 updating components versions (#21344) ([d45ae0b](https://github.com/bitnami/charts/commit/d45ae0b5ae09b0da6730a6e4b70925c441ce607a)), closes [#21344](https://github.com/bitnami/charts/issues/21344)

## <small>12.5.4 (2023-11-22)</small>

* [bitnami/rabbitmq] Release 12.5.4 updating components versions (#21204) ([26f1c0b](https://github.com/bitnami/charts/commit/26f1c0b997de200e340f9a3f6891cadc7b00fcc1)), closes [#21204](https://github.com/bitnami/charts/issues/21204)

## <small>12.5.3 (2023-11-21)</small>

* [bitnami/*] Rename solutions to "Bitnami package for ..." (#21038) ([b82f979](https://github.com/bitnami/charts/commit/b82f979e4fb63423fe6e2192c946d09d79c944fc)), closes [#21038](https://github.com/bitnami/charts/issues/21038)
* [bitnami/rabbitmq] Release 12.5.3 updating components versions (#21170) ([65fd490](https://github.com/bitnami/charts/commit/65fd490d26e4091683287517511b930c6be59af2)), closes [#21170](https://github.com/bitnami/charts/issues/21170)

## <small>12.5.2 (2023-11-21)</small>

* [bitnami/rabbitmq] Release 12.5.2 updating components versions (#21083) ([4675e03](https://github.com/bitnami/charts/commit/4675e038a65d1fa3e50f7f7a3964791dfffd4a55)), closes [#21083](https://github.com/bitnami/charts/issues/21083)

## <small>12.5.1 (2023-11-17)</small>

* [bitnami/*] Remove relative links to non-README sections, add verification for that and update TL;DR ([1103633](https://github.com/bitnami/charts/commit/11036334d82df0490aa4abdb591543cab6cf7d7f)), closes [#20967](https://github.com/bitnami/charts/issues/20967)
* [bitnami/rabbitmq] Release 12.5.1 updating components versions (#21029) ([b311186](https://github.com/bitnami/charts/commit/b311186b64ce1679bef16a1d5034e5ab9cd27779)), closes [#21029](https://github.com/bitnami/charts/issues/21029)

## 12.5.0 (2023-11-15)

* [bitnami/rabbitmq] Add Persistent Volume Claim Retention Policy to Rabbitmq Statefulsets   (#20915) ([0a70a6e](https://github.com/bitnami/charts/commit/0a70a6e53579360ce61a3c959b743ba1c2573031)), closes [#20915](https://github.com/bitnami/charts/issues/20915)

## <small>12.4.2 (2023-11-09)</small>

* [bitnami/rabbitmq] Release 12.4.2 updating components versions (#20869) ([aacd84e](https://github.com/bitnami/charts/commit/aacd84ed92e8776227b93f30fad64b8e5e155819)), closes [#20869](https://github.com/bitnami/charts/issues/20869)

## <small>12.4.1 (2023-11-08)</small>

* [bitnami/rabbitmq] Release 12.4.1 updating components versions (#20707) ([5194f22](https://github.com/bitnami/charts/commit/5194f22f56b542bf9c23a2da4e756e7c98ba3080)), closes [#20707](https://github.com/bitnami/charts/issues/20707)

## 12.4.0 (2023-11-06)

* [bitami/rabbitmq] Added ability to enable/disable allocateLoadBalancerNodePorts (#20503) ([8b508b0](https://github.com/bitnami/charts/commit/8b508b0ab18f6e09e948e37d86253d1742d2d57e)), closes [#20503](https://github.com/bitnami/charts/issues/20503)

## <small>12.3.1 (2023-11-05)</small>

* [bitnami/rabbitmq] Release 12.3.1 updating components versions (#20624) ([10bcb64](https://github.com/bitnami/charts/commit/10bcb644fd92f5937cb3bd5f9ce4961d4f35fc1d)), closes [#20624](https://github.com/bitnami/charts/issues/20624)

## 12.3.0 (2023-10-24)

* [bitnami/*] Rename VMware Application Catalog (#20361) ([3acc734](https://github.com/bitnami/charts/commit/3acc73472beb6fb56c4d99f929061001205bc57e)), closes [#20361](https://github.com/bitnami/charts/issues/20361)
* [bitnami/*] Skip image's tag in the README files of the Bitnami Charts (#19841) ([bb9a01b](https://github.com/bitnami/charts/commit/bb9a01b65911c87e48318db922cc05eb42785e42)), closes [#19841](https://github.com/bitnami/charts/issues/19841)
* [bitnami/*] Standardize documentation (#19835) ([af5f753](https://github.com/bitnami/charts/commit/af5f7530c1bc8c5ded53a6c4f7b8f384ac1804f2)), closes [#19835](https://github.com/bitnami/charts/issues/19835)
* [bitnami/rabbitmq] feat: :sparkles: Add support for PSA restricted policy (#20367) ([7fa78ed](https://github.com/bitnami/charts/commit/7fa78edaee5cbbe4d666b0e9df53cc6627a95aab)), closes [#20367](https://github.com/bitnami/charts/issues/20367)

## <small>12.2.5 (2023-10-18)</small>

* [bitnami/rabbitmq] Release 12.2.5 (#20295) ([907aab9](https://github.com/bitnami/charts/commit/907aab991f0e32a9fee98d720a02b06313fd3976)), closes [#20295](https://github.com/bitnami/charts/issues/20295)

## <small>12.2.4 (2023-10-16)</small>

* bitnami/rabbitmq corrected clustering.partitionHandling enum values (#19558) ([6d3450c](https://github.com/bitnami/charts/commit/6d3450c901ee3c19a6422578cd755e6d37399670)), closes [#19558](https://github.com/bitnami/charts/issues/19558)

## <small>12.2.3 (2023-10-12)</small>

* [bitnami/rabbitmq] Release 12.2.3 updating components versions (#20169) ([0820d76](https://github.com/bitnami/charts/commit/0820d76f8dfeab8f150bbc9c498bc432afc821dd)), closes [#20169](https://github.com/bitnami/charts/issues/20169)

## <small>12.2.2 (2023-10-12)</small>

* [bitnami/rabbitmq] Release 12.2.2 (#20117) ([b0fe7a5](https://github.com/bitnami/charts/commit/b0fe7a5be3e7c2f840417a6a16d850193318e820)), closes [#20117](https://github.com/bitnami/charts/issues/20117)

## <small>12.2.1 (2023-10-09)</small>

* [bitnami/*] Update Helm charts prerequisites (#19745) ([eb755dd](https://github.com/bitnami/charts/commit/eb755dd36a4dd3cf6635be8e0598f9a7f4c4a554)), closes [#19745](https://github.com/bitnami/charts/issues/19745)
* [bitnami/rabbitmq] Release 12.2.1 (#19937) ([84ef6b1](https://github.com/bitnami/charts/commit/84ef6b195e18fcbeb5e0b386e0474d651117eb1a)), closes [#19937](https://github.com/bitnami/charts/issues/19937)

## 12.2.0 (2023-10-03)

* bitnami/rabbitmq Add support for enableServiceLinks on rabbitmq chart (#19643) ([672284b](https://github.com/bitnami/charts/commit/672284bf5fe7e015feb64cc7b988b5c1f47dceed)), closes [#19643](https://github.com/bitnami/charts/issues/19643)

## <small>12.1.7 (2023-09-25)</small>

* [bitnami/rabbitmq] Release 12.1.7 (#19512) ([b93f8fa](https://github.com/bitnami/charts/commit/b93f8fad052f7b462b67b231dd15ca1e598e78a8)), closes [#19512](https://github.com/bitnami/charts/issues/19512)

## <small>12.1.6 (2023-09-22)</small>

* [bitnami/rabbitmq] Release 12.1.6 (#19463) ([338a3d5](https://github.com/bitnami/charts/commit/338a3d5d5bf77a29decd9f9e2d58becf7f7f9cf1)), closes [#19463](https://github.com/bitnami/charts/issues/19463)

## <small>12.1.5 (2023-09-21)</small>

* [bitnami/rabbitmq] Release 12.1.5 (#19453) ([380b1f5](https://github.com/bitnami/charts/commit/380b1f52885bedf01e38d1082415c784230b125d)), closes [#19453](https://github.com/bitnami/charts/issues/19453)
* Autogenerate schema files (#19194) ([a2c2090](https://github.com/bitnami/charts/commit/a2c2090b5ac97f47b745c8028c6452bf99739772)), closes [#19194](https://github.com/bitnami/charts/issues/19194)
* Revert "Autogenerate schema files (#19194)" (#19335) ([73d80be](https://github.com/bitnami/charts/commit/73d80be525c88fb4b8a54451a55acd506e337062)), closes [#19194](https://github.com/bitnami/charts/issues/19194) [#19335](https://github.com/bitnami/charts/issues/19335)

## <small>12.1.4 (2023-09-08)</small>

* [bitnami/rabbitmq]: Use merge helper: (#19098) ([30353ba](https://github.com/bitnami/charts/commit/30353ba7ca3004207d5aff994471e5a57e3d4f2e)), closes [#19098](https://github.com/bitnami/charts/issues/19098)

## <small>12.1.3 (2023-08-28)</small>

* [bitnami/rabbitmq] test: :white_check_mark: Add persistence tests (#18812) ([fd9745a](https://github.com/bitnami/charts/commit/fd9745a9f99561d2654fe7692da6af53ded4555c)), closes [#18812](https://github.com/bitnami/charts/issues/18812)

## <small>12.1.2 (2023-08-25)</small>

* [bitnami/rabbitmq] Release 12.1.2 (#18845) ([2daf44e](https://github.com/bitnami/charts/commit/2daf44e485c6bc27a6b5464a5712e4a6662cdea6)), closes [#18845](https://github.com/bitnami/charts/issues/18845)

## <small>12.1.1 (2023-08-24)</small>

* [bitnami/rabbitmq] fix path to serviceMonitor annotations value (#18797) ([85fe75b](https://github.com/bitnami/charts/commit/85fe75bfcc1f4a6add364b9d5f31d010852f9a70)), closes [#18797](https://github.com/bitnami/charts/issues/18797)

## 12.1.0 (2023-08-22)

* [bitnami/rabbitmq] Support for customizing standard labels (#18416) ([a070c62](https://github.com/bitnami/charts/commit/a070c6219afa3bffa28d6b69736b781eba8462b0)), closes [#18416](https://github.com/bitnami/charts/issues/18416)

## <small>12.0.13 (2023-08-20)</small>

* [bitnami/rabbitmq] Release 12.0.13 (#18711) ([1bc09aa](https://github.com/bitnami/charts/commit/1bc09aa5bc6a27c4ed51667d0210c9121ae866e5)), closes [#18711](https://github.com/bitnami/charts/issues/18711)

## <small>12.0.12 (2023-08-18)</small>

* [bitnami/rabbitmq] Release 12.0.12 (#18608) ([61384d6](https://github.com/bitnami/charts/commit/61384d624eab8ce7a8841c3e0115b892e1d8ef0b)), closes [#18608](https://github.com/bitnami/charts/issues/18608)

## <small>12.0.11 (2023-08-17)</small>

* [bitnami/rabbitmq] Release 12.0.11 (#18586) ([6bfa3bb](https://github.com/bitnami/charts/commit/6bfa3bb6253a8f021ec2c9821afce64fd650ce97)), closes [#18586](https://github.com/bitnami/charts/issues/18586)

## <small>12.0.10 (2023-08-04)</small>

* [bitnami/rabbitmq] Fix issue with ingress.existingSecret (#18125) ([ba42210](https://github.com/bitnami/charts/commit/ba42210ccea45732095e9cb2ed4427b68884e499)), closes [#18125](https://github.com/bitnami/charts/issues/18125)

## <small>12.0.9 (2023-07-26)</small>

* [bitnami/rabbitmq] Release 12.0.9 (#17954) ([ed09fce](https://github.com/bitnami/charts/commit/ed09fcebc209d6f3f5e171f7782ba1fd38b4d165)), closes [#17954](https://github.com/bitnami/charts/issues/17954)

## <small>12.0.8 (2023-07-22)</small>

* [bitnami/rabbitmq] Release 12.0.8 (#17826) ([ac13413](https://github.com/bitnami/charts/commit/ac134131bbb956cd6259d120245ea9e7a1e9b6c9)), closes [#17826](https://github.com/bitnami/charts/issues/17826)

## <small>12.0.7 (2023-07-17)</small>

* [bitnami/rabbitmq] Release 12.0.7 (#17752) ([872f61f](https://github.com/bitnami/charts/commit/872f61f29b9e1f660ce63187a790a98cbf983e31)), closes [#17752](https://github.com/bitnami/charts/issues/17752)

## <small>12.0.6 (2023-07-13)</small>

* [bitnami/rabbitmq] Release 12.0.6 (#17660) ([9f1bf9f](https://github.com/bitnami/charts/commit/9f1bf9ffab3a6d9e05028b819c8e8cef688d4d84)), closes [#17660](https://github.com/bitnami/charts/issues/17660)

## <small>12.0.5 (2023-07-13)</small>

* [bitnami/rabbitmq] Updating the tls certs through values.yaml not restarting the pods (#17537) ([c0dea3c](https://github.com/bitnami/charts/commit/c0dea3c2f77fd62d3a5e1ab61788ebf7fbf1d5f5)), closes [#17537](https://github.com/bitnami/charts/issues/17537)

## <small>12.0.4 (2023-06-27)</small>

* [bitnami/rabbitmq] Release 12.0.4 (#17361) ([2d36d89](https://github.com/bitnami/charts/commit/2d36d89f4a6e757b59a3c6296e7cfa891549347a)), closes [#17361](https://github.com/bitnami/charts/issues/17361)
* Add copyright header (#17300) ([da68be8](https://github.com/bitnami/charts/commit/da68be8e951225133c7dfb572d5101ca3d61c5ae)), closes [#17300](https://github.com/bitnami/charts/issues/17300)

## <small>12.0.3 (2023-06-22)</small>

* [bitnami/rabbitmq] Release 12.0.3 (#17319) ([2858f96](https://github.com/bitnami/charts/commit/2858f965ea5503bd91006fd01de59562260c4cb6)), closes [#17319](https://github.com/bitnami/charts/issues/17319)
* Update charts readme (#17217) ([31b3c0a](https://github.com/bitnami/charts/commit/31b3c0afd968ff4429107e34101f7509e6a0e913)), closes [#17217](https://github.com/bitnami/charts/issues/17217)

## <small>12.0.2 (2023-06-19)</small>

* [bitnami/rabbitmq] removed empty line added between password and cookie secret (#17162) ([4290ffe](https://github.com/bitnami/charts/commit/4290ffe6a9629a4f2d9a5e261bf20188720fc944)), closes [#17162](https://github.com/bitnami/charts/issues/17162)

## <small>12.0.1 (2023-06-13)</small>

* [bitnami/rabbitmq] Set full hostname in rabbitmq svcbind secret (#17056) ([8dfaf4d](https://github.com/bitnami/charts/commit/8dfaf4d8b7e6417ff7701689cd22845dfd08cfcb)), closes [#17056](https://github.com/bitnami/charts/issues/17056)

## 12.0.0 (2023-06-07)

* [bitnami/rabbitmq] Release 12.0.0 (#17064) ([1b86d3b](https://github.com/bitnami/charts/commit/1b86d3bbc335202697a155b6d9738b4f01328a40)), closes [#17064](https://github.com/bitnami/charts/issues/17064)

## <small>11.16.2 (2023-06-05)</small>

* [bitnami/*] Change copyright section in READMEs (#17006) ([ef986a1](https://github.com/bitnami/charts/commit/ef986a1605241102b3dcafe9fd8089e6fc1201ad)), closes [#17006](https://github.com/bitnami/charts/issues/17006)
* [bitnami/rabbitmq] Release 11.16.2 (#17032) ([56326c7](https://github.com/bitnami/charts/commit/56326c7efbe8e13aff5e80c84dfe4b397911d2de)), closes [#17032](https://github.com/bitnami/charts/issues/17032)

## <small>11.16.1 (2023-06-01)</small>

* [bitnami/rabbitmq] Release 11.16.1 (#16991) ([3f5cb79](https://github.com/bitnami/charts/commit/3f5cb79454418d4e353ccc05ee3d99ce64105bda)), closes [#16991](https://github.com/bitnami/charts/issues/16991)
* [bitnami/several] Change copyright section in READMEs (#16989) ([5b6a5cf](https://github.com/bitnami/charts/commit/5b6a5cfb7625a751848a2e5cd796bd7278f406ca)), closes [#16989](https://github.com/bitnami/charts/issues/16989)

## 11.16.0 (2023-05-31)

* [bitnami/rabbitmq] Add possibility to specify `params` for ServiceMonitor (#16896) ([a5dba57](https://github.com/bitnami/charts/commit/a5dba5752210d099919ea5797a53f964341637dd)), closes [#16896](https://github.com/bitnami/charts/issues/16896)

## <small>11.15.7 (2023-05-31)</small>

* Re add curl validation (#16976) ([94da8bb](https://github.com/bitnami/charts/commit/94da8bb6df32884071ecb298bee5a3b58ec8fd47)), closes [#16976](https://github.com/bitnami/charts/issues/16976) [#16916](https://github.com/bitnami/charts/issues/16916)

## <small>11.15.6 (2023-05-31)</small>

* [bitnami/rabbitmq] Release 11.15.6 (#16974) ([2daef2a](https://github.com/bitnami/charts/commit/2daef2afaa75ac78b11e0f6119b8790ef188537b)), closes [#16974](https://github.com/bitnami/charts/issues/16974)
* Changes auth.enableLoopbackUser parameter description (#16958) ([7bd397a](https://github.com/bitnami/charts/commit/7bd397aa2e22aeafe2e97fcdb8eeb547f2a1e223)), closes [#16958](https://github.com/bitnami/charts/issues/16958)

## <small>11.15.5 (2023-05-30)</small>

* [bitnami/rabbitmq] Release 11.15.5 (#16952) ([02bb38b](https://github.com/bitnami/charts/commit/02bb38b52acde1fbcd4d2f4fa64305edba374789)), closes [#16952](https://github.com/bitnami/charts/issues/16952)

## <small>11.15.4 (2023-05-29)</small>

* [bitnami/rabbitmq] Release 11.15.4 (#16916) ([c6939a5](https://github.com/bitnami/charts/commit/c6939a5234042438a79e5d1167adc101f1407ad9)), closes [#16916](https://github.com/bitnami/charts/issues/16916)

## <small>11.15.3 (2023-05-21)</small>

* [bitnami/rabbitmq] Release 11.15.3 (#16786) ([06e2ea2](https://github.com/bitnami/charts/commit/06e2ea2d99bff0e5aefeb52c8bb18b6dde610b0c)), closes [#16786](https://github.com/bitnami/charts/issues/16786)

## <small>11.15.2 (2023-05-13)</small>

* [bitnami/rabbitmq] Release 11.15.2 (#16635) ([b9a8e74](https://github.com/bitnami/charts/commit/b9a8e74150c9b164ad510e997c3bca281bc2c753)), closes [#16635](https://github.com/bitnami/charts/issues/16635)

## <small>11.15.1 (2023-05-11)</small>

* [bitnami/rabbitmq] Use built-in probes if management is not installed (#16567) ([e3d48d6](https://github.com/bitnami/charts/commit/e3d48d69de636046fbdb165c8423a0f5671d7196)), closes [#16567](https://github.com/bitnami/charts/issues/16567)
* Add wording for enterprise page (#16560) ([8f22774](https://github.com/bitnami/charts/commit/8f2277440b976d52785ba9149762ad8620a73d1f)), closes [#16560](https://github.com/bitnami/charts/issues/16560)

## 11.15.0 (2023-05-09)

* [bitnami/several] Adapt Chart.yaml to set desired OCI annotations (#16546) ([fc9b18f](https://github.com/bitnami/charts/commit/fc9b18f2e98805d4df629acbcde696f44f973344)), closes [#16546](https://github.com/bitnami/charts/issues/16546)

## <small>11.14.5 (2023-05-09)</small>

* [bitnami/rabbitmq] Release 11.14.5 (#16497) ([32c0945](https://github.com/bitnami/charts/commit/32c0945a0bb0ac46ad775d3f14abe0f21799908d)), closes [#16497](https://github.com/bitnami/charts/issues/16497)
* Align tls port name to amqp-tls by default (#16335) ([1104e32](https://github.com/bitnami/charts/commit/1104e32e205a579e92196123f98582898bda3f4f)), closes [#16335](https://github.com/bitnami/charts/issues/16335)

## <small>11.14.4 (2023-05-02)</small>

* [bitnami/rabbitmq] Fix high CPU usage while idle (#16082) ([83827ce](https://github.com/bitnami/charts/commit/83827ce340efd7f7203d1ca7789eb9205110a1e0)), closes [#16082](https://github.com/bitnami/charts/issues/16082) [#11117](https://github.com/bitnami/charts/issues/11117) [#11180](https://github.com/bitnami/charts/issues/11180) [#11116](https://github.com/bitnami/charts/issues/11116)

## <small>11.14.3 (2023-04-29)</small>

* [bitnami/rabbitmq] Release 11.14.3 (#16285) ([81861c3](https://github.com/bitnami/charts/commit/81861c37fee3f554c1518f4d81cabcfe6555a7ce)), closes [#16285](https://github.com/bitnami/charts/issues/16285)

## <small>11.14.2 (2023-04-29)</small>

* [bitnami/rabbitmq] Release 11.14.2 (#16271) ([5d68b21](https://github.com/bitnami/charts/commit/5d68b219bd1b6b281fb4520da43a7376847bbbc0)), closes [#16271](https://github.com/bitnami/charts/issues/16271)

## <small>11.14.1 (2023-04-27)</small>

* [bitnami/rabbitmq] Use username as key in the Service Binding secret (#16254) ([483fcc8](https://github.com/bitnami/charts/commit/483fcc8527b6005f31b16a963e28ebd837d09cbb)), closes [#16254](https://github.com/bitnami/charts/issues/16254)

## 11.14.0 (2023-04-20)

* [bitnami/*] Make Helm charts 100% OCI (#15998) ([8841510](https://github.com/bitnami/charts/commit/884151035efcbf2e1b3206e7def85511073fb57d)), closes [#15998](https://github.com/bitnami/charts/issues/15998)

## 11.13.0 (2023-04-04)

* [bitnami/rabbitmq] Updated values.yaml (#15934) ([ac8e273](https://github.com/bitnami/charts/commit/ac8e2736ad9a50b032c2a8432099cb90fe03a026)), closes [#15934](https://github.com/bitnami/charts/issues/15934)

## <small>11.12.3 (2023-04-04)</small>

* Add missing version, kind to volumeClaimTemplates (#15943) ([b8ff10c](https://github.com/bitnami/charts/commit/b8ff10c51ab4035508ee3d44062964e73b6ede3b)), closes [#15943](https://github.com/bitnami/charts/issues/15943)

## <small>11.12.2 (2023-04-01)</small>

* [bitnami/rabbitmq] Release 11.12.2 (#15905) ([9c16737](https://github.com/bitnami/charts/commit/9c1673758da7889362a9eff6b2ca634f30b96466)), closes [#15905](https://github.com/bitnami/charts/issues/15905)

## <small>11.12.1 (2023-03-31)</small>

* [bitnami/rabbitmq] Empty RABBITMQ_FEATURE_FLAGS variable causes some flags to be disabled (#15819) ([dcd192a](https://github.com/bitnami/charts/commit/dcd192ac7e8509f43e3786255699513a105f17ad)), closes [#15819](https://github.com/bitnami/charts/issues/15819)

## 11.12.0 (2023-03-21)

* [bitnami/rabbitmq] Add support for service.headless.annotations (#15440) ([9db2941](https://github.com/bitnami/charts/commit/9db2941e97b0d1f3641297a184f3b52cac7d226f)), closes [#15440](https://github.com/bitnami/charts/issues/15440)

## <small>11.11.2 (2023-03-20)</small>

* [bitnami/rabbitmq] Release 11.11.2 (#15647) ([4eb06e0](https://github.com/bitnami/charts/commit/4eb06e053891c1c9547d43d16d93d84dba066fbc)), closes [#15647](https://github.com/bitnami/charts/issues/15647)

## <small>11.11.1 (2023-03-19)</small>

* [bitnami/rabbitmq] Release 11.11.1 (#15604) ([2c8446f](https://github.com/bitnami/charts/commit/2c8446fcf14fabfbe960ccc513c4945844e85af4)), closes [#15604](https://github.com/bitnami/charts/issues/15604)

## 11.11.0 (2023-03-17)

* [bitnami/charts] Apply linter to README files (#15357) ([0e29e60](https://github.com/bitnami/charts/commit/0e29e600d3adc8b1b46e506eccb3decfab3b4e63)), closes [#15357](https://github.com/bitnami/charts/issues/15357)
* Add support for StatefulSet annotations (#15529) ([4566584](https://github.com/bitnami/charts/commit/456658446dc26e5c60ab18a35af9883d4d41379a)), closes [#15529](https://github.com/bitnami/charts/issues/15529)

## <small>11.10.3 (2023-03-07)</small>

* [bitnami/rabbitmq] add PVC labels (#15352) ([bd0fbf7](https://github.com/bitnami/charts/commit/bd0fbf7b4d123d1024982bc6fdbdee99ac84a8f8)), closes [#15352](https://github.com/bitnami/charts/issues/15352)

## <small>11.10.2 (2023-03-02)</small>

* [bitnami/rabbitmq] Release 11.10.2 (#15305) ([1a09a4e](https://github.com/bitnami/charts/commit/1a09a4e9b8b0741d820232cb67e442199a18856e)), closes [#15305](https://github.com/bitnami/charts/issues/15305)

## <small>11.10.1 (2023-03-01)</small>

* [bitnami/rabbitmq] Release 11.10.1 (#15269) ([505749e](https://github.com/bitnami/charts/commit/505749e3ad25e43d58099e10764ecacff3482f65)), closes [#15269](https://github.com/bitnami/charts/issues/15269)

## 11.10.0 (2023-02-21)

* [bitnami/rabbitmq] feat: :sparkles: Add ServiceBinding-compatible secrets (#14915) ([6256b83](https://github.com/bitnami/charts/commit/6256b8312ce6efa14f5921d0acccfa536bd7668d)), closes [#14915](https://github.com/bitnami/charts/issues/14915)

## <small>11.9.3 (2023-02-17)</small>

* [bitnami/rabbitmq] Release 11.9.3 (#15030) ([9b47294](https://github.com/bitnami/charts/commit/9b47294438e9b9176531a0bb512db1a37c7424ff)), closes [#15030](https://github.com/bitnami/charts/issues/15030)

## <small>11.9.2 (2023-02-17)</small>

* [bitnami/*] Fix markdown linter issues (#14874) ([a51e0e8](https://github.com/bitnami/charts/commit/a51e0e8d35495b907f3e70dd2f8e7c3bcbf4166a)), closes [#14874](https://github.com/bitnami/charts/issues/14874)
* [bitnami/*] Fix markdown linter issues 2 (#14890) ([aa96572](https://github.com/bitnami/charts/commit/aa9657237ee8df4a46db0d7fdf8a23230dd6902a)), closes [#14890](https://github.com/bitnami/charts/issues/14890)
* [bitnami/*] Remove unexpected extra spaces (#14873) ([c97c714](https://github.com/bitnami/charts/commit/c97c714887380d47eae7bfeff316bf01595ecd1d)), closes [#14873](https://github.com/bitnami/charts/issues/14873)
* [bitnami/rabbitmq] fix broken config option #14766 (#14859) ([75b0cf6](https://github.com/bitnami/charts/commit/75b0cf6f8958d8b35bfa2670d30e4b81e8e88d8d)), closes [#14766](https://github.com/bitnami/charts/issues/14766) [#14859](https://github.com/bitnami/charts/issues/14859) [#14766](https://github.com/bitnami/charts/issues/14766) [#14766](https://github.com/bitnami/charts/issues/14766)

## <small>11.9.1 (2023-02-12)</small>

* [bitnami/rabbitmq] Release 11.9.1 (#14851) ([c7aa17e](https://github.com/bitnami/charts/commit/c7aa17ee9e506ee0497ec62525be775fcd91dd6b)), closes [#14851](https://github.com/bitnami/charts/issues/14851)

## 11.9.0 (2023-02-06)

* Charts failing because of the feature flags not enabled (#14387) ([814a95a](https://github.com/bitnami/charts/commit/814a95a971258f276080cdb48fcd98a58f42baeb)), closes [#14387](https://github.com/bitnami/charts/issues/14387)

## 11.8.0 (2023-02-03)

* [bitnami/rabbitmq] Add Possibility to set tcpListenOptions (#14719) ([37b6c5b](https://github.com/bitnami/charts/commit/37b6c5bb391f005db6d93a0566904477f75055bb)), closes [#14719](https://github.com/bitnami/charts/issues/14719) [#14702](https://github.com/bitnami/charts/issues/14702)

## <small>11.7.1 (2023-02-02)</small>

* [bitnami/rabbitmq] Don't regenerate self-signed certs on upgrade (#14653) ([1369f8e](https://github.com/bitnami/charts/commit/1369f8eca641f96d345c6948153ed0b9e145d09b)), closes [#14653](https://github.com/bitnami/charts/issues/14653)

## 11.7.0 (2023-02-01)

* [bitnami/*] Change copyright date (#14682) ([add4ec7](https://github.com/bitnami/charts/commit/add4ec701108ac36ed4de2dffbdf407a0d091067)), closes [#14682](https://github.com/bitnami/charts/issues/14682)
* [bitnami/rabbitmq] Add ssl_options.password (#14463) ([9941124](https://github.com/bitnami/charts/commit/9941124a3f8d3770d3b17acfaeec0e134930dd25)), closes [#14463](https://github.com/bitnami/charts/issues/14463) [#14373](https://github.com/bitnami/charts/issues/14373) [#14373](https://github.com/bitnami/charts/issues/14373)

## <small>11.6.1 (2023-01-31)</small>

* [bitnami/rabbitmq] Release 11.6.1 (#14675) ([54b2278](https://github.com/bitnami/charts/commit/54b2278da50ae17b8f32e22f912d5ab4112ca6b0)), closes [#14675](https://github.com/bitnami/charts/issues/14675)

## 11.6.0 (2023-01-26)

* [bitnam/rabbitmq] Choice of setting configuration and extraConfiguration as secret (#14519) ([5520624](https://github.com/bitnami/charts/commit/5520624e67f0ae44928d83d31dadf5e8a217584b)), closes [#14519](https://github.com/bitnami/charts/issues/14519) [#14444](https://github.com/bitnami/charts/issues/14444) [#14444](https://github.com/bitnami/charts/issues/14444) [#14444](https://github.com/bitnami/charts/issues/14444) [#14444](https://github.com/bitnami/charts/issues/14444) [#14444](https://github.com/bitnami/charts/issues/14444) [#14444](https://github.com/bitnami/charts/issues/14444) [#14444](https://github.com/bitnami/charts/issues/14444)

## <small>11.5.1 (2023-01-25)</small>

* [bitnami/rabbitmq] Release 11.5.1 (#14535) ([15e9c17](https://github.com/bitnami/charts/commit/15e9c1703ac409ecd6844cf7951f7172f49f70d6)), closes [#14535](https://github.com/bitnami/charts/issues/14535)

## 11.5.0 (2023-01-23)

* [bitnami/*] Change licenses annotation format (#14377) ([0ab7608](https://github.com/bitnami/charts/commit/0ab760862c660fcc78cffadf8e1d8cdd70881473)), closes [#14377](https://github.com/bitnami/charts/issues/14377)
* [bitnami/*] Unify READMEs (#14472) ([2064fb8](https://github.com/bitnami/charts/commit/2064fb8dcc78a845cdede8211af8c3cc52551161)), closes [#14472](https://github.com/bitnami/charts/issues/14472)
* [bitnami/rabbitmq] Choice of enabling loopbackUser (#14467) ([3bb3c5e](https://github.com/bitnami/charts/commit/3bb3c5e476eda6dd041cfdd03658745f2364c781)), closes [#14467](https://github.com/bitnami/charts/issues/14467) [#14445](https://github.com/bitnami/charts/issues/14445)

## 11.4.0 (2023-01-12)

* [bitnami/*] Add license annotation and remove obsolete engine parameter (#14293) ([da2a794](https://github.com/bitnami/charts/commit/da2a7943bae95b6e9b5b4ed972c15e990b69fdb0)), closes [#14293](https://github.com/bitnami/charts/issues/14293)
* Closes #14209 - Separate TLS certificate/key secret and CA configuration (#14233) ([55d5a72](https://github.com/bitnami/charts/commit/55d5a725697b3facb29eaa265eab3466a047a512)), closes [#14209](https://github.com/bitnami/charts/issues/14209) [#14233](https://github.com/bitnami/charts/issues/14233)

## <small>11.3.2 (2023-01-11)</small>

* [bitnami/rabbitmq] Fixes 14212 - Rabbitmq metrics port configuration follows containerPorts.metrics  ([98b293a](https://github.com/bitnami/charts/commit/98b293a0a7d0d8cac08659b23c4a6a7eebf00d1d)), closes [#14219](https://github.com/bitnami/charts/issues/14219)

## <small>11.3.1 (2023-01-05)</small>

* [bitnami/rabbitmq] Release 11.3.1 (#14198) ([057b9be](https://github.com/bitnami/charts/commit/057b9bec0c0cd63387d4c5f123ede45053a2aa73)), closes [#14198](https://github.com/bitnami/charts/issues/14198)

## 11.3.0 (2022-12-23)

* [bitnami/rabbitmq] Set advanced configuration from an existing secret (#14071) ([a1b42f2](https://github.com/bitnami/charts/commit/a1b42f2ae620c6c91c7f152787191829b1ec2522)), closes [#14071](https://github.com/bitnami/charts/issues/14071)

## <small>11.2.2 (2022-12-16)</small>

* [bitnami/rabbitmq] Release 11.2.2 (#13991) ([46f96af](https://github.com/bitnami/charts/commit/46f96afc8ecedfb1ad17bcb6338e54bc08a46793)), closes [#13991](https://github.com/bitnami/charts/issues/13991)

## <small>11.2.1 (2022-12-14)</small>

* [bitnami/rabbitmq] Release 11.2.1 (#13949) ([fbcec45](https://github.com/bitnami/charts/commit/fbcec455087d94ed1a32b7b04133a6dae66c932d)), closes [#13949](https://github.com/bitnami/charts/issues/13949)

## 11.2.0 (2022-12-07)

* [bitnami/rabbitmq] Add parameter for headless service naming customization (#13805) ([e963f17](https://github.com/bitnami/charts/commit/e963f17318085b18eb87f88d44415da7ecd4f3d0)), closes [#13805](https://github.com/bitnami/charts/issues/13805)

## <small>11.1.5 (2022-11-29)</small>

* [bitnami/rabbitmq] Release 11.1.5 (#13731) ([ff99dba](https://github.com/bitnami/charts/commit/ff99dbadd4d92b95a5d64cc65815a0288dc89271)), closes [#13731](https://github.com/bitnami/charts/issues/13731)

## <small>11.1.4 (2022-11-21)</small>

* [bitnami/rabbitmq] fix apiversion hardcode for NetworkPolicy (#13600) ([9073ee2](https://github.com/bitnami/charts/commit/9073ee27b4c5b5f27fc2dc52e1285ca7b49d17d1)), closes [#13600](https://github.com/bitnami/charts/issues/13600)

## <small>11.1.3 (2022-11-18)</small>

* [bitnami/rabbitmq]fix apiversion hardcode for statefulset (#13563) ([b014902](https://github.com/bitnami/charts/commit/b014902321aba4c8a598583cdd9d133ab031da6a)), closes [#13563](https://github.com/bitnami/charts/issues/13563)

## <small>11.1.2 (2022-11-10)</small>

* [bitnami/rabbitmq] Release 11.1.2 (#13452) ([61cac97](https://github.com/bitnami/charts/commit/61cac9795ee4679efdf6b1f6248c476dd2b99012)), closes [#13452](https://github.com/bitnami/charts/issues/13452)

## <small>11.1.1 (2022-10-27)</small>

* [bitnami/rabbitmq] Configure password correctly when RABBITMQ_SECURE_PASSWORD is not enabled (#13141 ([e51cb75](https://github.com/bitnami/charts/commit/e51cb757f5d0fe794bb6eac43c15ff38fcddbe23)), closes [#13141](https://github.com/bitnami/charts/issues/13141)

## 11.1.0 (2022-10-25)

* [bitnami/rabbitmq]fix secure password env hardcode (#13056) ([dfad63c](https://github.com/bitnami/charts/commit/dfad63c3db1756b0489f0167d03f681f4fc4e8ab)), closes [#13056](https://github.com/bitnami/charts/issues/13056)

## <small>11.0.4 (2022-10-24)</small>

* [bitnami/rabbitmq] Fix rolebinding creation (#13115) ([a946379](https://github.com/bitnami/charts/commit/a946379decf8b051cd396af94e80eb53da1e1371)), closes [#13115](https://github.com/bitnami/charts/issues/13115)

## <small>11.0.3 (2022-10-19)</small>

* [bitnami/rabbitmq] Release 11.0.3 (#13022) ([f57d296](https://github.com/bitnami/charts/commit/f57d29664fa11afb4571c83c3b99c5cc9d1ad14a)), closes [#13022](https://github.com/bitnami/charts/issues/13022)

## <small>11.0.2 (2022-10-18)</small>

* [bitnami/*] Use new default branch name in links (#12943) ([a529e02](https://github.com/bitnami/charts/commit/a529e02597d49d944eba1eb0f190713293247176)), closes [#12943](https://github.com/bitnami/charts/issues/12943)
* [bitnami/rabbitmq] Release 11.0.2 (#12995) ([f15388e](https://github.com/bitnami/charts/commit/f15388eabbb1422f0944c126c22fb28ae9b70f5e)), closes [#12995](https://github.com/bitnami/charts/issues/12995)
* [bitnami/rabbitmq] Support ingress.existingSecret for Rabbitmq (#12908) ([a4b91d0](https://github.com/bitnami/charts/commit/a4b91d050546b3935dc59dba62c53bec30e862c5)), closes [#12908](https://github.com/bitnami/charts/issues/12908)
* [bitnami/rabbitmq] Update README after releasing version 11.0.0 (#12973) ([60f203c](https://github.com/bitnami/charts/commit/60f203c7557320956c3c1a4114186641bdf60c92)), closes [#12973](https://github.com/bitnami/charts/issues/12973)

## <small>11.0.1 (2022-10-17)</small>

* [bitnami/rabbitmq] Release 11.0.1 (#12949) ([b1592ca](https://github.com/bitnami/charts/commit/b1592ca9a7a8a357c9371e82ebde0c83bbe4d17b)), closes [#12949](https://github.com/bitnami/charts/issues/12949)

## 11.0.0 (2022-10-11)

* [bitnami/rabbitmq] Release 11.0.0 (#12909) ([bb360fe](https://github.com/bitnami/charts/commit/bb360fe5cd1ff86d7b7a0417550c66c93ce08cb9)), closes [#12909](https://github.com/bitnami/charts/issues/12909)

## <small>10.3.9 (2022-10-05)</small>

* [bitnami/rabbitmq] Release 10.3.9 (#12824) ([93b0f1a](https://github.com/bitnami/charts/commit/93b0f1a9c8027aa8ecfe548053257656bff76339)), closes [#12824](https://github.com/bitnami/charts/issues/12824)

## <small>10.3.8 (2022-10-05)</small>

* [bitnami/rabbitmq] Generic README instructions related to the repo (#12795) ([344317e](https://github.com/bitnami/charts/commit/344317e07a480c79780eaf6855bb923832054cdd)), closes [#12795](https://github.com/bitnami/charts/issues/12795)
* [bitnami/rabbitmq] Release 10.3.8 (#12813) ([7e7ce74](https://github.com/bitnami/charts/commit/7e7ce742a40d78c7f55ae04582b0fdb7b3bfa5fe)), closes [#12813](https://github.com/bitnami/charts/issues/12813)

## <small>10.3.7 (2022-10-04)</small>

* [bitnami/rabbitmq] Release 10.3.7 (#12802) ([25a91de](https://github.com/bitnami/charts/commit/25a91dedc094c7bd9bc3da3b87cc34dc4ed0eb95)), closes [#12802](https://github.com/bitnami/charts/issues/12802)

## <small>10.3.6 (2022-09-27)</small>

* [bitnami/rabbitmq] Release 10.3.6 (#12684) ([ed47cdc](https://github.com/bitnami/charts/commit/ed47cdc8fc43b611dd37446a3d00768e66deef9a)), closes [#12684](https://github.com/bitnami/charts/issues/12684)

## <small>10.3.5 (2022-09-08)</small>

* [bitnami/rabbitmq] Release 10.3.4 (#12331) ([2fd9cbe](https://github.com/bitnami/charts/commit/2fd9cbecd6b132573098cc377a1ee7ac7bc8966a)), closes [#12331](https://github.com/bitnami/charts/issues/12331)

## <small>10.3.4 (2022-09-08)</small>

* [bitnami/rabbitmq] Bump chart version (#12327) ([c9ab756](https://github.com/bitnami/charts/commit/c9ab756534a2446ef08f0e0a8ab0433802dbc50e)), closes [#12327](https://github.com/bitnami/charts/issues/12327)
* RabbitMQ: Use custom probes if given (#12303) ([99e9d0c](https://github.com/bitnami/charts/commit/99e9d0c93ea11c2d70d90f1fb2f50860dfd70d0c)), closes [#12303](https://github.com/bitnami/charts/issues/12303)

## <small>10.3.3 (2022-09-07)</small>

* [bitnami/rabbitmq] Improve error handling message (#12227) (#12227) ([fdd88a5](https://github.com/bitnami/charts/commit/fdd88a59f3a4c9b65120b4f97ed4647fbee62a9e)), closes [#12227](https://github.com/bitnami/charts/issues/12227) [#12227](https://github.com/bitnami/charts/issues/12227)

## <small>10.3.2 (2022-08-30)</small>

* Fixing init scripts via configmap/secret (#12161) (#12170) ([39239fc](https://github.com/bitnami/charts/commit/39239fcb0378565bbe63b4fdee7ea5857aa68bc3)), closes [#12161](https://github.com/bitnami/charts/issues/12161) [#12170](https://github.com/bitnami/charts/issues/12170)

## <small>10.3.1 (2022-08-23)</small>

* [bitnami/rabbitmq] Update Chart.lock (#12070) ([7016384](https://github.com/bitnami/charts/commit/7016384eca36d25648d7f033a38a9c1f8161c9fe)), closes [#12070](https://github.com/bitnami/charts/issues/12070)

## 10.3.0 (2022-08-22)

* [bitnami/rabbitmq] Add support for image digest apart from tag (#11938) ([704644f](https://github.com/bitnami/charts/commit/704644f4f97ba23765d13659fe8361d8a47633e6)), closes [#11938](https://github.com/bitnami/charts/issues/11938)

## <small>10.2.1 (2022-08-10)</small>

* [bitnami/rabbitmq] Revert rabbitmq probes (#11177) ([1ae90e5](https://github.com/bitnami/charts/commit/1ae90e5fbcd3e96a04d03b950c2b821c29976cb6)), closes [#11177](https://github.com/bitnami/charts/issues/11177) [#11117](https://github.com/bitnami/charts/issues/11117)

## 10.2.0 (2022-08-10)

* [bitnami/rabbitmq] Add init scripts support (#11013) ([a5ae293](https://github.com/bitnami/charts/commit/a5ae293a8ecea13757c8678221491c6809ed52e7)), closes [#11013](https://github.com/bitnami/charts/issues/11013)

## <small>10.1.19 (2022-08-09)</small>

* [bitnami/rabbitmq] Release 10.1.19 (#11668) ([d2cb921](https://github.com/bitnami/charts/commit/d2cb92189e5ec8ba3f165752f622ffcfbf7dc598)), closes [#11668](https://github.com/bitnami/charts/issues/11668)
* Document data layout change in RabbitMQ 7.0.0 (#11461) ([08ac2ae](https://github.com/bitnami/charts/commit/08ac2ae2ee6980225effab70dd19a9fbf67346cc)), closes [#11461](https://github.com/bitnami/charts/issues/11461)

## <small>10.1.18 (2022-08-04)</small>

* [bitnami/rabbitmq] Release 10.1.18 (#11596) ([ff20ed7](https://github.com/bitnami/charts/commit/ff20ed764a43c39ee469ad1923502731c5c791f9)), closes [#11596](https://github.com/bitnami/charts/issues/11596)

## <small>10.1.17 (2022-08-04)</small>

* [bitnami/*] Update URLs to point to the new bitnami/containers monorepo (#11352) ([d665af0](https://github.com/bitnami/charts/commit/d665af0c708846192d8d5fb2f5f9ea65dd464ab0)), closes [#11352](https://github.com/bitnami/charts/issues/11352)
* [bitnami/rabbitmq] Release 10.1.17 (#11512) ([3e7a378](https://github.com/bitnami/charts/commit/3e7a378f2a8c5d2bf329b4be44463f94186b959f)), closes [#11512](https://github.com/bitnami/charts/issues/11512)

## <small>10.1.16 (2022-07-25)</small>

* [bitnami/rabbitmq] Fix volume-permissions initContainer if runAsUser is auto (#11300) ([1d59ac4](https://github.com/bitnami/charts/commit/1d59ac4394f68177dd349a2008675448bc82f081)), closes [#11300](https://github.com/bitnami/charts/issues/11300)

## <small>10.1.15 (2022-07-22)</small>

* [bitnami/rabbitmq] Release 10.1.15 (#11310) ([50463f3](https://github.com/bitnami/charts/commit/50463f3ade899b86d907076b0a60524b3d2ef76f)), closes [#11310](https://github.com/bitnami/charts/issues/11310)

## <small>10.1.14 (2022-07-12)</small>

* [bitnami/rabbitmq] Fix high CPU usage while idle (#11117) ([73966c6](https://github.com/bitnami/charts/commit/73966c67ff6d3386721cf1f42a6493f961c56792)), closes [#11117](https://github.com/bitnami/charts/issues/11117) [#11116](https://github.com/bitnami/charts/issues/11116)

## <small>10.1.13 (2022-07-08)</small>

* [bitnami/rabbitmq] Allow set custom logging (#11049) ([5393b96](https://github.com/bitnami/charts/commit/5393b963fe519fea00312966ffae00de6e7304ce)), closes [#11049](https://github.com/bitnami/charts/issues/11049)

## <small>10.1.12 (2022-07-01)</small>

* [bitnami/rabbitmq] Allow to customize volumePermissions.containerSecurityContext.runAsUser (#10901) ([bec65c1](https://github.com/bitnami/charts/commit/bec65c1eb1e7c647299f32f11951e4911ca87d6c)), closes [#10901](https://github.com/bitnami/charts/issues/10901)

## <small>10.1.11 (2022-06-30)</small>

* [bitnami/rabbitmq] Release 10.1.11 (#10957) ([5f40d6b](https://github.com/bitnami/charts/commit/5f40d6b5fa002caea6163231f345dd032c806c8f)), closes [#10957](https://github.com/bitnami/charts/issues/10957)

## <small>10.1.10 (2022-06-27)</small>

* Reverts PR #10042 (#10886) ([ea8b0cf](https://github.com/bitnami/charts/commit/ea8b0cfc12131eb87479e8b3dd4cd96b64b8403e)), closes [#10042](https://github.com/bitnami/charts/issues/10042) [#10886](https://github.com/bitnami/charts/issues/10886)

## <small>10.1.9 (2022-06-22)</small>

* [bitnami/rabbitmq] Use short DNS names for clustering (#10042) ([5858dd5](https://github.com/bitnami/charts/commit/5858dd5508e10205412549ead1845e17b2526cd4)), closes [#10042](https://github.com/bitnami/charts/issues/10042)

## <small>10.1.8 (2022-06-15)</small>

* [bitnami/rabbitmq] fix: Typo in the port name of rabbitmq notes (#10688) ([45ef50f](https://github.com/bitnami/charts/commit/45ef50ff34022512ed684c41e10f3a18ecab5f8c)), closes [#10688](https://github.com/bitnami/charts/issues/10688)

## <small>10.1.7 (2022-06-11)</small>

* [bitnami/rabbitmq] Release 10.1.7 updating components versions ([b13fc76](https://github.com/bitnami/charts/commit/b13fc7658d2603479b2340e9a6774436cd2ba240))

## <small>10.1.6 (2022-06-10)</small>

* [bitnami/*] Replace Kubeapps URL in READMEs (and kubeapps Chart.yaml) and remove BKPR references (#1 ([c6a7914](https://github.com/bitnami/charts/commit/c6a7914361e5aea6016fb45bf4d621edfd111d32)), closes [#10600](https://github.com/bitnami/charts/issues/10600)
* [bitnami/rabbitmq] Release 10.1.6 updating components versions ([12544dc](https://github.com/bitnami/charts/commit/12544dcd657883cfd4c8ba070827360bcb59a290))

## <small>10.1.5 (2022-06-06)</small>

* [bitnami/rabbitmq] Release 10.1.5 updating components versions ([58525ea](https://github.com/bitnami/charts/commit/58525eade1e1a43e59f90a0a9cd7b98440ee26d2))

## <small>10.1.4 (2022-06-03)</small>

* [bitnami/rabbitmq] Release 10.1.4 updating components versions ([09ed917](https://github.com/bitnami/charts/commit/09ed917149b8907709dcddcb46eb28a0c7277c44))

## <small>10.1.3 (2022-06-03)</small>

* [bitnami/rabbitmq] fix: extraSecretsPrependReleaseName (#10558) ([8f37a00](https://github.com/bitnami/charts/commit/8f37a003504188315e0e821184d9fccb01b6df6e)), closes [#10558](https://github.com/bitnami/charts/issues/10558)
* [bitnami/several] Replace maintainers email by url (#10523) ([ff3cf61](https://github.com/bitnami/charts/commit/ff3cf617a1680509b0f3855d17c4ccff7b29a0ff)), closes [#10523](https://github.com/bitnami/charts/issues/10523)

## <small>10.1.2 (2022-06-01)</small>

* [bitnami/rabbitmq] Release 10.1.2 updating components versions ([3fa150e](https://github.com/bitnami/charts/commit/3fa150e170b44f86f7949779ac66e8fcb4cee6e6))

## <small>10.1.1 (2022-05-30)</small>

* [bitnami/several] Replace base64 --decode with base64 -d (#10495) ([099286a](https://github.com/bitnami/charts/commit/099286ae7a87784cf809df0b64ab24f7ff0144c8)), closes [#10495](https://github.com/bitnami/charts/issues/10495)

## 10.1.0 (2022-05-30)

* [bitnami/rabbitmq] LDAP standardisation for LDAP (#10451) ([cebd4aa](https://github.com/bitnami/charts/commit/cebd4aa70312bbd4f4da2763be41e60752d461d7)), closes [#10451](https://github.com/bitnami/charts/issues/10451)

## <small>10.0.1 (2022-05-27)</small>

* [bitnami/rabbitmq] Release 10.0.1 updating components versions ([db7d2b2](https://github.com/bitnami/charts/commit/db7d2b273b36bd55a5787a58740848f0367c92cb))

## 10.0.0 (2022-05-26)

* [bitnami/rabbitmq] feat!: :arrow_up: Bump default image to 3.10.x (#10401) ([ea20500](https://github.com/bitnami/charts/commit/ea20500394fdacc86eef38a184a9613861256736)), closes [#10401](https://github.com/bitnami/charts/issues/10401)

## <small>9.1.4 (2022-05-21)</small>

* [bitnami/rabbitmq] Release 9.1.4 updating components versions ([783edd7](https://github.com/bitnami/charts/commit/783edd7bcbc8ffdfbc0d75421c27e38e9f6f9e92))

## <small>9.1.3 (2022-05-20)</small>

* [bitnami/rabbitmq] Release 9.1.3 updating components versions ([daa8e96](https://github.com/bitnami/charts/commit/daa8e96fb32ac2e763414c4026088afb792a0968))

## <small>9.1.2 (2022-05-19)</small>

* [bitnami/rabbitmq] Release 9.1.2 updating components versions ([0dac623](https://github.com/bitnami/charts/commit/0dac623318a4f06c282b70ffa67ac498fe7fc126))

## <small>9.1.1 (2022-05-18)</small>

* [bitnami/rabbitmq] Release 9.1.1 updating components versions ([d4891f2](https://github.com/bitnami/charts/commit/d4891f2a1552bed902dfa478d4e57714e757168c))

## 9.1.0 (2022-05-16)

*  [bitnami/rabbitmq] helm install ignores setting clusterIP when using service type LoadBalancer  (#1 ([6be0864](https://github.com/bitnami/charts/commit/6be086471125d807783511e44b6c76c3f58e5b09)), closes [#10176](https://github.com/bitnami/charts/issues/10176)
* [bitnami/*] add ingress extraRules feature (#10253) ([0f6cbb9](https://github.com/bitnami/charts/commit/0f6cbb9099b0e56685cc1d36ba50340f3d7278a1)), closes [#10253](https://github.com/bitnami/charts/issues/10253)

## <small>9.0.8 (2022-05-13)</small>

* [bitnami/*] Remove old 'ci' files (#10171) ([5df30c4](https://github.com/bitnami/charts/commit/5df30c44dbd1812da8786579ce4a94917d46a6ad)), closes [#10171](https://github.com/bitnami/charts/issues/10171)
* [bitnami/*] Unify k8s directives separators (#10185) ([2650214](https://github.com/bitnami/charts/commit/26502141d146ca3bdfb3bf744fcdec8ca5cece44)), closes [#10185](https://github.com/bitnami/charts/issues/10185)

## <small>9.0.7 (2022-05-11)</small>

* [bitnami/*] Fix typo in comments (relay -> rely) (#10151) ([9cfe4a4](https://github.com/bitnami/charts/commit/9cfe4a48cc35851faea6be7ffb2a978d223befa0)), closes [#10151](https://github.com/bitnami/charts/issues/10151)

## <small>9.0.6 (2022-05-11)</small>

* [bitnami/rabbitmq] Fixed missing `$` in secrets (#10108) ([6362170](https://github.com/bitnami/charts/commit/63621702a9470d12f65f4eac9e0c148c114655bf)), closes [#10108](https://github.com/bitnami/charts/issues/10108)

## <small>9.0.5 (2022-05-11)</small>

* [bitnami/rabbitmq] Release 9.0.5 updating components versions ([97d3e91](https://github.com/bitnami/charts/commit/97d3e9195df7ea94b2d1024e29ca01bc122665bf))

## <small>9.0.4 (2022-05-10)</small>

* [bitnami/rabbitmq] reuse secret data during upgrade with look up (#9724) ([03b72e6](https://github.com/bitnami/charts/commit/03b72e67f3c9f95c63d38f338cafd7d628e55e3c)), closes [#9724](https://github.com/bitnami/charts/issues/9724)

## <small>9.0.3 (2022-05-06)</small>

* [bitnami/rabbitmq] Add '-r' to volumePermissions to not fail on empty xargs (#10053) ([e7d4f2f](https://github.com/bitnami/charts/commit/e7d4f2f8949564d068aa3d0730d711ec8386e9e3)), closes [#10053](https://github.com/bitnami/charts/issues/10053)

## <small>9.0.2 (2022-05-05)</small>

* [bitnami/rabbitmq] Use short dns name for the Kubernetes api (#10019) ([ea783e2](https://github.com/bitnami/charts/commit/ea783e289bea9cb28141cdd7f45f448da0a9192e)), closes [#10019](https://github.com/bitnami/charts/issues/10019)

## <small>9.0.1 (2022-05-03)</small>

* [bitnami/rabbitmq] Fix metrics service port value (#9987) ([76de19e](https://github.com/bitnami/charts/commit/76de19e3c345772ec116b435c2daf1cdfe61158b)), closes [#9987](https://github.com/bitnami/charts/issues/9987)

## 9.0.0 (2022-04-28)

* [bitnami/rabbitmq] Chart standardised (#9817) ([3315cbd](https://github.com/bitnami/charts/commit/3315cbd9f3aeb20d23ace1e5bf5369659db93028)), closes [#9817](https://github.com/bitnami/charts/issues/9817)

## <small>8.32.2 (2022-04-27)</small>

* [bitnami/rabbitmq] Release 8.32.2 updating components versions ([d5f2aee](https://github.com/bitnami/charts/commit/d5f2aee2f8d54b4df3ddaa27dcb9cb502dc0bff1))

## <small>8.32.1 (2022-04-22)</small>

* Fixed ingress bug with extraHosts in RabbitMQ chart. (#9843) ([aad0a78](https://github.com/bitnami/charts/commit/aad0a78726f2c59300169ec68a6ef4945d69922e)), closes [#9843](https://github.com/bitnami/charts/issues/9843)

## 8.32.0 (2022-04-20)

* [bitnami/rabbitmq] Add dnsConfig option to rabbitmq (#9810) ([7d6311c](https://github.com/bitnami/charts/commit/7d6311c550316c2321db31e76834b87d5c5ac339)), closes [#9810](https://github.com/bitnami/charts/issues/9810)

## <small>8.31.7 (2022-04-20)</small>

* [bitnami/rabbitmq] Release 8.31.7 updating components versions ([ab38a81](https://github.com/bitnami/charts/commit/ab38a81d8c2a02d8c1137298b9b9d9a751b0dd0b))

## <small>8.31.6 (2022-04-19)</small>

* [bitnami/rabbitmq] Release 8.31.6 updating components versions ([4adbc6c](https://github.com/bitnami/charts/commit/4adbc6c7799ebb8a201994fea8b0fead8574afee))

## <small>8.31.5 (2022-04-18)</small>

* [bitnami/rabbitmq] Fix AMQP port installation notes (#9813) ([220c243](https://github.com/bitnami/charts/commit/220c2437403da1370442ecd66b6fc1c7f047d072)), closes [#9813](https://github.com/bitnami/charts/issues/9813)

## <small>8.31.4 (2022-04-13)</small>

* [bitnami/rabbitmq] Release 8.31.4 updating components versions ([f256d28](https://github.com/bitnami/charts/commit/f256d281d17db5a595ddc6cdfa06bd1a30cbf70b))

## <small>8.31.3 (2022-04-07)</small>

* [bitnami/rabbitmq] Release 8.31.3 updating components versions ([ca58d0c](https://github.com/bitnami/charts/commit/ca58d0ce5c063c12ad094a09b147cf842836d435))

## <small>8.31.2 (2022-04-02)</small>

* [bitnami/rabbitmq] Release 8.31.2 updating components versions ([d1bb9eb](https://github.com/bitnami/charts/commit/d1bb9ebf5573f35dfad8b5b4e4bf9d0262afedb6))

## <small>8.31.1 (2022-04-02)</small>

* [bitnami/rabbitmq] Release 8.31.1 updating components versions ([3a328ef](https://github.com/bitnami/charts/commit/3a328efd677e6e2d32b62ec7428cc9ac8559b016))

## 8.31.0 (2022-04-01)

* [bitnami/rabbitmq] Allow both definitions and setting credentials (#9638) ([ac845cb](https://github.com/bitnami/charts/commit/ac845cb5131c053b9413780428a17a1f85417df0)), closes [#9638](https://github.com/bitnami/charts/issues/9638)

## <small>8.30.4 (2022-03-28)</small>

* [bitnami/rabbitmq] Release 8.30.4 updating components versions ([2016a03](https://github.com/bitnami/charts/commit/2016a03ede1045e19b9ea016d9d496d976996661))

## <small>8.30.3 (2022-03-27)</small>

* [bitnami/rabbitmq] Release 8.30.3 updating components versions ([2b8e2b3](https://github.com/bitnami/charts/commit/2b8e2b33d3eaf1d4e5a92b1b7c2246e4c442a5d2))

## <small>8.30.2 (2022-03-22)</small>

* [bitnami/rabbitmq] Release 8.30.2 updating components versions ([8bfdcda](https://github.com/bitnami/charts/commit/8bfdcda73725f71befe6964d5bd2f4197e45abfa))

## <small>8.30.1 (2022-03-16)</small>

* [bitnami/rabbitmq] Release 8.30.1 updating components versions ([935d857](https://github.com/bitnami/charts/commit/935d857683f0230bf06457b58aa79e7be85f2f5a))

## 8.30.0 (2022-03-04)

* [bitnami/rabbitmq] Add support for ingress alt backend (#9276) ([bfd233a](https://github.com/bitnami/charts/commit/bfd233af512c0e388783923065df0e1f7cbf8cc2)), closes [#9276](https://github.com/bitnami/charts/issues/9276)

## <small>8.29.3 (2022-02-27)</small>

* [bitnami/rabbitmq] Release 8.29.3 updating components versions ([67fd7bb](https://github.com/bitnami/charts/commit/67fd7bb2719466de0bc317d1ee4017d264fbbd86))

## <small>8.29.2 (2022-02-24)</small>

* [bitnami/rabbitmq] Release 8.29.2 updating components versions ([6a41a6f](https://github.com/bitnami/charts/commit/6a41a6f3d416d94dbc0e5a49c92e7a33e35a5253))

## <small>8.29.1 (2022-02-20)</small>

* [bitnami/rabbitmq] Release 8.29.1 updating components versions ([72e4509](https://github.com/bitnami/charts/commit/72e450920156df3badbb1b8fa81a33c87ad0e8be))

## 8.29.0 (2022-02-10)

* [bitnami/rabbitmq] Add support for custom mountPath and subpath (#8939) ([694e7f2](https://github.com/bitnami/charts/commit/694e7f260e2eb4c09dfdd76ab625273cc150e231)), closes [#8939](https://github.com/bitnami/charts/issues/8939)
* Non utf8 chars (#8923) ([6ffd18f](https://github.com/bitnami/charts/commit/6ffd18fbbdf10e94ea1a90cf5b84ef610ac2a72d)), closes [#8923](https://github.com/bitnami/charts/issues/8923)

## <small>8.28.1 (2022-02-02)</small>

* [bitnami/*] Make use of new "common.ingress.certManagerRequest" helper (#8862) ([12e4c37](https://github.com/bitnami/charts/commit/12e4c3733eaeaa9a5579fdf917fa098a0f2aae23)), closes [#8862](https://github.com/bitnami/charts/issues/8862)

## 8.28.0 (2022-02-01)

* [bitnami/rabbitmq] fix: Remove postStart hook (#8855) ([adc6b3d](https://github.com/bitnami/charts/commit/adc6b3d0fcb4a068c447113da6d4df0468944dd2)), closes [#8855](https://github.com/bitnami/charts/issues/8855)

## 8.27.0 (2022-01-24)

* [bitnami/rabbitmq] Added option to disable epmd and dist port on service (#8750) ([74ede1e](https://github.com/bitnami/charts/commit/74ede1e4bb45d4d5f90b0dba6c298753bd947357)), closes [#8750](https://github.com/bitnami/charts/issues/8750)
* [bitnami/several] Change prerequisites (#8725) ([8d740c5](https://github.com/bitnami/charts/commit/8d740c566cfdb7e2d933c40128b4e919fce953a5)), closes [#8725](https://github.com/bitnami/charts/issues/8725)

## <small>8.26.3 (2022-01-19)</small>

* [bitnami/*] Update READMEs (#8716) ([b9a9533](https://github.com/bitnami/charts/commit/b9a953337590eb2979453385874a267bacf50936)), closes [#8716](https://github.com/bitnami/charts/issues/8716)
* [bitnami/rabbitmq] Release 8.26.3 updating components versions ([c071bb8](https://github.com/bitnami/charts/commit/c071bb84f4b38a687a0d1f4364f1231e75d1054b))

## <small>8.26.2 (2022-01-19)</small>

* [bitnami/*] Readme automation (#8579) ([78d1938](https://github.com/bitnami/charts/commit/78d193831c900d178198491ffd08fa2217a64ecd)), closes [#8579](https://github.com/bitnami/charts/issues/8579)
* [bitnami/rabbitmq] Release 8.26.2 updating components versions ([f1ea71c](https://github.com/bitnami/charts/commit/f1ea71c9feb73d35f6c47b2ec086d358c284c04c))

## <small>8.26.1 (2022-01-11)</small>

* [bitnami/rabbitmq] Release 8.26.1 updating components versions ([048e65e](https://github.com/bitnami/charts/commit/048e65ebf5b9e8871857d6576f660963b0ef1c47))

## 8.26.0 (2022-01-07)

* [bitnami/rabbitmq] Support for relabling and aligned service monitor with other charts (#8583) ([6e64d41](https://github.com/bitnami/charts/commit/6e64d41276df966c5eecfc666dea5aa48ef78f1e)), closes [#8583](https://github.com/bitnami/charts/issues/8583)

## <small>8.25.1 (2022-01-06)</small>

* [bitnami/rabbitmq] Release 8.25.1 updating components versions ([2a9e44a](https://github.com/bitnami/charts/commit/2a9e44a916e760068326e541f907687201417089))

## 8.25.0 (2022-01-05)

* [bitnami/several] Adapt templating format (#8562) ([8cad18a](https://github.com/bitnami/charts/commit/8cad18aed9966a6f0208e5ad6cee46cb217f47ab)), closes [#8562](https://github.com/bitnami/charts/issues/8562)
* [bitnami/several] Add license to the README ([05f7633](https://github.com/bitnami/charts/commit/05f763372501d596e57db713dd53ff4ff3027cc4))
* [bitnami/several] Add license to the README ([32fb238](https://github.com/bitnami/charts/commit/32fb238e60a0affc6debd3142eaa3c3d9089ec2a))
* [bitnami/several] Add license to the README ([b87c2f7](https://github.com/bitnami/charts/commit/b87c2f7899d48a8b02c506765e6ae82937e9ba3f))

## <small>8.24.13 (2022-01-01)</small>

* [bitnami/rabbitmq] Release 8.24.13 updating components versions ([d1680a5](https://github.com/bitnami/charts/commit/d1680a524f65c0194fec188c77eb35a9cec0cd4b))
* RabbitMQ chart copy updates (#8432) ([99b480d](https://github.com/bitnami/charts/commit/99b480d0bc677ccfb549cde7d463950709d30903)), closes [#8432](https://github.com/bitnami/charts/issues/8432)

## <small>8.24.12 (2021-12-13)</small>

* [bitnami/RabbitMQ] Allow to set nodePort when type is LoadBalancer for all ports (#8369) ([4937d48](https://github.com/bitnami/charts/commit/4937d48a18b4c5580da07cdcf0cfe9dab2d262da)), closes [#8369](https://github.com/bitnami/charts/issues/8369)

## <small>8.24.11 (2021-12-09)</small>

* [bitnami/rabbitmq] fix error with scope of .Values in secrets template (#8339) ([8ca5052](https://github.com/bitnami/charts/commit/8ca50529ffead382fa8651ee1d8fce9e1c03a1a6)), closes [#8339](https://github.com/bitnami/charts/issues/8339)

## <small>8.24.10 (2021-12-07)</small>

* [bitnami/rabbitmq] fix common annotations in secrets template (#8329) ([c093be3](https://github.com/bitnami/charts/commit/c093be3a588f48dc53b7b3aed24fcd3a86e646bc)), closes [#8329](https://github.com/bitnami/charts/issues/8329)

## <small>8.24.9 (2021-12-02)</small>

* [bitnami/rabbitmq] Release 8.24.9 updating components versions ([2b811ea](https://github.com/bitnami/charts/commit/2b811ea50717dca79ea3ccb4def1c6810a3e5ec5))

## <small>8.24.8 (2021-11-29)</small>

* [bitnami/several] Regenerate README tables ([ac75243](https://github.com/bitnami/charts/commit/ac752431b90e935d0a4dbfef70dc44f24f3d3dd2))
* [bitnami/several] Replace HTTP by HTTPS when possible (#8259) ([eafb5bd](https://github.com/bitnami/charts/commit/eafb5bd5a2cc3aaf04fc1e8ebedd73f420d76864)), closes [#8259](https://github.com/bitnami/charts/issues/8259)

## <small>8.24.7 (2021-11-25)</small>

* [bitnami/rabbitmq] Release 8.24.7 updating components versions ([6681ad5](https://github.com/bitnami/charts/commit/6681ad5145e7b70ad2fb6c4a26346e49b32329c4))
* [bitnami/several] Fix deadlinks in README.md (#8215) ([99e90d2](https://github.com/bitnami/charts/commit/99e90d244b3244e059a42f72dcbecd3cda2b66bb)), closes [#8215](https://github.com/bitnami/charts/issues/8215)

## <small>8.24.6 (2021-11-23)</small>

* [bitnami/rabbitmq] PodDisruptionBudget: support apiVersion policy/v1 (#8018) ([0c03c7c](https://github.com/bitnami/charts/commit/0c03c7c5129640b983e14e5dc631077b4460586e)), closes [#8018](https://github.com/bitnami/charts/issues/8018)
* [bitnami/several] Regenerate README tables ([e8e4e6a](https://github.com/bitnami/charts/commit/e8e4e6a9f7c924bcdab52f269cb60dbfeef840a9))

## <small>8.24.5 (2021-11-21)</small>

* [bitnami/rabbitmq] Release 8.24.5 updating components versions ([8d1d19f](https://github.com/bitnami/charts/commit/8d1d19f56bf23d736316aa505288e7c022882337))

## <small>8.24.4 (2021-11-16)</small>

* [bitnami/rabbitmq] Set publishNotReadyAddresses to true in headless service (#8135) ([d82c934](https://github.com/bitnami/charts/commit/d82c934a1724c0736385d6fd6cca47a8c411e076)), closes [#8135](https://github.com/bitnami/charts/issues/8135)
* [bitnami/several] Regenerate README tables ([e6f742c](https://github.com/bitnami/charts/commit/e6f742cb88c330952cb52da038db61537ec921e5))

## <small>8.24.3 (2021-11-12)</small>

* [bitnami/rabbitmq] Release 8.24.3 updating components versions ([fb757a2](https://github.com/bitnami/charts/commit/fb757a278ff3addc465b8a5c1d65be10201597f1))
* [bitnami/several] Regenerate README tables ([412cf6a](https://github.com/bitnami/charts/commit/412cf6a513cb0c03444a6e7811c6f27193239a10))

## <small>8.24.2 (2021-10-27)</small>

* [bitnami/rabbitmq] Release 8.24.2 updating components versions ([f682c81](https://github.com/bitnami/charts/commit/f682c8141f0765eba97254e7bbe186a98d58f36a))

## <small>8.24.1 (2021-10-22)</small>

* [bitnami/several] Add chart info to NOTES.txt (#7889) ([a6751cd](https://github.com/bitnami/charts/commit/a6751cdd33c461fabbc459fbea6f219ec64ab6b2)), closes [#7889](https://github.com/bitnami/charts/issues/7889)

## 8.24.0 (2021-10-22)

* [bitnami/rabbimq] Set cluster_partition_handling as parameter in values.yaml (#7874) ([5597ce8](https://github.com/bitnami/charts/commit/5597ce8549c4a20f3b0951effc4dcfa7fcc0d6d6)), closes [#7874](https://github.com/bitnami/charts/issues/7874)
* [bitnami/several] Regenerate README tables ([73cabea](https://github.com/bitnami/charts/commit/73cabea54a941d2239accd98e8df6cbfc9fa8d3c))

## <small>8.23.4 (2021-10-20)</small>

* [bitnami/rabbitmq] Release 8.23.4 updating components versions ([10e4358](https://github.com/bitnami/charts/commit/10e4358a36798b713a9b01591d3ea1d44f5a9108))

## <small>8.23.3 (2021-10-19)</small>

* [bitnami/several] Change pullPolicy for bitnami-shell image (#7852) ([9711a33](https://github.com/bitnami/charts/commit/9711a33c6eec72ea79143c4b7574dbe6a148d6b2)), closes [#7852](https://github.com/bitnami/charts/issues/7852)

## <small>8.23.2 (2021-10-19)</small>

* [bitnami/rabbitmq] Fix default for topologySpreadConstraints (#7843) ([ede4f2b](https://github.com/bitnami/charts/commit/ede4f2b529bed64b761955ebf61b19dd4d075535)), closes [#7843](https://github.com/bitnami/charts/issues/7843)

## <small>8.23.1 (2021-10-14)</small>

* [bitnami/*] Generate READMEs (#7790) ([0833a8c](https://github.com/bitnami/charts/commit/0833a8c16300d68abf6030639c3479d8fb031e25)), closes [#7790](https://github.com/bitnami/charts/issues/7790)
* bitnami/rabbitmq - Fix management port when service type is LoadBalancer (#7798) ([6f3e953](https://github.com/bitnami/charts/commit/6f3e9535f71ead1bcf4d2498dddab58982ba2472)), closes [#7798](https://github.com/bitnami/charts/issues/7798)

## 8.23.0 (2021-10-13)

* [bitnami/rabbitmq] Added option to configure automounting service credentials (#7745) ([6592de6](https://github.com/bitnami/charts/commit/6592de659b3cffad069f3a1f581cb78b3648ccdc)), closes [#7745](https://github.com/bitnami/charts/issues/7745)

## <small>8.22.4 (2021-09-30)</small>

* [bitnami/*] Drop support for deprecated cert-manager annotation (#7656) ([bbf403b](https://github.com/bitnami/charts/commit/bbf403bae78be53430fbf13e462f9bd1eeb5bf04)), closes [#7656](https://github.com/bitnami/charts/issues/7656)
* [bitnami/several] Regenerate README tables ([ff170d1](https://github.com/bitnami/charts/commit/ff170d10f8aa6dae0f1e5c3f7d1c69fcec96b731))

## <small>8.22.3 (2021-09-24)</small>

* [bitnami/*] Generate READMEs with new generator version (#7614) ([e5ab2e6](https://github.com/bitnami/charts/commit/e5ab2e6ecdd6bce800863f154cda524ff9f6c117)), closes [#7614](https://github.com/bitnami/charts/issues/7614)
* [bitnami/rabbitmq] Release 8.22.3 updating components versions ([d5301af](https://github.com/bitnami/charts/commit/d5301afca60bd6755d46b968dd8f76cedf0e90da))
* [bitnami/several] Regenerate README tables ([003a0fb](https://github.com/bitnami/charts/commit/003a0fbaedeb775c546b8d8452b7a5ab0a63af52))

## <small>8.22.2 (2021-09-17)</small>

* [bitnami/rabbitmq] Release 8.22.2 updating components versions ([b7c979a](https://github.com/bitnami/charts/commit/b7c979a643cad7b356d66984b21fad8bb877bb5d))
* [bitnami/several] Regenerate README tables ([5c79422](https://github.com/bitnami/charts/commit/5c7942235d3169332e8dadb380cd757416f28946))

## <small>8.22.1 (2021-09-14)</small>

* [bitnami/rabbitmq] Fix typo on values.yaml (#7477) ([ae013f8](https://github.com/bitnami/charts/commit/ae013f8b56b3ea52d996a3e03b25e116e0f92d80)), closes [#7477](https://github.com/bitnami/charts/issues/7477)
* [bitnami/several] Regenerate README tables ([dcb935c](https://github.com/bitnami/charts/commit/dcb935c1bf066b6d8988f3b0dbe85d01aa01b215))

## 8.22.0 (2021-09-08)

* [bitnami/rabbitmq] Add persistence annotations to PVC (#7380) ([457a3e5](https://github.com/bitnami/charts/commit/457a3e526c30dd51f9beafd8ed1165b468d22666)), closes [#7380](https://github.com/bitnami/charts/issues/7380)
* [bitnami/several] Regenerate README tables ([8c2dfde](https://github.com/bitnami/charts/commit/8c2dfde7724141adfb90f0fa6bb97bf9acd4d14e))

## 8.21.0 (2021-09-02)

* [bitnami/rabbitmq] Add upgrade note for rabbitmq 3.9 (#7161) ([ba5e368](https://github.com/bitnami/charts/commit/ba5e36845d00ccb7513d4f5ed7a3c70ce5a1529e)), closes [#7161](https://github.com/bitnami/charts/issues/7161)
* [bitnami/several] Regenerate README tables ([64d5d74](https://github.com/bitnami/charts/commit/64d5d747b84299ca9f63ea8a586b13870abe31a6))

## <small>8.20.5 (2021-08-28)</small>

* [bitnami/rabbitmq] Release 8.20.5 updating components versions ([9dcc8d1](https://github.com/bitnami/charts/commit/9dcc8d19475effba3d5dabe16b94b3830fc14df2))
* [bitnami/several] Regenerate README tables ([da2513b](https://github.com/bitnami/charts/commit/da2513bf0a33819f3b1151d387c631a9ffdb03e2))

## <small>8.20.4 (2021-08-26)</small>

* [bitnami/rabbitmq] Release 8.20.4 updating components versions ([654d15a](https://github.com/bitnami/charts/commit/654d15af542e3853f9af57ac4754038ee1db4b40))

## <small>8.20.3 (2021-08-25)</small>

* [bitnami/rabbitmq] Release 8.20.3 updating components versions ([af5931b](https://github.com/bitnami/charts/commit/af5931b9fee7960a47991581e88f2461dc71e83e))

## <small>8.20.2 (2021-08-20)</small>

* bitnami/rabbitmq: enable tpl for auth.tls.existingSecret (#7276) ([20e83cb](https://github.com/bitnami/charts/commit/20e83cb07871e6f9dfcca21cab48f026f0e60ff6)), closes [#7276](https://github.com/bitnami/charts/issues/7276)

## <small>8.20.1 (2021-08-11)</small>

* [bitnami/rabbitmq] Release 8.20.1 updating components versions ([22f189d](https://github.com/bitnami/charts/commit/22f189d36c70da0f46733fd5da548f6ea6ec5d28))

## 8.20.0 (2021-08-10)

* [bitnami/rabbitmq] add service.portEnabled (#7150) ([1534730](https://github.com/bitnami/charts/commit/1534730ecae0c6a7320ac3157b4af893f9ebb368)), closes [#7150](https://github.com/bitnami/charts/issues/7150)

## <small>8.19.3 (2021-08-09)</small>

* [bitnami/rabbitmq] Release 8.19.3 updating components versions ([e3e8ddf](https://github.com/bitnami/charts/commit/e3e8ddfd25bbb50f17930a560967966be97ec4ba))

## <small>8.19.2 (2021-08-04)</small>

* [bitnami/rabbitmq] Release 8.19.2 updating components versions ([622c582](https://github.com/bitnami/charts/commit/622c582db3a89bdc9f292c102bb7983ab546752e))

## <small>8.19.1 (2021-07-30)</small>

* [bitnami/rabbitmq] Fix wrong link in README (#7087) ([785aa61](https://github.com/bitnami/charts/commit/785aa619c30aa376993979be0d042ae722b0f83e)), closes [#7087](https://github.com/bitnami/charts/issues/7087)
* [bitnami/several] Fix default values when using `foo: |` (#7092) ([fe91297](https://github.com/bitnami/charts/commit/fe91297fdf3f6c74aee31c423912e4ac19b55c94)), closes [#7092](https://github.com/bitnami/charts/issues/7092)

## 8.19.0 (2021-07-27)

* [bitnami/several] Add diagnostic mode (#7012) ([f1344b0](https://github.com/bitnami/charts/commit/f1344b0361c5a93bf971d08f0fc64d3c8588cbf9)), closes [#7012](https://github.com/bitnami/charts/issues/7012)

## <small>8.18.1 (2021-07-20)</small>

* [bitnami/several] Fix default values and regenerate README (II) (#6994) ([d095793](https://github.com/bitnami/charts/commit/d0957937c764187307b924f600247c2b7afaf536)), closes [#6994](https://github.com/bitnami/charts/issues/6994)

## 8.18.0 (2021-07-16)

* [bitnami/rabbitmq] add extraDeploy support (#6926) (#6978) ([d3da8aa](https://github.com/bitnami/charts/commit/d3da8aab1d9031a872254e528d1fcbc346221f77)), closes [#6926](https://github.com/bitnami/charts/issues/6926) [#6978](https://github.com/bitnami/charts/issues/6978)

## 8.17.0 (2021-07-15)

* bitnami/rabbitmq: add support for ingressClassName (#6905) ([f5c4490](https://github.com/bitnami/charts/commit/f5c4490afbd454f2042af03a5dda1e3251350a71)), closes [#6905](https://github.com/bitnami/charts/issues/6905) [#6905](https://github.com/bitnami/charts/issues/6905)

## <small>8.16.4 (2021-07-14)</small>

* [bitnami/*] Adapt values.yaml of PrestaShop, PyTorch and RabbitMQ charts (#6939) ([0a9d4c7](https://github.com/bitnami/charts/commit/0a9d4c7fb9096443393b05ee2a0c7572df9e5ba2)), closes [#6939](https://github.com/bitnami/charts/issues/6939)

## <small>8.16.3 (2021-07-09)</small>

* [bitnami/rabbitmq] Fix default empty "initContainers" object (#6902) ([110c55a](https://github.com/bitnami/charts/commit/110c55a18cd313c11a998d850beaebafe2cd89af)), closes [#6902](https://github.com/bitnami/charts/issues/6902)

## <small>8.16.2 (2021-07-06)</small>

* [bitnami/rabbitmq] Release 8.16.2 updating components versions ([7dd4831](https://github.com/bitnami/charts/commit/7dd4831617593ee5c7eedebf0f98d9b82f88c842))

## <small>8.16.1 (2021-06-27)</small>

* [bitnami/rabbitmq] Release 8.16.1 updating components versions ([b9c0831](https://github.com/bitnami/charts/commit/b9c083144bb7551e8ad377dc23e884ec9f550d3b))

## 8.16.0 (2021-06-16)

* [bitnami/rabbitmq] Add support for autogenerated certs (#6528) ([2c88d0b](https://github.com/bitnami/charts/commit/2c88d0bdef9f005a7ff98f9e3f279067601ee721)), closes [#6528](https://github.com/bitnami/charts/issues/6528)

## <small>8.15.3 (2021-06-11)</small>

* [bitnami/rabbitmq] Release 8.15.3 updating components versions ([8596db6](https://github.com/bitnami/charts/commit/8596db6862ce9a223a4460803b67d0071bb2fe39))

## <small>8.15.2 (2021-05-28)</small>

* [bitnami/rabbitmq] Release 8.15.2 updating components versions ([99b28de](https://github.com/bitnami/charts/commit/99b28debfe080da09f66dfcf9883460b67fe2b68))

## <small>8.15.1 (2021-05-23)</small>

* [bitnami/rabbitmq] Release 8.15.1 updating components versions ([d041833](https://github.com/bitnami/charts/commit/d041833338de7ebc6e26523fd37e8e087511eb95))

## 8.15.0 (2021-05-14)

* [bitnami/rabbitmq] add commonAnnotations to values.yaml to apply it to all deployed resources (#6359 ([395934b](https://github.com/bitnami/charts/commit/395934bfc46804f5aa1b96d2fca416c0d5bfa02e)), closes [#6359](https://github.com/bitnami/charts/issues/6359)

## <small>8.14.1 (2021-05-12)</small>

* [bitnami/rabbitmq] change the dafult port to tlsPort (#6331) ([9920a6f](https://github.com/bitnami/charts/commit/9920a6f5bdc2fdf3829201603fc8439ccf43cf0b)), closes [#6331](https://github.com/bitnami/charts/issues/6331) [#6330](https://github.com/bitnami/charts/issues/6330) [#6260](https://github.com/bitnami/charts/issues/6260) [#6325](https://github.com/bitnami/charts/issues/6325) [#6324](https://github.com/bitnami/charts/issues/6324)

## 8.14.0 (2021-05-11)

* [bitnami/rabbitmq] feat: servicemonitor and ingress configuration (#6260) ([07ab097](https://github.com/bitnami/charts/commit/07ab097836bd7a3705926a5cacb80a080d8f82f8)), closes [#6260](https://github.com/bitnami/charts/issues/6260)

## <small>8.13.1 (2021-05-05)</small>

* [bitnami/rabbitmq] Release 8.13.1 updating components versions ([4f6076e](https://github.com/bitnami/charts/commit/4f6076edb1a353af27fbb3ca1e1b9cab5166e596))

## 8.13.0 (2021-05-04)

* [bitnami/rabbitmq] Improve Ingress TLS management (#6235) ([29ae259](https://github.com/bitnami/charts/commit/29ae25949201f1cb5de58ae5aaa001ae40858cc8)), closes [#6235](https://github.com/bitnami/charts/issues/6235)

## <small>8.12.3 (2021-05-04)</small>

* [bitnami/rabbitmq] Release 8.12.3 updating components versions ([69645ae](https://github.com/bitnami/charts/commit/69645ae471aa21a655b650d00176ea39795edf87))

## <small>8.12.2 (2021-04-30)</small>

* Fix typos in several README files (#6252) ([fd16565](https://github.com/bitnami/charts/commit/fd1656587a007ac9b8e9d895f6b99607fb225f7c)), closes [#6252](https://github.com/bitnami/charts/issues/6252)

## <small>8.12.1 (2021-04-28)</small>

* [bitnami/rabbitmq] Release 8.12.1 updating components versions ([da17ec2](https://github.com/bitnami/charts/commit/da17ec23647498cef61c66c6b3ea037a02d9cb3e))

## 8.12.0 (2021-04-26)

* [bitnami/rabbitmq]: allow to specify targetLabels on servicemonitor (#6202) ([a6824ea](https://github.com/bitnami/charts/commit/a6824ea56e762ed3ed7eac08218c886b3700fa94)), closes [#6202](https://github.com/bitnami/charts/issues/6202)

## <small>8.11.9 (2021-04-19)</small>

* [bitnami/rabbitmq] delete release in values (#6149) ([80f51a5](https://github.com/bitnami/charts/commit/80f51a57d795b203ca129cfa1894655f247458ef)), closes [#6149](https://github.com/bitnami/charts/issues/6149)

## <small>8.11.8 (2021-04-19)</small>

* [bitnami/rabbitmq] add option to disable clustering entirely (#6129) ([30bb3da](https://github.com/bitnami/charts/commit/30bb3da87a96aac4debbd3e7136527c2b78bd569)), closes [#6129](https://github.com/bitnami/charts/issues/6129)

## <small>8.11.7 (2021-04-15)</small>

* [bitnami/rabbitmq] Improved RabbitMQ README (#6105) ([dde65db](https://github.com/bitnami/charts/commit/dde65db3fd5b1497894e4bf43aa3f9be65370c23)), closes [#6105](https://github.com/bitnami/charts/issues/6105)

## <small>8.11.6 (2021-04-08)</small>

* [bitnami/rabbitmq] Fix crash setting up metric.podAnnotions to null (#6047) ([0ab4f88](https://github.com/bitnami/charts/commit/0ab4f88a4f42ab46a644693a4f0840fe833530c6)), closes [#6047](https://github.com/bitnami/charts/issues/6047)

## <small>8.11.5 (2021-03-29)</small>

* [bitnami/rabbitmq] Improve handling of load-definitions (#5939) ([44cc11d](https://github.com/bitnami/charts/commit/44cc11d5688aa36d0c90d1de7b772b487c446949)), closes [#5939](https://github.com/bitnami/charts/issues/5939)

## <small>8.11.4 (2021-03-23)</small>

* [bitnami/rabbitmq] Credentials during upgrade information in notes (#5871) ([78a1a10](https://github.com/bitnami/charts/commit/78a1a10eae089e7553494ba2dbdfc12f02601429)), closes [#5871](https://github.com/bitnami/charts/issues/5871)

## <small>8.11.3 (2021-03-04)</small>

* [bitnami/*] Remove minideb mentions (#5677) ([870bc4d](https://github.com/bitnami/charts/commit/870bc4dba1fc3aa55dd157da6687b25e8d352206)), closes [#5677](https://github.com/bitnami/charts/issues/5677)

## <small>8.11.2 (2021-03-03)</small>

* [bitnami/rabbitmq] Release 8.11.2 updating components versions ([2af94b9](https://github.com/bitnami/charts/commit/2af94b95c30abe5cc1f06be71ff488c98a34a64e))

## <small>8.11.1 (2021-02-27)</small>

* [bitnami/rabbitmq] Release 8.11.1 updating components versions ([0df7737](https://github.com/bitnami/charts/commit/0df7737b83be049b57dc214e605ed6a0ad81611f))

## 8.11.0 (2021-02-24)

* Added Topology Spread Constraints for pod assignment (#5599) ([a57b09b](https://github.com/bitnami/charts/commit/a57b09b68f9265a26b61795e3b7c10eb8dc84765)), closes [#5599](https://github.com/bitnami/charts/issues/5599)

## <small>8.10.2 (2021-02-22)</small>

* [bitnami/*] Use common macro to define RBAC apiVersion (#5585) ([71fb99f](https://github.com/bitnami/charts/commit/71fb99f541e971b1daafaa20ffb7d18b153b8d60)), closes [#5585](https://github.com/bitnami/charts/issues/5585)

## <small>8.10.1 (2021-02-18)</small>

* Add support for annotations on Rabbitmq Headless service (#5530) ([b1f12df](https://github.com/bitnami/charts/commit/b1f12dfce3f3b7ef3dfa8171081e011bd2160237)), closes [#5530](https://github.com/bitnami/charts/issues/5530)

## 8.10.0 (2021-02-17)

* [bitnami/rabbitmq] Add the RabbitMQ Manager port enable/disable functionality in the service definit ([8f8994b](https://github.com/bitnami/charts/commit/8f8994b3a5bf080e59dadd57b525b7d27d1dfde7)), closes [#5516](https://github.com/bitnami/charts/issues/5516)

## <small>8.9.3 (2021-02-17)</small>

* [bitnami/*] Add notice regarding parameters immutability after chart installation (#4853) ([5f09573](https://github.com/bitnami/charts/commit/5f095734f92555dec7cd0e3ee961f315eac170ff)), closes [#4853](https://github.com/bitnami/charts/issues/4853)
* [bitnami/rabbitmq] Add section to cover cluster recovery (#5455) ([b947148](https://github.com/bitnami/charts/commit/b9471482d1a804ee46efa07ecf766f1812f2a881)), closes [#5455](https://github.com/bitnami/charts/issues/5455)
* [bitnami/rabbitmq] Release 8.9.3 updating components versions ([8573a62](https://github.com/bitnami/charts/commit/8573a62618e8214c20a69e61128d8525ca4a00f2))

## <small>8.9.2 (2021-02-03)</small>

* [bitnami/rabbitmq] Add comment in ingress.path value (#5379) ([ce2f21d](https://github.com/bitnami/charts/commit/ce2f21db6c95923004145da5e98b123a6a798129)), closes [#5379](https://github.com/bitnami/charts/issues/5379)

## <small>8.9.1 (2021-01-29)</small>

* Fix template evaluation of podAnnotations (#5299) ([8745e16](https://github.com/bitnami/charts/commit/8745e16bf0bfbdaf8f1921092cde4cdd8d45c2b3)), closes [#5299](https://github.com/bitnami/charts/issues/5299)

## 8.9.0 (2021-01-28)

* [bitnami/rabbitmq] Add hostAliases (#5304) ([f8ffed1](https://github.com/bitnami/charts/commit/f8ffed1d6b7d9dbaa4e186674622a4abab5f053a)), closes [#5304](https://github.com/bitnami/charts/issues/5304)

## 8.8.0 (2021-01-27)

* [bitnami/rabbitmq] Fix forceBoot README typo (#5215) ([320dd74](https://github.com/bitnami/charts/commit/320dd745389f844dc70beb0d0607103eadd12dfe)), closes [#5215](https://github.com/bitnami/charts/issues/5215)
* [bitnami/rabbitmq]: 8.8.0 - adds auth.tls.existingSecretFullChain option (#3192) ([7932e1b](https://github.com/bitnami/charts/commit/7932e1b2a7d9387d5eb8d1e7ec455c6cda4fde93)), closes [#3192](https://github.com/bitnami/charts/issues/3192)

## <small>8.7.5 (2021-01-25)</small>

* [bitnami/rabbitmq] Release 8.7.5 updating components versions ([a3b6935](https://github.com/bitnami/charts/commit/a3b6935910d9d9fdca581911ee064cbdb81d4357))

## <small>8.7.4 (2021-01-22)</small>

* [bitnami/rabbitmq]: use managerPortName in ingress target port (#5184) ([de55fe1](https://github.com/bitnami/charts/commit/de55fe1cbfc8a9634e8836e9bf36a2d472a9f365)), closes [#5184](https://github.com/bitnami/charts/issues/5184)

## <small>8.7.3 (2021-01-20)</small>

* [bitnami/rabbitmq] Release 8.7.3 updating components versions ([b1bf54d](https://github.com/bitnami/charts/commit/b1bf54ddff9b14ecd9476d08d749cc75d447b688))

## <small>8.7.2 (2021-01-19)</small>

* [bitnami/*] Change helm version in the prerequisites (#5090) ([c5e67a3](https://github.com/bitnami/charts/commit/c5e67a388743cbee28439d2cabca27884b9daf97)), closes [#5090](https://github.com/bitnami/charts/issues/5090)
* [bitnami/rabbitmq] Drop values-production.yaml support (#5128) ([675d44b](https://github.com/bitnami/charts/commit/675d44b70576c381ba27e9af98c5841e68474d98)), closes [#5128](https://github.com/bitnami/charts/issues/5128)

## <small>8.7.1 (2021-01-18)</small>

* [bitnami/rabbitmq] Fix critical rabbitmq helm scope bug (#5062) ([fbb0ef3](https://github.com/bitnami/charts/commit/fbb0ef314ecceb974068be95b22a4a6490e20735)), closes [#5062](https://github.com/bitnami/charts/issues/5062)

## 8.7.0 (2021-01-15)

* [bitnami/rabbitmq] Adapt ingress to k8s 1.20 (#5007) ([19f440e](https://github.com/bitnami/charts/commit/19f440e58cdc17a4e1886b9c696290d306596a95)), closes [#5007](https://github.com/bitnami/charts/issues/5007)

## <small>8.6.4 (2021-01-13)</small>

* extraSecrets prepend release name (#4960) ([6bc3701](https://github.com/bitnami/charts/commit/6bc370176fb996b02b024479b830dd87e4537bf0)), closes [#4960](https://github.com/bitnami/charts/issues/4960)

## <small>8.6.3 (2021-01-12)</small>

* Add a toggle for podSecurityContext (#4957) ([0126ba9](https://github.com/bitnami/charts/commit/0126ba938e019e30d103878ae7b907240af55f4a)), closes [#4957](https://github.com/bitnami/charts/issues/4957)

## <small>8.6.2 (2021-01-08)</small>

* [bitnami/rabbitmq] Release 8.6.2 updating components versions ([16d2aaf](https://github.com/bitnami/charts/commit/16d2aafd0776653f732f6ad9a262e6dbe6229904))

## <small>8.6.1 (2020-12-22)</small>

* [bitnami/minio][bitnami/rabbitmq] Fix tls-secrets namespace scope (#4801) ([8058468](https://github.com/bitnami/charts/commit/80584687240739eb7a887a7f331dba38211a44d2)), closes [#4801](https://github.com/bitnami/charts/issues/4801)

## 8.6.0 (2020-12-18)

* [bitnami/rabbitmq] Refine probes (#4747) ([80c02e3](https://github.com/bitnami/charts/commit/80c02e3195d34c0c49005453bcb4600ff13e94a3)), closes [#4747](https://github.com/bitnami/charts/issues/4747)

## <small>8.5.5 (2020-12-17)</small>

* [bitnami/*] fix typos (#4699) ([49adc63](https://github.com/bitnami/charts/commit/49adc63b672da976c55af2e077aa5648a357b77f)), closes [#4699](https://github.com/bitnami/charts/issues/4699)
* [bitnami/rabbitmq] Release 8.5.5 updating components versions ([7bf6867](https://github.com/bitnami/charts/commit/7bf6867572ea20d6b1688e72450f2b59dbb9730c))

## <small>8.5.4 (2020-12-11)</small>

* [bitnami/*] Update dependencies (#4694) ([2826c12](https://github.com/bitnami/charts/commit/2826c125b42505f28431301e3c1bbe5366e47a01)), closes [#4694](https://github.com/bitnami/charts/issues/4694)

## <small>8.5.3 (2020-12-11)</small>

* [bitnami/rabbitmq] Release 8.5.3 updating components versions ([8546000](https://github.com/bitnami/charts/commit/85460009e6c4da847f600388ab8296aa7b09a86c))

## <small>8.5.2 (2020-12-10)</small>

* [bitnami/*] Update CI *-values.yaml files (#4674) ([b473fa9](https://github.com/bitnami/charts/commit/b473fa98f79cb1b06bf592cfe8495c92a6fda16b)), closes [#4674](https://github.com/bitnami/charts/issues/4674)

## <small>8.5.1 (2020-12-09)</small>

* [bitnami/rabbitmq] fix missing quote on namespace (#4663) ([e726d64](https://github.com/bitnami/charts/commit/e726d647527b61ce25dce90f37e4fa451bd1c7d4)), closes [#4663](https://github.com/bitnami/charts/issues/4663)

## 8.5.0 (2020-12-09)

* [bitnami/rabbitmq] Add parameter to customize 'externalTrafficPolicy' (#4645) ([86aca4d](https://github.com/bitnami/charts/commit/86aca4dd44191b39737aa7c837a556c95d9bb601)), closes [#4645](https://github.com/bitnami/charts/issues/4645)

## <small>8.4.3 (2020-12-08)</small>

* [bitnami/rabbitmq] fix if statement for checksum/secret annotation (#4644) ([95fde31](https://github.com/bitnami/charts/commit/95fde31b18b53f7a2459fbf611d8b2b805a9338d)), closes [#4644](https://github.com/bitnami/charts/issues/4644)

## <small>8.4.2 (2020-12-08)</small>

* [bitnami/rabbitmq] Fix rabbitmqctl command (#4636) ([e09301c](https://github.com/bitnami/charts/commit/e09301cf99230e22248d2fa72ff0162769640b6e)), closes [#4636](https://github.com/bitnami/charts/issues/4636)

## <small>8.4.1 (2020-12-04)</small>

* [bitnami/rabbitmq] Update readinessProbe to avoid deadlocks (#4598) ([aab5ec7](https://github.com/bitnami/charts/commit/aab5ec78b3d53be810d4eaf9bca1bde7cd436955)), closes [#4598](https://github.com/bitnami/charts/issues/4598)

## 8.4.0 (2020-12-03)

* [bitnami/rabbitmq] Affinity based on common presets (#4577) ([a318bec](https://github.com/bitnami/charts/commit/a318becebd08a034110a4628fceadf739350ee70)), closes [#4577](https://github.com/bitnami/charts/issues/4577)

## 8.3.0 (2020-12-02)

* [bitnami/rabbitmq] Allow existingErlangSecret to be templatized (#4547) ([50c5d1a](https://github.com/bitnami/charts/commit/50c5d1af54c883b4a451203f02ed750ff71a1c42)), closes [#4547](https://github.com/bitnami/charts/issues/4547)
* [bitnami/rabbitmq] Fix backwards compatibility of the nodeshutdown.sh script (#4495) ([e4fb3b0](https://github.com/bitnami/charts/commit/e4fb3b01ca0e232594c98efea718ba7b56947b29)), closes [#4495](https://github.com/bitnami/charts/issues/4495)

## 8.2.0 (2020-11-26)

* [bitnami/rabbitmq] Start to use shutdown script waiting for synchronization (#4470) ([9524947](https://github.com/bitnami/charts/commit/95249478d9498b5b57a99b943bd995a1c901cf52)), closes [#4470](https://github.com/bitnami/charts/issues/4470) [#3931](https://github.com/bitnami/charts/issues/3931) [#3931](https://github.com/bitnami/charts/issues/3931)

## <small>8.1.1 (2020-11-24)</small>

* [bitnami/rabbitmq] fix: align values.yaml and README.md (#4231) ([c6e5584](https://github.com/bitnami/charts/commit/c6e55842dc599f9087f7d8659e6f6dc2542eed68)), closes [#4231](https://github.com/bitnami/charts/issues/4231)

## 8.1.0 (2020-11-23)

* [bitnami/rabbitmq] Add statefulsetLabels, similar to podLabels (#4449) ([ab46e49](https://github.com/bitnami/charts/commit/ab46e49574a2522454c94058c31dcc92f0ca31f6)), closes [#4449](https://github.com/bitnami/charts/issues/4449)

## <small>8.0.5 (2020-11-19)</small>

* [bitnami/rabbitmq] Add config checksum to the statefulset (#4423) ([ad61be2](https://github.com/bitnami/charts/commit/ad61be20a3edd06f11692e07443973455c257511)), closes [#4423](https://github.com/bitnami/charts/issues/4423)

## <small>8.0.4 (2020-11-18)</small>

* [bitnami/rabbitmq] Fix networkpolicy podSelector labels (#4409) ([b743bf6](https://github.com/bitnami/charts/commit/b743bf60c75ce6962a7a48cb236d41c21206103d)), closes [#4409](https://github.com/bitnami/charts/issues/4409)

## <small>8.0.3 (2020-11-17)</small>

* [bitnami/rabbitmq] Allow for templating in extraSecrets (#4378) ([db3dd28](https://github.com/bitnami/charts/commit/db3dd28bf64cc060615e03226030180d6c2f3dc9)), closes [#4378](https://github.com/bitnami/charts/issues/4378)
* Clarify configuration params (#4363) ([ebc07b9](https://github.com/bitnami/charts/commit/ebc07b91528b63bc9ecec155cdfc7cf1cf52fbcf)), closes [#4363](https://github.com/bitnami/charts/issues/4363)

## <small>8.0.2 (2020-11-12)</small>

* [bitnami/rabbitmq]  Fix typo in svc-headless.yaml (#4309) ([7008521](https://github.com/bitnami/charts/commit/7008521c57b46194017b74c75712826c7264d7b8)), closes [#4309](https://github.com/bitnami/charts/issues/4309)

## <small>8.0.1 (2020-11-11)</small>

* [bitnami/rabbitmq] Add how to configure the default user/vhost to README (#4256) ([00fe910](https://github.com/bitnami/charts/commit/00fe9102835b89e36f8c4c252f5115a2be795fa4)), closes [#4256](https://github.com/bitnami/charts/issues/4256) [#4122](https://github.com/bitnami/charts/issues/4122)

## 8.0.0 (2020-11-11)

* [bitnami/rabbitmq] Major version. Adapt Chart to apiVersion: v2 (#4288) ([ecb3d11](https://github.com/bitnami/charts/commit/ecb3d1115f8a5f41ee4e73f31ff95723e99bc85a)), closes [#4288](https://github.com/bitnami/charts/issues/4288)

## 7.8.0 (2020-11-09)

* [bitnami/rabbitmq] Allow port names customization in services (#4243) ([b2d5689](https://github.com/bitnami/charts/commit/b2d56891e0f0e144b35439cdcf5bbec3eae75ac9)), closes [#4243](https://github.com/bitnami/charts/issues/4243)

## <small>7.7.1 (2020-10-28)</small>

* [bitnami/*] Include link to Troubleshootin guide on README.md (#4136) ([c08a20e](https://github.com/bitnami/charts/commit/c08a20e3db004215383004ff023a73fcc2522e72)), closes [#4136](https://github.com/bitnami/charts/issues/4136)
* [bitnami/rabbitmq] Allow integers for namespace (#4137) ([2643e7c](https://github.com/bitnami/charts/commit/2643e7c9ea13724188011f3bbe76733264d9625d)), closes [#4137](https://github.com/bitnami/charts/issues/4137)

## 7.7.0 (2020-10-27)

* [bitnami/rabbitmq] Attach existing volume as persistence storage (#4102) ([d883c8b](https://github.com/bitnami/charts/commit/d883c8bbadc8ca03b8f1d60cf59839c4bfce62ce)), closes [#4102](https://github.com/bitnami/charts/issues/4102)

## <small>7.6.9 (2020-10-25)</small>

* [bitnami/rabbitmq] Release 7.6.9 updating components versions ([b3ba436](https://github.com/bitnami/charts/commit/b3ba436d3f2300079cc41805cfa1e21d41a015e9))

## <small>7.6.8 (2020-09-25)</small>

* [bitnami/rabbitmq] Release 7.6.8 updating components versions ([e57038c](https://github.com/bitnami/charts/commit/e57038cd7f2d38a88bcf98272095f21698caad2a))

## <small>7.6.7 (2020-09-21)</small>

* [bitnami/rabbitmq] Release 7.6.7 updating components versions ([8e388f5](https://github.com/bitnami/charts/commit/8e388f5e4d9ebd35efbf884b0290c1cd8e24e3b6))
* Add links to docs.bitnami.com (#3715) ([2449278](https://github.com/bitnami/charts/commit/24492789aeadc39da80649d409116b7aa3155ce1)), closes [#3715](https://github.com/bitnami/charts/issues/3715)

## <small>7.6.6 (2020-09-09)</small>

* [bitnami/rabbitmq] Release 7.6.6 Changing default value for service.extraPorts (#3630) ([1b9ca8d](https://github.com/bitnami/charts/commit/1b9ca8daea338aed3949b1857a94c8a5ac451fdc)), closes [#3630](https://github.com/bitnami/charts/issues/3630)

## <small>7.6.5 (2020-09-04)</small>

* [bitnami/metrics-server] Add source repo (#3577) ([1ed12f9](https://github.com/bitnami/charts/commit/1ed12f96af75322b46afdb2b3d9907c11b13f765)), closes [#3577](https://github.com/bitnami/charts/issues/3577)
* [bitnami/rabbitmq] Release 7.6.5 updating components versions ([d695d12](https://github.com/bitnami/charts/commit/d695d1281775e77eb9dd7682594e10d3233037d9))

## <small>7.6.4 (2020-08-24)</small>

* Change documentation about 'load_definitions' (#3483) ([2833762](https://github.com/bitnami/charts/commit/28337624c81e7328a2fbad2408b9c3034acdb0e6)), closes [#3483](https://github.com/bitnami/charts/issues/3483)

## <small>7.6.3 (2020-08-19)</small>

* [bitnami/rabbitmq] Release 7.6.3 updating components versions ([fafc4f8](https://github.com/bitnami/charts/commit/fafc4f8465b6a27eee293f299b82e7995787c235))

## <small>7.6.2 (2020-08-18)</small>

* [bitnami/rabbitmq] Run secret name through tpl (#3222) (#3445) ([8d0be4f](https://github.com/bitnami/charts/commit/8d0be4f3cdd1b7ddaeefabbad8d4ff5f2a21f7ae)), closes [#3222](https://github.com/bitnami/charts/issues/3222) [#3445](https://github.com/bitnami/charts/issues/3445)

## <small>7.6.1 (2020-08-14)</small>

* [bitnami/rabbitmq] Evaluate extraConfiguration value as template (#3415) ([e713290](https://github.com/bitnami/charts/commit/e713290f523360ae166de1f4f71164dc3e9f9cd6)), closes [#3415](https://github.com/bitnami/charts/issues/3415)

## 7.6.0 (2020-08-13)

* [bitnami/*] Use common helps for upgrade password errors (#3335) ([079f5bd](https://github.com/bitnami/charts/commit/079f5bd6ec59bb058216d6a931449b895517c789)), closes [#3335](https://github.com/bitnami/charts/issues/3335)

## <small>7.5.8 (2020-08-05)</small>

* [bitnami/*] Fix TL;DR typo in READMEs (#3280) ([3d7ab40](https://github.com/bitnami/charts/commit/3d7ab406fecd64f1af25f53e7d27f03ec95b29a4)), closes [#3280](https://github.com/bitnami/charts/issues/3280)
* [bitnami/rabbitmq] Release 7.5.8 updating components versions ([fc481d8](https://github.com/bitnami/charts/commit/fc481d882ea63f242806cf0a20fdbc701721fb83))

## <small>7.5.7 (2020-07-22)</small>

* Bugfix rabbitmq pre stop hook not applied when rebalance is true (#3171) ([cc3c27c](https://github.com/bitnami/charts/commit/cc3c27c500df620b1fbf32550daa2fc5c32682e5)), closes [#3171](https://github.com/bitnami/charts/issues/3171)

## <small>7.5.6 (2020-07-18)</small>

* [bitnami/rabbitmq] Release 7.5.6 updating components versions ([c265119](https://github.com/bitnami/charts/commit/c2651190070d861f592b35fd08c4de22f10c84be))

## <small>7.5.5 (2020-07-17)</small>

* [bitnami/rabbitmq] Fix issue with mnesia dir when forcing boot (#3139) ([654eae7](https://github.com/bitnami/charts/commit/654eae7ed6aea46b69aaea3c2ce6e9de484a2814)), closes [#3139](https://github.com/bitnami/charts/issues/3139)

## <small>7.5.4 (2020-07-15)</small>

* [bitnami/all] Add categories (#3075) ([63bde06](https://github.com/bitnami/charts/commit/63bde066b87a140fab52264d0522401ab3d63509)), closes [#3075](https://github.com/bitnami/charts/issues/3075)
* [bitnami/rabbitmq] Release 7.5.4 updating components versions ([874e4ce](https://github.com/bitnami/charts/commit/874e4ceef48a86645a686d1391fff1da5ef28fe7))

## <small>7.5.3 (2020-07-10)</small>

* [bitnami/rabbitmq] Corrected example of extraContainerPorts (#3076) ([bcc42b4](https://github.com/bitnami/charts/commit/bcc42b4e3c7ccf9dabd2023e27dd582bc7f4a862)), closes [#3076](https://github.com/bitnami/charts/issues/3076) [#2023](https://github.com/bitnami/charts/issues/2023) [#2027](https://github.com/bitnami/charts/issues/2027) [bitnami#2023](https://github.com/bitnami/issues/2023) [bitnami#2027](https://github.com/bitnami/issues/2027) [bitnami#2023](https://github.com/bitnami/issues/2023) [bitnami#2027](https://github.com/bitnami/issues/2027)

## <small>7.5.2 (2020-07-09)</small>

* [bitnami/rabbitmq] Show the correct secret names in post-install notes (#3050) ([c951672](https://github.com/bitnami/charts/commit/c9516721f0191d4cbde2eea096cc632f79dac5ea)), closes [#3050](https://github.com/bitnami/charts/issues/3050)

## <small>7.5.1 (2020-07-08)</small>

* [bitnami/rabbitmq] Release 7.5.1 updating components versions ([f29183d](https://github.com/bitnami/charts/commit/f29183d87c8d9cab8069e997ae980364fee88ad8))

## 7.5.0 (2020-07-08)

* [bitnami/rabbitmq] Add support for multiple LDAP servers (#3066) ([4a2884c](https://github.com/bitnami/charts/commit/4a2884cdeb5267bf94dcbb8c8ae30ca71d453323)), closes [#3066](https://github.com/bitnami/charts/issues/3066)

## <small>7.4.8 (2020-07-08)</small>

* [bitnami/rabbitmq] fix tls entries in ingress (#3065) ([d8fb355](https://github.com/bitnami/charts/commit/d8fb3559cc15c8a3db908c3c03a68c52e3a86e14)), closes [#3065](https://github.com/bitnami/charts/issues/3065)

## <small>7.4.7 (2020-07-07)</small>

* [bitnami/rabbitmq] Release 7.4.7 updating components versions ([176cd7d](https://github.com/bitnami/charts/commit/176cd7deabafce15c661d4cda6ced18407c04fee))

## <small>7.4.6 (2020-07-07)</small>

* [bitnami/rabbitmq] Improve reliability and document post-scalation steps when scaling down (#3048) ([ad0f5b7](https://github.com/bitnami/charts/commit/ad0f5b7d8089ac2d306585b7fe5dc2d03eb1415a)), closes [#3048](https://github.com/bitnami/charts/issues/3048)

## <small>7.4.5 (2020-07-03)</small>

* [bitnami/rabbitmq] Release 7.4.5 updating components versions ([e4e7c4f](https://github.com/bitnami/charts/commit/e4e7c4f73deb094d1b5fc8a279832541f0b33170))

## <small>7.4.4 (2020-07-03)</small>

* [bitnami/rabbitmq] Use less intrusive probes (#3016) ([f5f3ad2](https://github.com/bitnami/charts/commit/f5f3ad2cb7737cfe6ef3a822c3e70313a5616799)), closes [#3016](https://github.com/bitnami/charts/issues/3016)

## <small>7.4.3 (2020-07-01)</small>

* [bitnami/rabbitmq] Release 7.4.3 updating components versions ([77e4d00](https://github.com/bitnami/charts/commit/77e4d00b4460cd63a75630e12696a787e747b540))

## <small>7.4.2 (2020-07-01)</small>

* [bitnami/rabbitmq] Update plugins section in the README (#2989) ([bc14dfd](https://github.com/bitnami/charts/commit/bc14dfdc6cb922af74eee925ea0d83973b89bd8a)), closes [#2989](https://github.com/bitnami/charts/issues/2989)

## <small>7.4.1 (2020-07-01)</small>

* [bitnami/rabbitmq] Release 7.4.1 updating components versions ([3ca06a6](https://github.com/bitnami/charts/commit/3ca06a624c9e628774503dc854eca33819c4449e))

## 7.4.0 (2020-07-01)

* [bitnami/rabbitmq] Use existingSecret for ingress TLS (#2986) ([b293d3a](https://github.com/bitnami/charts/commit/b293d3afd56639b0be3643340dc12272a4b233d0)), closes [#2986](https://github.com/bitnami/charts/issues/2986)

## <small>7.3.3 (2020-06-29)</small>

* [bitnami/rabbitmq] Release 7.3.3 updating components versions ([b3af237](https://github.com/bitnami/charts/commit/b3af2379c87e421907e257434bd1e8d1d4146778))

## <small>7.3.2 (2020-06-29)</small>

* [bitnami/rabbitmq] Fix a bug in readiness and liveness (#2949) ([157a0a7](https://github.com/bitnami/charts/commit/157a0a7eac398f609b38b0aa04d38d4d5b51ffc0)), closes [#2949](https://github.com/bitnami/charts/issues/2949)

## <small>7.3.1 (2020-06-25)</small>

* [bitnami/rabbitmq] Release 7.3.1 updating components versions ([be9d8e2](https://github.com/bitnami/charts/commit/be9d8e2d4a5af81969fdb63c9d2e4ee18b2ac91a))

## 7.3.0 (2020-06-25)

* [bitnami/rabbitmq] Provide mechanism to download community plugins during initialization (#2906) ([ba888ca](https://github.com/bitnami/charts/commit/ba888ca49d7e0b96eb9fcb9389ae3118fa6114be)), closes [#2906](https://github.com/bitnami/charts/issues/2906)

## <small>7.2.1 (2020-06-24)</small>

* [bitnami/rabbitmq] Release 7.2.1 updating components versions ([1e86fc8](https://github.com/bitnami/charts/commit/1e86fc8bbe22310af62bfb6c9fe75d5f486b961e))

## 7.2.0 (2020-06-24)

* [bitnami/rabbitmq] Fix Ingress port and add custom path (#2897) ([206233c](https://github.com/bitnami/charts/commit/206233c5293736057d8f53cb716bbfee3206f30c)), closes [#2897](https://github.com/bitnami/charts/issues/2897)

## <small>7.1.2 (2020-06-23)</small>

* [bitnami/rabbitmq] Release 7.1.2 updating components versions ([2fa98fe](https://github.com/bitnami/charts/commit/2fa98fe41081fce9619958572a4f30216936ff80))

## <small>7.1.1 (2020-06-23)</small>

* [bitnami/rabbitmq] Fix clustering when non-default k8s domain name is used (#2894) ([ac68b0f](https://github.com/bitnami/charts/commit/ac68b0f5b1693007ee352ce5f0c8b94069b27d4e)), closes [#2894](https://github.com/bitnami/charts/issues/2894)

## 7.1.0 (2020-06-22)

* [bitnami/rabbitmq] Custom nodePort expose per port (#2825) ([78b4d60](https://github.com/bitnami/charts/commit/78b4d6035de9eab7520e15e866dbc2113de2c62a)), closes [#2825](https://github.com/bitnami/charts/issues/2825)

## <small>7.0.4 (2020-06-22)</small>

* [bitnami/several] remove library/bitnami-common (#2879) ([d936fe3](https://github.com/bitnami/charts/commit/d936fe33d2a28fc38b6d714a553d922f85669bd7)), closes [#2879](https://github.com/bitnami/charts/issues/2879)

## <small>7.0.3 (2020-06-19)</small>

* [bitnami/rabbitmq] Fix incorrect RabbitMQ advanced.config config map key (#2882) ([d81a958](https://github.com/bitnami/charts/commit/d81a958736e038bf00b8d651da4e766c30a8e905)), closes [#2882](https://github.com/bitnami/charts/issues/2882)

## <small>7.0.2 (2020-06-19)</small>

* [bitnami/rabbitmq] Allow customizing RabbitMQ username (#2884) ([dca246c](https://github.com/bitnami/charts/commit/dca246ce41ebef6b7396c14c61e64242ca907a83)), closes [#2884](https://github.com/bitnami/charts/issues/2884)
* [bitnami/rabbitmq] Fixing extraEnvVars (#2867) ([1a265f8](https://github.com/bitnami/charts/commit/1a265f8b2cc3a910a1990df02b5e52274cee1f0e)), closes [#2867](https://github.com/bitnami/charts/issues/2867)

## <small>7.0.1 (2020-06-19)</small>

* [bitnami/rabbitmq] Release 7.0.1 updating components versions ([abba63f](https://github.com/bitnami/charts/commit/abba63f37e482b6f7c51c03e1fa2c1a7b0abf64c))
* [multiple charts] Update hidden properties in the different JSON schemas (#2871) ([4cff6ba](https://github.com/bitnami/charts/commit/4cff6ba8b0013b6dc368a1e7986c393e8447e75b)), closes [#2871](https://github.com/bitnami/charts/issues/2871)

## 7.0.0 (2020-06-17)

* [bitnami/rabbitmq] Rely on container initialization + refactoring to follow Helm chart best practice ([43e7082](https://github.com/bitnami/charts/commit/43e70827ef73560e118907b3a2c0d7d9c72bce52)), closes [#2843](https://github.com/bitnami/charts/issues/2843)

## <small>6.28.1 (2020-06-17)</small>

* [bitnami/rabbitmq] Release 6.28.1 updating components versions ([11b7183](https://github.com/bitnami/charts/commit/11b71835341eebf705ef6409496172404acc2be7))

## 6.28.0 (2020-06-16)

* [bitnami\rabbitmq] Added support of specific service account (#2569) ([7c03dab](https://github.com/bitnami/charts/commit/7c03daba7723920167887ec9a51cf2f7b42dd0b1)), closes [#2569](https://github.com/bitnami/charts/issues/2569)

## <small>6.27.2 (2020-06-16)</small>

* [bitnami/rabbitmq] Release 6.27.2 updating components versions ([77fd0e7](https://github.com/bitnami/charts/commit/77fd0e72689ec43f9b2b2ec3bcdc0459f4177102))

## <small>6.27.1 (2020-06-15)</small>

* [bitnami/rabbitmq] Release 6.27.1 updating components versions ([c56fdc4](https://github.com/bitnami/charts/commit/c56fdc45ca6c10d1a44a75a71ca7e26597470833))

## 6.27.0 (2020-06-11)

* [bitnami/rabbitmq] Add community plugins (#2729) ([979d876](https://github.com/bitnami/charts/commit/979d876782c85bd858bf68659d807ed35e8ec9fd)), closes [#2729](https://github.com/bitnami/charts/issues/2729)

## 6.26.0 (2020-06-05)

* [bitnami/rabbitmq] add podLabels to statefulset (#2744) ([a3abafe](https://github.com/bitnami/charts/commit/a3abafe21cbd393f0cabaf7cee68a4c0059435c9)), closes [#2744](https://github.com/bitnami/charts/issues/2744)

## <small>6.25.13 (2020-05-26)</small>

* [bitnami/rabbitmq] make load definition secret name be templatable (#2654) ([e56173b](https://github.com/bitnami/charts/commit/e56173b10b9b19c88398c0b88fed24449849ccaf)), closes [#2654](https://github.com/bitnami/charts/issues/2654)
* update bitnami/common to be compatible with helm v2.12+ (#2615) ([c7751eb](https://github.com/bitnami/charts/commit/c7751eb5764e468e1854b58a1b8491d2b13e0a4a)), closes [#2615](https://github.com/bitnami/charts/issues/2615)

## <small>6.25.12 (2020-05-18)</small>

* [bitnami/rabbitmq] improve network policy to include internal traffic (#2597) ([d6c55db](https://github.com/bitnami/charts/commit/d6c55dbdf444995e5e5b1b9224ba84d6874f4f3e)), closes [#2597](https://github.com/bitnami/charts/issues/2597)

## <small>6.25.11 (2020-05-13)</small>

* [bitnami/rabbitmq] Release 6.25.11 updating components versions ([5e95a78](https://github.com/bitnami/charts/commit/5e95a787bc781837af221b2075e4130c562dc1d2))

## <small>6.25.10 (2020-05-13)</small>

* [bitnami/rabbitmq] Release 6.25.10 updating components versions ([80e9483](https://github.com/bitnami/charts/commit/80e94837f9918a12055f6396b41c5f0b3d2726ee))

## <small>6.25.9 (2020-05-08)</small>

* [bitnami/rabbitmq] Add externalIP custom value (#2534) ([f1911e9](https://github.com/bitnami/charts/commit/f1911e96164535f9da3c8f508531680bc17eb09c)), closes [#2534](https://github.com/bitnami/charts/issues/2534)

## <small>6.25.8 (2020-05-07)</small>

* [bitnami/rabbitmq] extraVolumeMounts new line fix (#2533) ([ecb5150](https://github.com/bitnami/charts/commit/ecb515030b85b200e1e6d9873e3b3f2ad6da2b92)), closes [#2533](https://github.com/bitnami/charts/issues/2533) [#2532](https://github.com/bitnami/charts/issues/2532)

## <small>6.25.7 (2020-05-04)</small>

* [bitnami/rabbitmq] fix extraPorts indentation (#2496) ([5fe117c](https://github.com/bitnami/charts/commit/5fe117ca1af76da214ff9e04101e786a56668030)), closes [#2496](https://github.com/bitnami/charts/issues/2496)

## <small>6.25.6 (2020-04-30)</small>

* Rabbitmq pod annotations (#2466) ([4cff212](https://github.com/bitnami/charts/commit/4cff212571be1dbf6bd1e710992081a85979bd08)), closes [#2466](https://github.com/bitnami/charts/issues/2466)

## <small>6.25.5 (2020-04-22)</small>

* [bitnami/rabbitmq] Release 6.25.5 updating components versions ([79eb3b5](https://github.com/bitnami/charts/commit/79eb3b5e84204f9ac557db53c3c987acdb686655))

## <small>6.25.4 (2020-04-16)</small>

* [bitnami/rabbitmq] Release 6.25.4 updating components versions ([f0a4b14](https://github.com/bitnami/charts/commit/f0a4b146a06714d59a1a98d47a105877591e9067))
* [bitnami/rabbitmq] Tune parameters to increase performance (#2252) ([4bcde72](https://github.com/bitnami/charts/commit/4bcde72608f0b2ad68382d1e871da95293c469a4)), closes [#2252](https://github.com/bitnami/charts/issues/2252)

## <small>6.25.3 (2020-04-14)</small>

* [bitnami/rabbitmq] Release 6.25.3 updating components versions ([0c4821c](https://github.com/bitnami/charts/commit/0c4821c7058f77d187a7ea74813ed0a0f69918df))

## <small>6.25.2 (2020-04-10)</small>

* [bitnami/rabbitmq] Release 6.25.2 updating components versions ([1647de0](https://github.com/bitnami/charts/commit/1647de0a86a46836432a1f450bc4618082063fae))

## <small>6.25.1 (2020-04-09)</small>

* [bitnami/rabbitmq] livenessProbe and readinessProbe command override (#2219) ([93d59d3](https://github.com/bitnami/charts/commit/93d59d3cae920e0da8d20985940d6f7f20523a90)), closes [#2219](https://github.com/bitnami/charts/issues/2219)
* [bitnami/rabbitmq] Release 6.25.1 updating components versions ([e56ced5](https://github.com/bitnami/charts/commit/e56ced54a0eaa0c57858a75ba61d16c4b1972f9b))

## 6.25.0 (2020-04-08)

* [bitnami/rabbitmq] Add ability to add service labels. (#2237) ([21dbb4a](https://github.com/bitnami/charts/commit/21dbb4a66e17570d6fd27ce8c38ee77fb09efd03)), closes [#2237](https://github.com/bitnami/charts/issues/2237)

## 6.24.0 (2020-04-06)

* [bitnami/rabbitmq] Disable old metrics, enable new Prometheus auto-scraping (#2110) ([ab679d4](https://github.com/bitnami/charts/commit/ab679d4030156189404ae859e3e9fdade51e513c)), closes [#2110](https://github.com/bitnami/charts/issues/2110)

## 6.23.0 (2020-04-02)

* [bitnami/rabbitmq] Add a selector to persistentVolumeClaims to be able to target an existing persist ([3b9461c](https://github.com/bitnami/charts/commit/3b9461ceb236677a0ad7bb443644d511fe0f9822)), closes [#2184](https://github.com/bitnami/charts/issues/2184)

## 6.22.0 (2020-04-01)

* [bitnami/rabbitmq] Add events create rule, needed by the rabbitmq_peer_discovery_k8s plugin (#2173) ([57f7074](https://github.com/bitnami/charts/commit/57f70743e475b6e5b404b7edad001085fda54418)), closes [#2173](https://github.com/bitnami/charts/issues/2173)

## <small>6.21.1 (2020-03-31)</small>

* [rabbitmq] fix missing $ in .Release.Namespace (#2165) ([4353b52](https://github.com/bitnami/charts/commit/4353b52e633f3392cea2993d996bdd8cf27f5fe1)), closes [#2165](https://github.com/bitnami/charts/issues/2165)

## 6.21.0 (2020-03-30)

* [bitnami/rabbitmq] Introduced `.Release.Namespace`  in objects meta (#2159) ([16bd948](https://github.com/bitnami/charts/commit/16bd948d6d72bde3708b69231aecfcacd9d5c368)), closes [#2159](https://github.com/bitnami/charts/issues/2159)

## <small>6.20.1 (2020-03-26)</small>

* [bitnami/rabbitmq] Release 6.20.1 updating components versions ([94f00f4](https://github.com/bitnami/charts/commit/94f00f4dae2e6311feefad2e6bc3db72d2e6475d))

## 6.20.0 (2020-03-23)

* Link secret in rabbitmq chart (#2107) ([1f6ef84](https://github.com/bitnami/charts/commit/1f6ef84f39a7bf7b30d74f9b2cfe4ceff81624d0)), closes [#2107](https://github.com/bitnami/charts/issues/2107)

## <small>6.19.2 (2020-03-20)</small>

* [bitnami/rabbitmq] Release 6.19.2 updating components versions ([4afe19b](https://github.com/bitnami/charts/commit/4afe19b4c5ad1a145334afe2eba9b024be8ce56a))

## <small>6.19.1 (2020-03-19)</small>

* [bitnami/rabbitmq] Release 6.19.1 updating components versions ([3a01d45](https://github.com/bitnami/charts/commit/3a01d4510f03a1387881e233b709277e54108702))

## 6.19.0 (2020-03-19)

* Update Chart.yaml ([e2e8b29](https://github.com/bitnami/charts/commit/e2e8b292e217f034ca5e42469427507ca01d793a))

## <small>6.19.9 (2020-03-17)</small>

* [bitnami/rabbitmq] added instructions to use extra ports - closes #2023  (#2037) ([3f9c2ae](https://github.com/bitnami/charts/commit/3f9c2aea1386b4cbb7ca2ade9ac65116ee872974)), closes [#2023](https://github.com/bitnami/charts/issues/2023) [#2037](https://github.com/bitnami/charts/issues/2037)
* [bitnami/rabbitmq] adding possibility to have extras volumes/volumesm… (#2042) ([4b06e0f](https://github.com/bitnami/charts/commit/4b06e0f54d15656fd219626ade7e77c48246557f)), closes [#2042](https://github.com/bitnami/charts/issues/2042)

## <small>6.18.9 (2020-03-11)</small>

* [bitnami/rabbitmq] Release 6.18.9 updating components versions ([c9ce341](https://github.com/bitnami/charts/commit/c9ce341bff8c1c5ae3f385aaa8aba129075f2a5c))

## <small>6.18.8 (2020-03-11)</small>

* Move charts from upstreamed folder to bitnami (#2032) ([a0e44f7](https://github.com/bitnami/charts/commit/a0e44f7d6a10b8b5643186130ea420887cb72c7c)), closes [#2032](https://github.com/bitnami/charts/issues/2032)

## <small>6.18.7 (2020-03-10)</small>

* [bitnami/redis & rabbit] Revert CI removal (#2025) ([f05c165](https://github.com/bitnami/charts/commit/f05c16577066d95b835eb032c1f8e625d3cb47fd)), closes [#2025](https://github.com/bitnami/charts/issues/2025)

## <small>6.18.6 (2020-03-10)</small>

* [bitnami/rabbitmq] Modify RabbitMQ notes for convenience. (#2021) ([61a2013](https://github.com/bitnami/charts/commit/61a2013c258111131a40a2829a5937d59eede3bf)), closes [#2021](https://github.com/bitnami/charts/issues/2021)

## <small>6.18.5 (2020-03-09)</small>

* [bitnami/rabbitmq] Release 6.18.5 updating components versions ([80c8375](https://github.com/bitnami/charts/commit/80c837501528b3996ec553a96a23d1d478368fd6))

## <small>6.18.4 (2020-03-09)</small>

* [bitnami/redis] Move chart from stable and remove ci folder (#2017) ([bb8e1cf](https://github.com/bitnami/charts/commit/bb8e1cf68328a604dc48185b2deb165c0076d7d8)), closes [#2017](https://github.com/bitnami/charts/issues/2017)

## <small>6.18.3 (2020-03-06)</small>

* [bitnami/rabbitmq] Release 6.18.3 updating components versions ([a6bf548](https://github.com/bitnami/charts/commit/a6bf54815ec87803df6fc1ad750952dfcdbe3448))

## <small>6.18.2 (2020-03-06)</small>

* [bitnami/rabbitmq] Release 6.18.2 updating components versions ([a84f789](https://github.com/bitnami/charts/commit/a84f7891d6700e57e366e99053cfa6cef62f5c70))

## <small>6.18.1 (2020-03-06)</small>

* [bitnami/rabbitmq] Copy rabbitmq chart from stable (https://github.com/helm/charts/pull/21310) ([861c8ff](https://github.com/bitnami/charts/commit/861c8ff4b134e357b1beeb8aac684b4809e2d76f))
