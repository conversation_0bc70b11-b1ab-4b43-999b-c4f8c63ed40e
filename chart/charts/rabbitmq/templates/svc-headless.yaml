{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-%s" (include "common.names.fullname" .) (default "headless" .Values.servicenameOverride) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if or .Values.service.annotationsHeadless .Values.commonAnnotations .Values.service.headless.annotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.service.headless.annotations .Values.service.annotationsHeadless .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  clusterIP: None
  ports:
    - name: {{ .Values.service.portNames.epmd }}
      port: {{ .Values.service.ports.epmd }}
      targetPort: epmd
    {{- if or (.Values.service.portEnabled) (not .Values.auth.tls.enabled) }}
    - name: {{ .Values.service.portNames.amqp }}
      port: {{ .Values.service.ports.amqp }}
      targetPort: amqp
    {{- end }}
    {{- if .Values.auth.tls.enabled }}
    - name: {{ .Values.service.portNames.amqpTls }}
      port: {{ .Values.service.ports.amqpTls }}
      targetPort: amqp-tls
    {{- end }}
    - name: {{ .Values.service.portNames.dist }}
      port: {{ .Values.service.ports.dist }}
      targetPort: dist
    {{- if .Values.service.managerPortEnabled }}
    - name: {{ .Values.service.portNames.manager }}
      port: {{ .Values.service.ports.manager  }}
      targetPort: stats
    {{- end }}
    {{- if .Values.service.extraPortsHeadless }}
    {{- include "common.tplvalues.render" (dict "value" .Values.service.extraPortsHeadless "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.podLabels .Values.commonLabels ) "context" . ) }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
  publishNotReadyAddresses: true
  {{- if semverCompare ">=1.31-0" (include "common.capabilities.kubeVersion" .) }}
  trafficDistribution: {{ .Values.service.trafficDistribution }}
  {{- end }}
