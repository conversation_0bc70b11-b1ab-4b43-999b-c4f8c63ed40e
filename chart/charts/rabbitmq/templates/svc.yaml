{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: v1
kind: Service
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.service.labels .Values.commonLabels ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
  {{- if or .Values.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.service.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  {{- if not (empty .Values.service.clusterIP) }}
  clusterIP: {{ .Values.service.clusterIP }}
  {{- end }}
  {{- if and (eq .Values.service.type "LoadBalancer") (not (empty .Values.service.loadBalancerClass)) }}
  loadBalancerClass: {{ .Values.service.loadBalancerClass }}
  {{- end }}
  {{- if eq .Values.service.type "LoadBalancer" }}
  {{- if not (empty .Values.service.loadBalancerIP) }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
  {{- end }}
  {{- if .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{- toYaml .Values.service.loadBalancerSourceRanges | nindent 4 }}
  {{- end }}
  allocateLoadBalancerNodePorts: {{ .Values.service.allocateLoadBalancerNodePorts }}
  {{- end }}
  {{- if or (eq .Values.service.type "LoadBalancer") (eq .Values.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if .Values.service.sessionAffinity }}
  sessionAffinity: {{ .Values.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  {{- if .Values.service.externalIPs }}
  externalIPs: {{- toYaml .Values.service.externalIPs | nindent 4 }}
  {{- end }}
  ports:
    {{- if or (.Values.service.portEnabled) (not .Values.auth.tls.enabled) }}
    - name: {{ .Values.service.portNames.amqp }}
      port: {{ .Values.service.ports.amqp }}
      targetPort: amqp
      {{- if (eq .Values.service.type "ClusterIP") }}
      nodePort: null
      {{- else if and (or (eq .Values.service.type "NodePort") (eq .Values.service.type "LoadBalancer")) (not (empty .Values.service.nodePorts.amqp)) }}
      nodePort: {{ .Values.service.nodePorts.amqp }}
      {{- end }}
    {{- end }}
    {{- if .Values.auth.tls.enabled }}
    - name: {{ .Values.service.portNames.amqpTls }}
      port: {{ .Values.service.ports.amqpTls }}
      targetPort: amqp-tls
      {{- if (eq .Values.service.type "ClusterIP") }}
      nodePort: null
      {{- else if and (or (eq .Values.service.type "NodePort") (eq .Values.service.type "LoadBalancer")) (not (empty .Values.service.nodePorts.amqpTls)) }}
      nodePort: {{ .Values.service.nodePorts.amqpTls }}
      {{- end }}
    {{- end }}
    {{- if .Values.service.epmdPortEnabled }}
    - name: {{ .Values.service.portNames.epmd }}
      port: {{ .Values.service.ports.epmd }}
      targetPort: epmd
      {{- if (eq .Values.service.type "ClusterIP") }}
      nodePort: null
      {{- else if and (or (eq .Values.service.type "NodePort") (eq .Values.service.type "LoadBalancer")) (not (empty .Values.service.nodePorts.epmd)) }}
      nodePort: {{ .Values.service.nodePorts.epmd }}
      {{- end }}
    {{- end }}
    {{- if .Values.service.distPortEnabled }}
    - name: {{ .Values.service.portNames.dist }}
      port: {{ .Values.service.ports.dist }}
      targetPort: dist
      {{- if eq .Values.service.type "ClusterIP" }}
      nodePort: null
      {{- else if and (or (eq .Values.service.type "NodePort") (eq .Values.service.type "LoadBalancer")) (not (empty .Values.service.nodePorts.dist)) }}
      nodePort: {{ .Values.service.nodePorts.dist }}
      {{- end }}
    {{- end }}
    {{- if .Values.service.managerPortEnabled }}
    - name: {{ .Values.service.portNames.manager }}
      port: {{ .Values.service.ports.manager  }}
      targetPort: stats
      {{- if eq .Values.service.type "ClusterIP" }}
      nodePort: null
      {{- else if and (or (eq .Values.service.type "NodePort") (eq .Values.service.type "LoadBalancer")) (not (empty .Values.service.nodePorts.manager)) }}
      nodePort: {{ .Values.service.nodePorts.manager }}
      {{- end }}
    {{- end }}
    {{- if .Values.metrics.enabled }}
    - name: {{ .Values.service.portNames.metrics }}
      port: {{ .Values.service.ports.metrics }}
      targetPort: metrics
      {{- if eq .Values.service.type "ClusterIP" }}
      nodePort: null
      {{- else if and (or (eq .Values.service.type "NodePort") (eq .Values.service.type "LoadBalancer")) (not (empty .Values.service.nodePorts.metrics)) }}
      nodePort: {{ .Values.service.nodePorts.metrics }}
      {{- end }}
    {{- end }}
    {{- if .Values.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.podLabels .Values.commonLabels ) "context" . ) }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
