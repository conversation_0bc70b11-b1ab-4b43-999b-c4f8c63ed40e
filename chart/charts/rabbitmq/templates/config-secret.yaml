{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if or (empty .Values.configurationExistingSecret) .Values.advancedConfiguration }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-config" (include "common.names.fullname" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if empty .Values.configurationExistingSecret }}
  rabbitmq.conf: |-
    {{- include "common.tplvalues.render" (dict "value" .Values.configuration "context" $) | b64enc | nindent 4 }}
  {{- end }}  
  {{- if .Values.advancedConfiguration }}
  advanced.config: |-
    {{- include "common.tplvalues.render" (dict "value" .Values.advancedConfiguration "context" $) | b64enc | nindent 4 }}
  {{- end }}
{{- end }}
