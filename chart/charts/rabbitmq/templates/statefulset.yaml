{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: StatefulSet
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.statefulsetLabels .Values.commonLabels ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
  {{- if or .Values.statefulsetAnnotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.statefulsetAnnotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  serviceName: {{ printf "%s-%s" (include "common.names.fullname" .) (default "headless" .Values.servicenameOverride) }}
  podManagementPolicy: {{ .Values.podManagementPolicy }}
  replicas: {{ .Values.replicaCount }}
  {{- if .Values.updateStrategy }}
  updateStrategy: {{- toYaml .Values.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.podLabels .Values.commonLabels ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
  template:
    metadata:
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
      annotations:
        {{- if .Values.commonAnnotations }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 8 }}
        {{- end }}
        checksum/config: {{ include (print $.Template.BasePath "/config-secret.yaml") . | sha256sum }}
        {{- if (include "rabbitmq.createTlsSecret" . ) }}
        checksum/configTLS: {{ include (print $.Template.BasePath "/tls-secrets.yaml") . | sha256sum }}
        {{- end }}
        {{- if or (not .Values.auth.existingErlangSecret) (not .Values.auth.existingPasswordSecret) .Values.extraSecrets }}
        checksum/secret: {{ include (print $.Template.BasePath "/secrets.yaml") . | sha256sum }}
        {{- end }}
        {{- if .Values.initScripts }}
        checksum/initScripts: {{ include (print $.Template.BasePath "/init-configmap.yaml") . | sha256sum }}
        {{- end }}
        {{- if .Values.podAnnotations }}
        {{- include "common.tplvalues.render" (dict "value" .Values.podAnnotations "context" $) | nindent 8 }}
        {{- end }}
        {{- if and .Values.metrics.enabled .Values.metrics.podAnnotations }}
        {{- include "common.tplvalues.render" (dict "value" .Values.metrics.podAnnotations "context" $) | nindent 8 }}
        {{- end }}
    spec:
      {{- include "rabbitmq.imagePullSecrets" . | nindent 6 }}
      {{- if .Values.schedulerName }}
      schedulerName: {{ .Values.schedulerName | quote }}
      {{- end }}
      serviceAccountName: {{ template "rabbitmq.serviceAccountName" . }}
      {{- if .Values.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.affinity "context" .) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.podAffinityPreset "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.podAntiAffinityPreset "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.nodeAffinityPreset.type "key" .Values.nodeAffinityPreset.key "values" .Values.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
      {{- if .Values.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.nodeSelector "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName }}
      {{- end }}
      {{- if .Values.podSecurityContext.enabled }}
      securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.podSecurityContext "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.dnsPolicy }}
      dnsPolicy: {{ .Values.dnsPolicy }}
      {{- end }}
      {{- if .Values.dnsConfig }}
      dnsConfig: {{- include "common.tplvalues.render" (dict "value" .Values.dnsConfig "context" .) | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      enableServiceLinks: {{ .Values.enableServiceLinks }}
      initContainers:
        {{- if and .Values.volumePermissions.enabled .Values.persistence.enabled }}
        - name: volume-permissions
          image: {{ include "rabbitmq.volumePermissions.image" . }}
          imagePullPolicy: {{ .Values.volumePermissions.image.pullPolicy | quote }}
          command:
            - /bin/bash
          args:
            - -ec
            - |
              mkdir -p "{{ .Values.persistence.mountPath }}"
              {{- if eq ( toString ( .Values.volumePermissions.containerSecurityContext.runAsUser )) "auto" }}
              chown `id -u`:`id -G | cut -d " " -f2` {{ .Values.persistence.mountPath }}
              {{- else }}
              chown "{{ .Values.containerSecurityContext.runAsUser }}:{{ .Values.podSecurityContext.fsGroup }}" "{{ .Values.persistence.mountPath }}"
              {{- end }}
              find "{{ .Values.persistence.mountPath }}" -mindepth 1 -maxdepth 1 -not -name ".snapshot" -not -name "lost+found" | \
              {{- if eq ( toString ( .Values.volumePermissions.containerSecurityContext.runAsUser )) "auto" }}
                xargs -r chown -R `id -u`:`id -G | cut -d " " -f2`
              {{- else }}
                xargs -r chown -R "{{ .Values.containerSecurityContext.runAsUser }}:{{ .Values.podSecurityContext.fsGroup }}"
              {{- end }}
          {{- if eq ( toString ( .Values.volumePermissions.containerSecurityContext.runAsUser )) "auto" }}
          securityContext: {{- omit .Values.volumePermissions.containerSecurityContext "runAsUser" | toYaml | nindent 12 }}
          {{- else }}
          securityContext: {{- .Values.volumePermissions.containerSecurityContext | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.volumePermissions.resources }}
          resources: {{- include "common.tplvalues.render" (dict "value" .Values.volumePermissions.resources "context" $) | nindent 12 }}
          {{- else if ne .Values.volumePermissions.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.volumePermissions.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            - name: data
              mountPath: {{ .Values.persistence.mountPath }}
              {{- if .Values.persistence.subPath }}
              subPath: {{ .Values.persistence.subPath }}
              {{- end }}
        {{- end }}
        - name: prepare-plugins-dir
          image: {{ template "rabbitmq.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          {{- if .Values.resources }}
          resources: {{- include "common.tplvalues.render" (dict "value" .Values.resources "context" $) | nindent 12 }}
          {{- else if ne .Values.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.resourcesPreset) | nindent 12 }}
          {{- end }}
          {{- if .Values.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          command:
            - /bin/bash
          args:
            - -ec
            - |
              #!/bin/bash

              . /opt/bitnami/scripts/liblog.sh

              info "Copying plugins dir to empty dir"
              # In order to not break the possibility of installing custom plugins, we need
              # to make the plugins directory writable, so we need to copy it to an empty dir volume
              cp -r --preserve=mode /opt/bitnami/rabbitmq/plugins/ /emptydir/app-plugins-dir
          volumeMounts:
            - name: empty-dir
              mountPath: /emptydir
        {{- if .Values.initContainers }}
        {{- include "common.tplvalues.render" (dict "value" .Values.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: rabbitmq
          image: {{ template "rabbitmq.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          {{- if .Values.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.command "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.args "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.lifecycleHooks "context" $) | nindent 12 }}
          {{- else }}
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/bash
                  - -ec
                  - |
                    if [[ -f /opt/bitnami/scripts/rabbitmq/nodeshutdown.sh ]]; then
                        /opt/bitnami/scripts/rabbitmq/nodeshutdown.sh -t {{ .Values.terminationGracePeriodSeconds | quote }} -d {{ ternary "true" "false" .Values.image.debug | quote }}
                    else
                        rabbitmqctl stop_app
                    fi
          {{- end }}
          env:
            - name: BITNAMI_DEBUG
              value: {{ ternary "true" "false" (or .Values.image.debug .Values.diagnosticMode.enabled) | quote }}
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: MY_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            {{- $svcName := printf "%s-%s" (include "common.names.fullname" .) (default "headless" .Values.servicenameOverride) }}
            {{- if .Values.featureFlags }}
            - name: RABBITMQ_FEATURE_FLAGS
              value: {{ .Values.featureFlags }}
            {{- end }}
            - name: RABBITMQ_FORCE_BOOT
              value: {{ ternary "yes" "no" .Values.clustering.forceBoot | quote }}
            {{- if (eq "hostname" .Values.clustering.addressType) }}
            - name: RABBITMQ_NODE_NAME
              value: "rabbit@$(MY_POD_NAME).{{ $svcName }}.$(MY_POD_NAMESPACE).svc.{{ .Values.clusterDomain }}"
            {{- else }}
            - name: RABBITMQ_NODE_NAME
              value: "rabbit@$(MY_POD_NAME)"
            {{- end }}
            - name: RABBITMQ_UPDATE_PASSWORD
              value: {{ ternary "yes" "no" .Values.auth.updatePassword | quote }}
            - name: RABBITMQ_MNESIA_DIR
              value: "{{ .Values.persistence.mountPath }}/$(RABBITMQ_NODE_NAME)"
            - name: RABBITMQ_LDAP_ENABLE
              value: {{ ternary "yes" "no" .Values.ldap.enabled | quote }}
            {{- if .Values.ldap.enabled }}
            - name: RABBITMQ_LDAP_TLS
              value: {{ ternary "yes" "no" .Values.ldap.tls.enabled | quote }}
            - name: RABBITMQ_LDAP_SERVERS
              value: {{ .Values.ldap.servers | join "," | quote }}
            - name: RABBITMQ_LDAP_SERVERS_PORT
              value: {{ .Values.ldap.port | quote }}
            - name: RABBITMQ_LDAP_USER_DN_PATTERN
              value: {{ .Values.ldap.user_dn_pattern }}
            {{- end }}
            {{- if .Values.logs }}
            - name: RABBITMQ_LOGS
              value: {{ .Values.logs | quote }}
            {{- end }}
            - name: RABBITMQ_ULIMIT_NOFILES
              value: {{ .Values.ulimitNofiles | quote }}
            {{- if and .Values.maxAvailableSchedulers }}
            - name: RABBITMQ_SERVER_ADDITIONAL_ERL_ARGS
              value: {{ printf "+S %s:%s" (toString .Values.maxAvailableSchedulers) (toString .Values.onlineSchedulers) -}}
            {{- end }}
            - name: RABBITMQ_USE_LONGNAME
              value: "true"
            {{- if .Values.usePasswordFiles }}
            - name: RABBITMQ_ERL_COOKIE_FILE
              value: {{ printf "/opt/bitnami/rabbitmq/secrets/%s" (include "rabbitmq.secretErlangKey" .) }}
            {{- else }}
            - name: RABBITMQ_ERL_COOKIE
              valueFrom:
                secretKeyRef:
                  name: {{ template "rabbitmq.secretErlangName" . }}
                  key: {{ template "rabbitmq.secretErlangKey" . }}
            {{- end }}
            {{- if and .Values.clustering.rebalance (gt (.Values.replicaCount | int) 1) }}
            - name: RABBITMQ_CLUSTER_REBALANCE
              value: "true"
            {{- end }}
            - name: RABBITMQ_LOAD_DEFINITIONS
              value: {{ ternary "yes" "no" .Values.loadDefinition.enabled | quote }}
            - name: RABBITMQ_DEFINITIONS_FILE
              value: {{ .Values.loadDefinition.file | quote }}
            - name: RABBITMQ_SECURE_PASSWORD
              value: {{ ternary "yes" "no" (or .Values.auth.securePassword (not .Values.auth.password)) | quote }}
            - name: RABBITMQ_USERNAME
              value: {{ .Values.auth.username | quote }}
            {{- if .Values.usePasswordFiles }}
            - name: RABBITMQ_PASSWORD_FILE
              value: {{ printf "/opt/bitnami/rabbitmq/secrets/%s" (include "rabbitmq.secretPasswordKey" .) }}
            {{- else }}
            - name: RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "rabbitmq.secretPasswordName" . }}
                  key: {{ template "rabbitmq.secretPasswordKey" . }}
            {{- end }}
            - name: RABBITMQ_PLUGINS
              value: {{ include "rabbitmq.plugins" . | quote }}
            {{- if .Values.communityPlugins }}
            - name: RABBITMQ_COMMUNITY_PLUGINS
              value: {{ .Values.communityPlugins | quote }}
            {{- end }}
            {{- if .Values.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.extraEnvVarsSecret "context" $) }}
            {{- end }}
          ports:
            - name: amqp
              containerPort: {{ .Values.containerPorts.amqp }}
              {{- if .Values.hostPorts.amqp }}
              hostPort: {{ .Values.hostPorts.amqp }}
              {{- end }}
            - name: dist
              containerPort: {{ .Values.containerPorts.dist }}
            - name: stats
              containerPort: {{ .Values.containerPorts.manager }}
              {{- if .Values.hostPorts.manager }}
              hostPort: {{ .Values.hostPorts.manager }}
              {{- end }}
            - name: epmd
              containerPort: {{ .Values.containerPorts.epmd }}
            - name: metrics
              containerPort: {{ .Values.containerPorts.metrics }}
              {{- if .Values.hostPorts.metrics }}
              hostPort: {{ .Values.hostPorts.metrics }}
              {{- end }}
            {{- if .Values.auth.tls.enabled }}
            - name: amqp-tls
              containerPort: {{ .Values.containerPorts.amqpTls }}
              {{- if .Values.hostPorts.amqpTls }}
              hostPort: {{ .Values.hostPorts.amqpTls }}
              {{- end }}
            {{- end }}
            {{- if .Values.extraContainerPorts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.extraContainerPorts "context" $) | nindent 12 }}
            {{- end }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.livenessProbe "enabled") "context" $) | nindent 12 }}
            exec:
              command:
                - /bin/bash
                - -ec
                {{- if or (.Values.loadDefinition.enabled) (not (contains "rabbitmq_management" .Values.plugins )) }}
                - rabbitmq-diagnostics -q ping
                {{- else }}
                - curl -f --user {{ .Values.auth.username }}:{{ ternary "$(< $RABBITMQ_PASSWORD_FILE)" "$RABBITMQ_PASSWORD"  .Values.usePasswordFiles }} 127.0.0.1:{{ .Values.containerPorts.manager }}/api/health/checks/virtual-hosts
                {{- end }}
          {{- end }}
          {{- if .Values.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.readinessProbe "enabled") "context" $) | nindent 12 }}
            exec:
              command:
                - /bin/bash
                - -ec
                {{- if or (.Values.loadDefinition.enabled) (not (contains "rabbitmq_management" .Values.plugins )) }}
                - rabbitmq-diagnostics -q check_running && rabbitmq-diagnostics -q check_local_alarms
                {{- else }}
                - curl -f --user {{ .Values.auth.username }}:{{ ternary "$(< $RABBITMQ_PASSWORD_FILE)" "$RABBITMQ_PASSWORD"  .Values.usePasswordFiles }} 127.0.0.1:{{ .Values.containerPorts.manager }}/api/health/checks/local-alarms
                {{- end }}
          {{- end }}
          {{- if .Values.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.startupProbe "enabled") "context" $) | nindent 12 }}
            tcpSocket:
              port: {{ternary "amqp-tls" "amqp" .Values.auth.tls.enabled }}
          {{- end }}
          {{- end }}
          {{- if .Values.resources }}
          resources: {{- include "common.tplvalues.render" (dict "value" .Values.resources "context" $) | nindent 12 }}
          {{- else if ne .Values.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: configuration
              mountPath: /bitnami/rabbitmq/conf
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            - name: empty-dir
              mountPath: /opt/bitnami/rabbitmq/etc/rabbitmq
              subPath: app-conf-dir
            - name: empty-dir
              mountPath: /opt/bitnami/rabbitmq/var/lib/rabbitmq
              subPath: app-tmp-dir
            - name: empty-dir
              mountPath: /opt/bitnami/rabbitmq/.rabbitmq/
              subPath: app-erlang-cookie
            - name: empty-dir
              mountPath: /opt/bitnami/rabbitmq/var/log/rabbitmq
              subPath: app-logs-dir
            - name: empty-dir
              mountPath: /opt/bitnami/rabbitmq/plugins
              subPath: app-plugins-dir
            - name: data
              mountPath: {{ .Values.persistence.mountPath }}
              {{- if .Values.persistence.subPath }}
              subPath: {{ .Values.persistence.subPath }}
              {{- end }}
            {{- if  .Values.usePasswordFiles }}
            - name: rabbitmq-secrets
              mountPath: /opt/bitnami/rabbitmq/secrets
            {{- end }}
            {{- if .Values.auth.tls.enabled }}
            - name: certs
              mountPath: /opt/bitnami/rabbitmq/certs
            {{- end }}
            {{- if and .Values.ldap.tls.enabled .Values.ldap.tls.certificatesSecret }}
            - name: ldap-certs
              mountPath: {{ .Values.ldap.tls.certificatesMountPath }}
            {{- end }}
            {{- if .Values.loadDefinition.enabled }}
            - name: load-definition-volume
              mountPath: /app
              readOnly: true
            {{- end }}
            {{- if .Values.extraVolumeMounts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.extraVolumeMounts "context" $) | nindent 12 }}
            {{- end }}
            {{- if .Values.initScripts }}
            - name: custom-init-scripts
              mountPath: /docker-entrypoint-initdb.d/init-scripts
            {{- end }}
            {{- if .Values.initScriptsCM }}
            - name: custom-init-scripts-cm
              mountPath: /docker-entrypoint-initdb.d/init-scripts-cm
            {{- end }}
            {{- if .Values.initScriptsSecret }}
            - name: custom-init-scripts-secret
              mountPath: /docker-entrypoint-initdb.d/init-scripts-secret
            {{- end }}
        {{- if .Values.sidecars }}
        {{- include "common.tplvalues.render" (dict "value" .Values.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        - name: empty-dir
          emptyDir: {}
        {{- if .Values.auth.tls.enabled }}
        - name: certs
          projected:
            sources:
              - secret:
                  name: {{ template "rabbitmq.tlsSecretName" . }}
                  items:
                    {{- if not .Values.auth.tls.overrideCaCertificate }}
                    - key: {{ ternary "tls.crt" "ca.crt" .Values.auth.tls.existingSecretFullChain }}
                      path: ca_certificate.pem
                    {{- end }}
                    - key: tls.crt
                      path: server_certificate.pem
                    - key: tls.key
                      path: server_key.pem
              {{- if .Values.auth.tls.overrideCaCertificate }}
              - secret:
                  name: {{ .Values.auth.tls.overrideCaCertificate }}
                  items:
                    - key: ca.crt
                      path: ca_certificate.pem
              {{- end }}
        {{- end }}
        {{- if and .Values.ldap.tls.enabled .Values.ldap.tls.certificatesSecret }}
        - name: ldap-certs
          secret:
            secretName: {{ .Values.ldap.tls.certificatesSecret }}
        {{- end }}
        - name: configuration
          projected:
            sources:
              {{- if or (and (empty .Values.configurationExistingSecret) .Values.configuration) (and (not .Values.advancedConfigurationExistingSecret) .Values.advancedConfiguration) }}
              - secret:
                  name: {{ printf "%s-config" (include "common.names.fullname" .) }}
              {{- end }}
              {{- if and .Values.advancedConfigurationExistingSecret (not .Values.advancedConfiguration) }}
              - secret:
                  name: {{ tpl .Values.advancedConfigurationExistingSecret . | quote }}
              {{- end }}
              {{- if not (empty .Values.configurationExistingSecret) }}
              - secret:
                  name: {{ tpl .Values.configurationExistingSecret . | quote }}
              {{- end }}
        {{- if .Values.usePasswordFiles }}
        - name: rabbitmq-secrets
          projected:
            sources:
              - secret:
                  name: {{ template "rabbitmq.secretPasswordName" . }}
              - secret:
                  name: {{ template "rabbitmq.secretErlangName" . }}
        {{- end }}
        {{- if .Values.loadDefinition.enabled }}
        - name: load-definition-volume
          secret:
            secretName: {{ tpl .Values.loadDefinition.existingSecret . | quote }}
        {{- end }}
        {{- if .Values.initScripts }}
        - name: custom-init-scripts
          configMap:
            name: {{ template "rabbitmq.initScripts" . }}
        {{- end }}
        {{- if .Values.initScriptsCM }}
        - name: custom-init-scripts-cm
          configMap:
            name: {{ tpl .Values.initScriptsCM . | quote }}
        {{- end }}
        {{- if .Values.initScriptsSecret }}
        - name: custom-init-scripts-secret
          secret:
            secretName: {{ tpl .Values.initScriptsSecret . | quote }}
            defaultMode: 0755
        {{- end }}
        {{- if .Values.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.extraVolumes "context" $)  | nindent 8 }}
        {{- end }}
  {{- if not .Values.persistence.enabled }}
        - name: data
          emptyDir: {}
  {{- else if .Values.persistence.existingClaim }}
        - name: data
          persistentVolumeClaim:
            {{- with .Values.persistence.existingClaim }}
            claimName: {{ tpl . $ }}
            {{- end }}
  {{- else }}
  {{- if .Values.persistentVolumeClaimRetentionPolicy.enabled }}
  persistentVolumeClaimRetentionPolicy:
    whenDeleted: {{ .Values.persistentVolumeClaimRetentionPolicy.whenDeleted }}
    whenScaled: {{ .Values.persistentVolumeClaimRetentionPolicy.whenScaled }}
  {{- end }}
  volumeClaimTemplates:
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: data
        {{- $claimLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.persistence.labels .Values.commonLabels ) "context" . ) }}
        labels: {{- include "common.labels.matchLabels" ( dict "customLabels" $claimLabels "context" $ ) | nindent 10 }}
        {{- if .Values.persistence.annotations }}
        annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.persistence.annotations "context" $) | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
          {{- range .Values.persistence.accessModes }}
            - {{ . | quote }}
          {{- end }}
        resources:
          requests:
            storage: {{ .Values.persistence.size | quote }}
        {{ include "common.storage.class" (dict "persistence" .Values.persistence "global" .Values.global) }}
        {{- if .Values.persistence.selector }}
        selector: {{- include "common.tplvalues.render" (dict "value" .Values.persistence.selector "context" $) | nindent 10 }}
        {{- end -}}
  {{- end }}
