{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- $host := printf "%s.%s.svc.%s" (include "common.names.fullname" .) (include "common.names.namespace" .) .Values.clusterDomain }}
{{- $port := print .Values.service.ports.amqp }}
{{- $user := print .Values.auth.username }}
{{- $password := include "common.secrets.passwords.manage" (dict "secret" (include "rabbitmq.secretPasswordName" .) "key" (include "rabbitmq.secretPasswordKey" .) "length" 16 "providedValues" (list "auth.password") "skipB64enc" true "skipQuote" true "honorProvidedValues" true "context" $) }}
{{- $erlangCookie := include "common.secrets.passwords.manage" (dict "secret" (include "rabbitmq.secretErlangName" .) "key" (include "rabbitmq.secretErlangKey" .) "length" 32 "failOnNew" false "providedValues" (list "auth.erlangCookie") "honorProvidedValues" true "context" $) }}
{{- if or (not .Values.auth.existingErlangSecret) (not .Values.auth.existingPasswordSecret) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if (not .Values.auth.existingPasswordSecret) }}
  rabbitmq-password: {{ print $password | b64enc | quote }}
  {{- end }}
  {{- if (not .Values.auth.existingErlangSecret ) }}
  rabbitmq-erlang-cookie: {{ print $erlangCookie }}
  {{- end }}
{{- end }}
{{- range $key, $value := .Values.extraSecrets }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ ternary (printf "%s-%s" $.Release.Name $key) $key $.Values.extraSecretsPrependReleaseName }}
  namespace: {{ include "common.names.namespace" $ | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $.Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if $.Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $.Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
stringData: {{- include "common.tplvalues.render" (dict "value" $value "context" $) | nindent 2 }}
{{- end }}
{{- if .Values.serviceBindings.enabled }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}-svcbind
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: servicebinding.io/rabbitmq
data:
  provider: {{ print "bitnami" | b64enc | quote }}
  type: {{ print "rabbitmq" | b64enc | quote }}
  host: {{ print $host | b64enc | quote }}
  port: {{ print $port | b64enc | quote }}
  username: {{ print $user | b64enc | quote }}
  password: {{ print $password | b64enc | quote }}
  uri: {{ printf "amqp://%s:%s@%s:%s" $user $password $host $port | b64enc | quote }}
{{- end }}
