# Copyright Broadcom, Inc. All Rights Reserved.
# SPDX-License-Identifier: APACHE-2.0

annotations:
  category: Infrastructure
  images: |
    - name: os-shell
      image: docker.io/bitnami/os-shell:12-debian-12-r46
    - name: rabbitmq
      image: docker.io/bitnami/rabbitmq:4.1.1-debian-12-r2
  licenses: Apache-2.0
  tanzuCategory: service
apiVersion: v2
appVersion: 4.1.1
dependencies:
- name: common
  repository: oci://registry-1.docker.io/bitnamicharts
  tags:
  - bitnami-common
  version: 2.x.x
description: RabbitMQ is an open source general-purpose message broker that is designed
  for consistent, highly-available messaging scenarios (both synchronous and asynchronous).
home: https://bitnami.com
icon: https://dyltqmyl993wv.cloudfront.net/assets/stacks/rabbitmq/img/rabbitmq-stack-220x234.png
keywords:
- rabbitmq
- message queue
- AMQP
maintainers:
- name: Broadcom, Inc. All Rights Reserved.
  url: https://github.com/bitnami/charts
name: rabbitmq
sources:
- https://github.com/bitnami/charts/tree/main/bitnami/rabbitmq
version: 16.0.8
