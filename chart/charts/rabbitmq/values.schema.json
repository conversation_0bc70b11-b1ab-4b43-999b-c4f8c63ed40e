{"$schema": "http://json-schema.org/schema#", "type": "object", "properties": {"auth": {"type": "object", "properties": {"username": {"type": "string", "title": "RabbitMQ user", "form": true}, "password": {"type": "string", "title": "RabbitMQ password", "form": true, "description": "Defaults to a random 10-character alphanumeric string if not set"}}}, "extraConfiguration": {"type": "string", "title": "Extra RabbitMQ Configuration", "form": true, "render": "textArea", "description": "Extra configuration to be appended to RabbitMQ Configuration"}, "replicaCount": {"type": "integer", "form": true, "title": "Number of replicas", "description": "Number of replicas to deploy"}, "persistence": {"type": "object", "title": "Persistence configuration", "form": true, "properties": {"enabled": {"type": "boolean", "form": true, "title": "Enable persistence", "description": "Enable persistence using Persistent Volume Claims"}, "size": {"type": "string", "title": "Persistent Volume Size", "form": true, "render": "slider", "sliderMin": 1, "sliderMax": 100, "sliderUnit": "Gi", "hidden": {"value": false, "path": "persistence/enabled"}}}}, "volumePermissions": {"type": "object", "properties": {"enabled": {"type": "boolean", "form": true, "title": "Enable Init Containers", "description": "Use an init container to set required folder permissions on the data volume before mounting it in the final destination"}}}, "metrics": {"type": "object", "form": true, "title": "Prometheus metrics details", "properties": {"enabled": {"type": "boolean", "title": "Enable Prometheus metrics for RabbitMQ", "description": "Install Prometheus plugin in the RabbitMQ container", "form": true}, "serviceMonitor": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "Create Prometheus Operator ServiceMonitor", "description": "Create a ServiceMonitor to track metrics using Prometheus Operator", "form": true, "hidden": {"value": false, "path": "metrics/enabled"}}}}}}}}