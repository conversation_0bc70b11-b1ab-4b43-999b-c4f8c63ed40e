arg=$1
name=qdrant

status=$(docker inspect $name | grep Status | cut -d '"' -f 4)

if [ ! -z "$status" ]; then
  if [ "$status" != "running" ]; then
    docker start $name
  fi
  echo "Already running."
  exit 0
else
  echo "Container not found; attempt to start"
fi

if [[ "$arg" == '-i' ]]; then
  docker run -p 6333:6333 -p 6334:6334 --name $name --rm -it -v $HOME/qdrant_storage:/qdrant/storage:z qdrant/qdrant
else
  docker run -p 6333:6333 -p 6334:6334 --name $name -d -v $HOME/qdrant_storage:/qdrant/storage:z qdrant/qdrant
fi