#%%
import pandas as pd

# Load the CSV files
baseline = pd.read_csv('baseline_metrics.csv')
# no_corr = pd.read_csv('no-corrections_metrics.csv')
no_corr = pd.read_csv('subquery_test_metrics.csv')

# Metric Comparison: With vs. Without Q-A
# We begin by loading the two CSV files (one with the question-answer baseline, one without) and aligning them on the `user_input` column. This ensures we compare the same questions side-by-side. After loading, we compute the difference for each metric (`answer_correctness`, `semantic_similarity`, `context_precision`, `context_recall`) by subtracting the "no-corrections" value from the baseline value. The charts below illustrate how the average difference (baseline minus no-correction) varies across all questions, using a moving-average smoothing (shown with a solid line) and a shaded area for the min-max range at each point.

# Merge on user_input to align rows and compute differences
merged = pd.merge(baseline, no_corr, on='user_input', suffixes=('_with','_without'))
metrics = ['answer_correctness','context_precision','context_recall']
for m in metrics:
    merged[f'{m}_diff'] = merged[f'{m}_with'] - merged[f'{m}_without']

print(f"Loaded {len(baseline)} rows, merged {len(merged)} rows (should match).")
merged.head(1)[['user_input'] + [m+'_with' for m in metrics] + [m+'_without' for m in metrics] + [m+'_diff' for m in metrics]]
#%%
# Compute summary stats for differences
diff_cols = [m+'_diff' for m in metrics]
print(merged[diff_cols].describe().loc[['mean','std','min','max']])
#%%
# Percentage-point difference: how many points on a 0–100% scale each metric moves
for m in metrics:
    merged[f'{m}_pp_diff'] = (merged[f'{m}_with'] - merged[f'{m}_without']) * 100
#%%
import os

import matplotlib.pyplot as plt

window = 21  # smoothing window
save_charts = False

for m in metrics:
    pp_col = f'{m}_pp_diff'
    df = merged[[pp_col]].copy()
    rolling_avg = df[pp_col].rolling(window, center=True)
    df['mean'] = rolling_avg.mean()
    # Experiment: try to show original, not averaged values
    # df['mean'] = df[pp_col]
    rolling_std = rolling_avg.std()
    df['min']  = df['mean'] - rolling_std
    df['max']  = df['mean'] + rolling_std
    plot_df = df.dropna()

    avg_pp = merged[pp_col].mean()
    mean_with    = merged[f'{m}_with'].mean()
    mean_without = merged[f'{m}_without'].mean()
    # percentage improvement on overall means
    global_pct = (mean_with - mean_without) / mean_without * 100

    plt.figure(figsize=(6,4))
    plt.axhline(0, color='gray', linestyle='--', linewidth=1)       # zero axis

    plt.axhline(
        global_pct, color='red', linestyle='-.', linewidth=1.5,
        label=f'Overall mean ↑ {global_pct:.1f}%'
    )
    
    plt.plot(plot_df['mean'], label='Smoothed mean Δ (pp)')          # smoothed mean
    plt.fill_between(
        plot_df.index, plot_df['min'], plot_df['max'],  # min–max band
        alpha=0.2, label='Min–Max (pp)'
    )
    plt.text(
        0.95, 0.05,                                   
        f'Avg gain: +{avg_pp:.1f} pp | Global improvement = +{global_pct:.1f}%',  # annotate overall avg
        transform=plt.gca().transAxes,
        ha='right', va='bottom',
        bbox=dict(facecolor='white', alpha=0.6, edgecolor='none')
    )
    plt.title(f'{m.replace("_"," ").title()} — Percentage-Point Improvement')
    plt.xlabel('Question Index')
    plt.ylabel('Δ in Percentage Points')
    plt.legend(loc='upper left')
    plt.tight_layout()

    if save_charts:
        fname = os.path.join(f'{m}_pp_improvement.png')
        plt.savefig(fname, dpi=300)
        print(f"Saved chart to {fname}")
    
    plt.show()

#%%
for m in metrics:
    mean_with    = merged[f'{m}_with'].mean()
    mean_without = merged[f'{m}_without'].mean()
    # percentage improvement on overall means
    global_pct = (mean_with - mean_without) / mean_without * 100
    # mean percentage-point gain
    mean_pp    = merged[f'{m}_pp_diff'].mean()
    print(f"{m:<20}:   Avg pp gain = +{mean_pp:.1f} pp   |   Global improvement = {global_pct:.1f}%")
#%%
