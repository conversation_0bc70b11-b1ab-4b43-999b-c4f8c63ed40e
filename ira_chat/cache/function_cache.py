import json
import hashlib


class FunctionCache:
    def __init__(self):
        self._data = {}

    def set(self, key, value):
        self._data[key] = value

    def get(self, key):
        return self._data.get(key)


_cache_provider = FunctionCache()


def cached(func):
    func_name = func.__name__

    def decorated(*args, **kwargs):
        args_str = json.dumps({'args': args, 'kwargs': kwargs})
        args_str = func_name + ': ' + args_str
        key = hashlib.sha1(args_str.encode()).hexdigest()
        value = _cache_provider.get(key)
        if not value:
            value = func(*args, **kwargs)
            _cache_provider.set(key, value)
        return value

    return decorated
