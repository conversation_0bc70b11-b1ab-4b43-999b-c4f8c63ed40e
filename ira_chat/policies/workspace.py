import logging
from typing import <PERSON><PERSON>

from ira_chat.db import api as db_api, models

logger = logging.getLogger(__name__)


async def check_user_in_workspace(user_id, ws: models.Workspace) -> Tuple[bool, int, list]:
    access_type = ws.access_type
    perms = 0
    allowed = False
    if access_type in models.ACCESS_TYPES_ANY:
        allowed = True

    groups = await db_api.list_groups_by_user_id(user_id, workspace_id=ws.id)
    if groups:
        allowed = True
        group_ids = [g.id for g in groups]
        for g in groups:
            perms = perms | g.permissions
        return allowed, perms, group_ids
    else:
        return allowed, 0, []
