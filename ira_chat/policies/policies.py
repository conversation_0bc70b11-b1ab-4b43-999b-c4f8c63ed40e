from typing import Type

from fastapi import HTTPException


class AccessMode:
    OWNER = 1
    ALL_CHATS_VIEW = 1 << 1
    FILE_MANAGE = 1 << 2
    WORKSPACE_MANAGE = 1 << 3
    GROUP_MANAGE = 1 << 4
    MEMBERS_MANAGE = 1 << 5
    METRICS_MANAGE = 1 << 6
    ALL_CHATS_MANAGE = 1 << 7


class OrgAccessMode:
    USER = 1
    OWNER = 1 << 1
    ALL_WS_VIEW = 1 << 2
    WORKSPACE_CREATE = 1 << 3
    WORKSPACE_DELETE = 1 << 4
    ORG_USERS_MANAGE = 1 << 5
    ORG_MANAGE = 1 << 6
    DATASET_MANAGE = 1 << 7


class AccessName:
    OWNER = 'owner'
    ALL_CHATS_VIEW = 'all_chat.view'
    FILE_MANAGE = 'file.manage'
    WORKSPACE_MANAGE = 'workspace.manage'
    GROUP_MANAGE = 'group.manage'
    MEMBERS_MANAGE = 'members.manage'
    METRICS_MANAGE = 'metrics.manage'
    ALL_CHATS_MANAGE = 'all_chat.manage'


class OrgAccessName:
    USER = 'user'
    OWNER = 'owner'
    ALL_WS_VIEW = 'all_ws.view'
    WORKSPACE_CREATE = 'workspace.create'
    WORKSPACE_DELETE = 'workspace.delete'
    ORG_USERS_MANAGE = 'org_users.manage'
    ORG_MANAGE = 'org.manage'
    DATASET_MANAGE = 'dataset.manage'


all_map = {getattr(AccessName, a): getattr(AccessMode, a) for a in AccessMode.__dict__ if a.isupper()}
org_all_map = {getattr(OrgAccessName, a): getattr(OrgAccessMode, a) for a in OrgAccessMode.__dict__ if a.isupper()}


def decode_permissions(perm: int, org=False) -> list[str]:
    perm_map = all_map if not org else org_all_map
    mode_class = AccessMode if not org else OrgAccessMode
    result = []
    for name, code in perm_map.items():
        if is_allowed(perm, code, mode_class):
            result.append(name)
    return result


def encode_permissions(perm_list: list[str], org=False) -> int:
    perm_map = all_map if not org else org_all_map
    result = 0
    for allowed_perm in perm_list:
        if allowed_perm in perm_map:
            result += perm_map[allowed_perm]
        else:
            raise HTTPException(400, f'Wrong permission name: {allowed_perm}')

    return result


def is_allowed(perm: int, check_perm: int, mode_class: Type[AccessMode | OrgAccessMode] = AccessMode):
    if (perm & mode_class.OWNER) == mode_class.OWNER:
        return True

    return (perm & check_perm) == check_perm
