
from typing import Optional
from nemoguardrails.actions import action


@action(is_system_action=True)
async def check_blocked_terms(context: Optional[dict] = None):
    bot_response = context.get("bot_message")
    sensitive_information = [
        "Access Keys",
        "Secret Key",
    ]

    for term in sensitive_information:
        if bot_response and term in bot_response.lower():
            return True

    return False


@action(is_system_action=True)
async def skip_action(context: Optional[dict] = None):
    return False
