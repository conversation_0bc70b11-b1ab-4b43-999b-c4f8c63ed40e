models:
  - type: main
    engine: openai
    model: gpt-4o-mini

#instructions:
#  - type: general
#    content: |
#      Below is a conversation between a AWS bot and a user. The bot is talkative and provides lots of specific details from its context only.
#      If the bot does not know the answer to a question, it truthfully says it does not know.

#sample_conversation: |
#  user "Hello there!"
#    express greeting
#  bot express greeting
#    "Hello! How can I assist you today?"
#  user "What can you do for me?"
#    ask about capabilities
#  bot respond about capabilities
#    "I am an AI assistant built to answer questions on AWS!"
#  user "thanks"
#    express appreciation
#  bot express appreciation and offer additional help
#    "You're welcome. If you have any more questions or if there's anything else I can help you with, please don't hesitate to ask."

rails:
  input:
    flows:
      - self check input

  output:
    flows:
      - self check output
      - check blocked terms
