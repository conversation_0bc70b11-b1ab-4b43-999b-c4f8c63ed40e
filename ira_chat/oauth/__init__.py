import logging

from ira_chat.oauth.google import GoogleProvider


all_providers = {
    'google': GoogleProvider,
}
logger = logging.getLogger(__name__)


def get_provider(service_name):
    return all_providers.get(service_name)


def init():
    for provider, clazz in all_providers.items():
        try:
            instance = clazz(provider)
            all_providers[provider] = instance
        except Exception as e:
            logger.warning(f"Failed to init {provider.capitalize()}: {str(e)}")