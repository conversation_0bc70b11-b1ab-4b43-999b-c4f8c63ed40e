import asyncio
import datetime
import functools
import logging
import os
import time

import requests

logger = logging.getLogger(__name__)


class APIClient:
    def __init__(self, api_token: str, base_url: str, is_workspace_token=False, verbose=False):
        self.base_url = base_url
        self.key = api_token
        self.headers = {'Authorization': f'Bearer {self.key}'}
        self.session = WrappedSession(verbose=verbose)
        self.session.headers = self.headers

        self.ws = None
        self.ws_id = None
        if is_workspace_token:
            self.ws = self.detect_workspace()
            self.ws_id = self.ws['id']

            if verbose:
                logger.info(f'[BUILDER] Working in workspace [id={self.ws_id}, name={self.ws["name"]}]')

    def __getattribute__(self, item):
        if item.startswith('a') and hasattr(self, item[1:]):
            return functools.partial(self.as_async, getattr(self, item[1:]))
        return super().__getattribute__(item)

    def detect_workspace(self):
        workspaces = self.workspace_list()
        return workspaces[0]

    def set_workspace(self, ws_id=None, ws_name=None):
        if ws_id:
            self.ws = self.workspace_get(ws_id)
            self.ws_id = ws_id
        elif ws_name:
            ws = [w for w in self.workspace_list() if w['name'] == ws_name]
            if not ws:
                raise ValueError(f'Workspace with name {ws_name} not found')
            self.ws = ws[0]
            self.ws_id = self.ws['id']
        else:
            raise ValueError('Either ws_id or ws_name must be provided')

        logger.info(f'[BUILDER] Working in workspace [id={self.ws_id}, name={self.ws["name"]}]')

    def _url(self, u):
        return os.path.join(self.base_url, 'api/v1', u.lstrip("/"))

    def _ws_url(self, u, ws_id=None):
        if not ws_id:
            ws_id = self.ws_id
        if not ws_id:
            # Try set workspace
            ws_id = os.getenv('IRA_API_WORKSPACE_ID')
            self.set_workspace(ws_id=int(ws_id))
        return self._url(os.path.join(f'workspaces/{ws_id}', u.lstrip('/')))

    @staticmethod
    async def as_async(func, *args, **kwargs):
        loop = asyncio.get_event_loop()
        if kwargs:
            func = functools.partial(func, **kwargs)
        return await loop.run_in_executor(None, func, *args)

    def auth_info(self):
        resp = self.session.get(self._url('/auth/info'))
        return resp.json()

    def workspace_list(self):
        resp = self.session.get(self._url('/workspaces'))
        return resp.json()['workspaces']

    def workspace_get(self, ws_id):
        resp = self.session.get(self._url(f'/workspaces/{ws_id}'))
        return resp.json()

    def extractor_list(self):
        resp = self.session.get(self._ws_url('/extractors'))
        return resp.json()

    def extractor_create(self, name, data_schema, config=None, description=None):
        data = {
            'title': name,
            'config': config,
            'description': description,
            'data_schema': data_schema,
        }
        resp = self.session.post(self._ws_url('/extractors'), json=data)
        return resp.json()

    def extractor_get(self, extractor_id):
        resp = self.session.get(self._ws_url(f'/extractors/{extractor_id}'))
        return resp.json()

    def extractor_update(self, extractor_id, data):
        resp = self.session.put(self._ws_url(f'/extractors/{extractor_id}'), json=data)
        return resp.json()

    def extractor_delete(self, extractor_id):
        resp = self.session.delete(self._ws_url(f'/extractors/{extractor_id}'))
        return resp

    def extractor_process(self, extractor_id, wait_files=False):
        url = self._ws_url(f'/extractors/{extractor_id}/process')
        if wait_files:
            url = f'{url}?wait_files=true'
        resp = self.session.post(url)
        return resp.json()

    def extractor_list_results(self, extractor_id):
        resp = self.session.get(self._ws_url(f'/extractors/{extractor_id}/results'))
        return resp.json()

    def extractor_get_result(self, extractor_id, result_id):
        resp = self.session.get(self._ws_url(f'/extractors/{extractor_id}/results/{result_id}'))
        return resp.json()

    def extractor_get_result_csv(self, extractor_id, result_id):
        resp = self.session.get(self._ws_url(f'/extractors/{extractor_id}/results/{result_id}/csv'))
        return resp.content

    def extractor_get_result_xlsx(self, extractor_id, result_id):
        resp = self.session.get(self._ws_url(f'/extractors/{extractor_id}/results/{result_id}/xlsx'))
        return resp.content

    def extractor_delete_result(self, extractor_id, result_id):
        resp = self.session.delete(self._ws_url(f'/extractors/{extractor_id}/results/{result_id}'))
        return resp

    def extractor_upload_file(self, extractor_id, file_name, file_data):
        resp = self.session.post(
            self._ws_url(f'/extractors/{extractor_id}/files'),
            files={'file': (file_name, file_data)}
        )
        return resp.json()

    def extractor_replace_file(self, extractor_id, file_id, file_name, file_data):
        resp = self.session.put(
            self._ws_url(f'/extractors/{extractor_id}/files/{file_id}'),
            files={'file': (file_name, file_data)}
        )
        return resp.json()

    def extractor_list_files(self, extractor_id):
        resp = self.session.get(self._ws_url(f'/extractors/{extractor_id}/files'))
        return resp.json()

    def extractor_get_file(self, extractor_id, file_id):
        resp = self.session.get(self._ws_url(f'/extractors/{extractor_id}/files/{file_id}'))
        return resp.json()

    def extractor_get_file_by_hash(self, extractor_id, hash):
        resp = self.session.get(self._ws_url(f'/extractors/{extractor_id}/files/by-hash/{hash}'))
        return resp.json()

    def extractor_get_file_contents(self, extractor_id, file_id):
        resp = self.session.get(self._ws_url(f'/extractors/{extractor_id}/files/{file_id}/contents'))
        return resp.json()

    def extractor_delete_file(self, extractor_id, file_id):
        resp = self.session.delete(self._ws_url(f'/extractors/{extractor_id}/files/{file_id}'))
        return resp

    def extractor_reindex_file(self, extractor_id, file_id):
        resp = self.session.put(self._ws_url(f'/extractors/{extractor_id}/files/{file_id}/reindex'))
        return resp.json()

    def extractor_download_file(self, extractor_id, file_id):
        resp = self.session.get(self._ws_url(f'/extractors/{extractor_id}/files/{file_id}/download'))
        return resp

    def extractor_search(self, extractor_id, query, limit: int = 10):
        url = self._ws_url(f'/extractors/{extractor_id}/search?query={query}')
        if limit:
            url = f'{url}&limit={limit}'
        resp = self.session.get(url)

        return resp.json()

    def extractor_item_list(self, extractor_id, limit: int = 25):
        url = self._ws_url(f'/extractors/{extractor_id}/items')
        if limit:
            url = f'{url}?limit={limit}'
        resp = self.session.get(url)
        return resp.json()

    def extractor_item_get(self, extractor_id, item_id):
        resp = self.session.get(self._ws_url(f'/extractors/{extractor_id}/items/{item_id}'))
        return resp.json()

    def extractor_item_delete(self, extractor_id, item_id):
        resp = self.session.delete(self._ws_url(f'/extractors/{extractor_id}/items/{item_id}'))
        return resp

    def extractor_item_create(self, extractor_id, input):
        data = {
            'input': input,
        }
        resp = self.session.post(self._ws_url(f'/extractors/{extractor_id}/items'), json=data)
        return resp.json()

    def timeline_create(self, title, data_schema, description=None):
        data = {
            'title': title,
            'data_schema': data_schema,
            'description': description,
        }
        resp = self.session.post(self._ws_url(f'/timelines'), json=data)
        return resp.json()

    def timeline_get(self, id: int):
        resp = self.session.get(self._ws_url(f'/timelines/{id}'))
        return resp.json()

    def timeline_get_file(self, timeline_id, file_id):
        resp = self.session.get(self._ws_url(f'/timelines/{timeline_id}/files/{file_id}'))
        return resp.json()

    def timeline_get_file_download(self, timeline_id, file_id):
        resp = self.session.get(self._ws_url(f'/timelines/{timeline_id}/files/{file_id}/download'))
        # Allow chunking
        return resp

    def timeline_file_upload(self, timeline_id, point_id, file_name, file_data):
        resp = self.session.post(
            self._ws_url(f'/timelines/{timeline_id}/points/{point_id}/files'),
            files={'file': (file_name, file_data)}
        )
        return resp.json()

    def timeline_file_replace(self, timeline_id, point_id, file_id, file_name, file_data):
        resp = self.session.put(
            self._ws_url(f'/timelines/{timeline_id}/points/{point_id}/files/{file_id}'),
            files={'file': (file_name, file_data)}
        )
        return resp.json()

    def timeline_file_delete(self, timeline_id, point_id, file_id):
        resp = self.session.delete(self._ws_url(f'/timelines/{timeline_id}/points/{point_id}/files/{file_id}'))
        return resp

    def timeline_get_historical_result(self, id: int):
        resp = self.session.get(self._ws_url(f'/timelines/{id}/historical_result'))
        return resp.json()

    def timeline_delete(self, id: int):
        resp = self.session.delete(self._ws_url(f'/timelines/{id}'))
        return resp

    def timeline_get_file_by_hash(self, timeline_id, hash):
        resp = self.session.get(self._ws_url(f'/timelines/{timeline_id}/files/check_hash?hash={hash}'))
        return resp.json()

    def point_create(self, timeline_id: int, title: str, date: str, description: str = None):
        data = {
            'title': title,
            'date': date,
            'description': description,
        }
        resp = self.session.post(self._ws_url(f'/timelines/{timeline_id}/points'), json=data)
        return resp.json()

    def point_list(self, timeline_id: int):
        resp = self.session.get(self._ws_url(f'/timelines/{timeline_id}/points'))
        return resp.json()

    def point_get(self, timeline_id: int, point_id: int):
        resp = self.session.get(self._ws_url(f'/timelines/{timeline_id}/points/{point_id}'))
        return resp.json()

    def point_get_by_date(self, timeline_id: int, date: str):
        resp = self.session.get(self._ws_url(f'/timelines/{timeline_id}/points/by-date/{date}'))
        return resp.json()

    def point_add_change(self, timeline_id: int, point_id: int, data: dict, description: str = None):
        data = {
            'description': description,
            'data': data,
        }
        resp = self.session.post(self._ws_url(f'/timelines/{timeline_id}/points/{point_id}/changes'), json=data)
        return resp.json()

    def timeline_process(self, timeline_id: int):
        resp = self.session.post(self._ws_url(f'/timelines/{timeline_id}/process'))
        return resp.json()

    def point_wait_process(self, timeline_id: int, point_id: int, timeout: int = 120):
        point = self.point_get(timeline_id, point_id)
        time.sleep(1)
        t = time.time()
        while point['status']['status'] in ['PREPROCESSING', 'PROCESSING']:
            time.sleep(1)
            point = self.point_get(timeline_id, point_id)
            if time.time() - t > timeout:
                raise TimeoutError('Timeout exceeded while waiting for point processing')

        return point

    def chat_list(self):
        resp = self.session.get(self._ws_url('/chats'))
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.json()

    def chat_create(self, title, config=None):
        resp = self.session.post(self._ws_url('/chats'), json={'title': title, 'config': config})
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        chat = resp.json()
        return chat

    def download_history(
        self,
        workspace_id: int,
        format: str = 'csv',
        include_context: bool = False,
        include_urls: bool = False,
        include_metrics: bool = False,
        start_date: str | datetime.datetime = None,
        end_date: str | datetime.datetime = None,
        csv_delimiter: str = ','
    ):
        params = {
            'format': format,
            'include_context': str(include_context).lower(),
            'include_urls': str(include_urls).lower(),
            'include_metrics': str(include_metrics).lower(),
            'csv_delimiter': csv_delimiter
        }

        # Add optional parameters only if they're not None
        if start_date is not None:
            if isinstance(start_date, datetime.datetime):
                start_date = start_date.strftime('%Y-%m-%dT%H:%M:%S')
            params['start_date'] = start_date
        if end_date is not None:
            if isinstance(end_date, datetime.datetime):
                end_date = end_date.strftime('%Y-%m-%dT%H:%M:%S')
            params['end_date'] = end_date

        # Build query string
        query_string = '&'.join(f'{k}={v}' for k, v in params.items())

        # Construct the URL
        url = self._ws_url(f'/chats/history?{query_string}', ws_id=workspace_id)
        resp = self.session.get(url)

        return resp.content

    def import_history(self, workspace_id: int, file_data: bytes, format: str = 'csv', csv_delimiter: str = ','):
        params = {'csv_delimiter': csv_delimiter}
        url = self._ws_url(f'/chats/history_import', ws_id=workspace_id)
        resp = self.session.post(
            url,
            files={'file': (f'history.{format}', file_data)},
            data=params,
        )

        return resp.json()

    def message_create(self, chat_id, content):
        resp = self.session.post(self._ws_url(f'/chats/{chat_id}/messages'), json={'content': content})
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())
        message = resp.json()
        return message

    def message_get(self, chat_id, message_id):
        url = f'/chats/{chat_id}/messages/{message_id}'
        resp = self.session.get(self._ws_url(url))
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())
        message = resp.json()
        return message

    def message_wait_for_completion(self, message, result_id=None, timeout=180):
        completed = False
        start = time.time()
        while not completed:
            try:
                message = self.message_get(message['chat_id'], message['id'])
            except Exception as e:
                if time.time() - start > timeout:
                    raise TimeoutError(f"Timed out waiting for the message id={message['id']}")
                time.sleep(1)
                continue
            if result_id:
                res = [r for r in message['results'] if r['id'] == result_id][0]
            else:
                res = message
            status = res["status"]
            completed = status in {'SUCCESS', 'ERROR', 'ERROR_REASON'}

            if time.time() - start > timeout:
                raise TimeoutError(f"Timed out waiting for the message id={message['id']}")
            time.sleep(1)
        return message

    def message_list(self, chat_id):
        url = f'/chats/{chat_id}/messages'
        resp = self.session.get(self._ws_url(url))
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.json()

    def chat_delete(self, chat_id):
        url = f'/chats/{chat_id}'
        resp = self.session.delete(self._ws_url(url))
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

    def detection_item_list(self, detection_id: int, limit: int = 50, page=1):
        url = self._ws_url(f'/detections/{detection_id}/items?limit={limit}&page={page}')
        resp = self.session.get(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.json()

    def detection_file_download(self, detection_file_id) -> bytes:
        url = self._ws_url(f'/detection_files/{detection_file_id}')
        resp = self.session.get(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.content

    def detection_file_get_meta(self, detection_file_id):
        url = self._ws_url(f'/detection_files/{detection_file_id}/meta')
        resp = self.session.get(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.json()

    def detection_file_delete(self, detection_file_id):
        url = self._ws_url(f'/detection_files/{detection_file_id}')
        resp = self.session.delete(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp

    def dataset_list(self, q: str = None):
        url = self._url('/datasets')
        if q:
            url = f'{url}?q={q}'
        resp = self.session.get(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.json()

    def dataset_file_list(
        self, dataset_id, q: str = None, page: int = 1,
        limit: int = 100, order: str = 'name', desc: bool = False, status: str = None
    ):
        url = self._url(f'/datasets/{dataset_id}/files')
        params = []
        if q:
            params.append(f'q={q}')
        if page:
            params.append(f'page={page}')
        if limit:
            params.append(f'limit={limit}')
        if order:
            params.append(f'order={order}')
        if status:
            params.append(f'status={status}')
        if desc:
            params.append(f'desc={str(desc).lower()}')

        if params:
            url = f'{url}?{"&".join(params)}'
        resp = self.session.get(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.json()

    def dataset_file_upload(self, dataset_id, fp, name, wait: bool = True):
        url = self._url(f'/datasets/{dataset_id}/files')
        if wait:
            url = f'{url}?wait=true'
        resp = self.session.post(url, files={'file': (name, fp)}, timeout=300)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())
        res = resp.json()
        return res

    def dataset_file_get(self, dataset_id, file_id):
        url = self._url(f'/datasets/{dataset_id}/files/{file_id}')
        resp = self.session.get(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.json()

    def dataset_file_download(self, dataset_id, file_id) -> bytes:
        url = self._url(f'/datasets/{dataset_id}/files/{file_id}/download')
        resp = self.session.get(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.content

    def dataset_file_delete(self, dataset_id, file_id):
        url = self._url(f'/datasets/{dataset_id}/files/{file_id}')
        resp = self.session.delete(url)

        return resp


class WrappedSession(requests.Session):
    def __init__(self, verbose=False):
        super().__init__()
        self.verbose = verbose

    def request(
        self,
        method: str | bytes,
        url: str | bytes,
        *args, **kwargs
    ):
        url_path = '/' + '/'.join(url.split('/')[3:])
        if self.verbose:
            logger.info(f"[BUILDER] {method} {url_path}")
        resp = super().request(method, url, *args, **kwargs)
        if self.verbose:
            logger.info(f"[BUILDER] {method} {url_path} -> {resp.status_code}")

        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())
        return resp


def raise_for_status(resp):
    if resp.status_code >= 400:
        raise ValueError(resp.content.decode())
