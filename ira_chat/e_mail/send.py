import smtplib
import ssl
import logging
import os
from email.message import EmailMessage

import jinja2
from jinja2 import Environment, FileSystemLoader

from ira_chat.credentials import credentials
from ira_chat.utils import utils


class SmtpConfig:
    username = None
    password = None
    host = None
    addr = None
    from_ = None
    from_name = None


logger = logging.getLogger(__name__)
context = ssl.create_default_context()
smtp_config: SmtpConfig = None


def init_smtp():
    try:
        smtp = credentials.read_credentials('smtp')
        global smtp_config
        smtp_config = SmtpConfig()
        smtp_config.username = smtp.get('username')
        smtp_config.password = smtp.get('password')
        smtp_config.host = smtp.get('host')
        smtp_config.addr = smtp.get('addr')
        smtp_config.from_ = smtp.get('from')
        smtp_config.from_name = smtp.get('from_name')
    except Exception as e:
        print(f'Error while reading smtp.json: {str(e)}')


def send(to, subject, body):
    # pattern = ""
    # pattern += f"From: {smtp_config.from_name} <{smtp_config.from_}>\r\n"
    # pattern += f"To: {to}\r\n"
    # pattern += "MIME-Version: 1.0\r\nContent-type: text/html" + "\r\n"
    # pattern += f"Subject: {subject}\r\n\r\n{body}"

    em = EmailMessage()

    em['Subject'] = subject
    em['From'] = f"{smtp_config.from_name} <{smtp_config.from_}>"
    em['To'] = to
    em.add_header('Content-Type', 'text/html')
    em.set_payload(body, charset='utf-8')

    hostport = smtp_config.addr.split(':')
    host = hostport[0]
    port = hostport[1] if len(hostport) > 1 else 443

    context = ssl.create_default_context()

    # print(em.get_content())
    with smtplib.SMTP(host, port) as server:
        server.starttls(context=context)
        server.login(smtp_config.username, smtp_config.password)
        # server.sendmail(smtp_config.from_, [to], pattern)
        server.send_message(em)


def get_base_host(base_url):
    splitted = base_url.split('/')
    return splitted[2]


def send_confirmation(user, confirm, base_url, new_password=None):
    logger.info(f'Sending confirmation to user_id={user.id}')

    email = user.login
    ctx = {
        'name': user.name or 'User',
        'base_url': get_base_host(base_url),
        'confirm_url': f'{base_url}/api/v1/confirm?token={confirm.id}' if confirm else None,
        'new_password': new_password,
    }
    dirname = os.path.dirname(__file__)
    loader = FileSystemLoader(os.path.join(dirname, 'templates'))
    jinja_env = Environment(loader=loader)
    template_file = os.path.basename('confirm.html')
    template = jinja_env.get_template(template_file)
    rendered = template.render(ctx)
    if confirm:
        subject = f'Email verification at {base_url}'
    else:
        subject = f'Your temporary password at {base_url}'

    send(email, subject, rendered)
    logger.info(f"Sent to user_id={user.id}.")


def send_reset_password(user, confirm, base_url):
    logger.info(f'Sending reset password to user_id={user.id}')

    email = user.login
    future_pass = utils.generate_pass_from_uuid(confirm.id)
    ctx = {
        'name': user.name or 'User',
        'base_url': get_base_host(base_url),
        'password': future_pass,
        'confirm_link': f'{base_url}/api/v1/confirm/reset?token={confirm.id}'
    }
    dirname = os.path.dirname(__file__)
    loader = FileSystemLoader(os.path.join(dirname, 'templates'))
    jinja_env = Environment(loader=loader)
    template_file = os.path.basename('reset_password.html')
    template = jinja_env.get_template(template_file)
    rendered = template.render(ctx)
    subject = f'Reset password request at {base_url}'

    send(email, subject, rendered)
    logger.info(f"Sent to user_id={user.id}.")


def send_invite(email, base_url, ws, org, invite):
    logger.info(f'Sending invitation to user_email={email}')

    def get_invite_url(base_url, invite_db):
        return f'{base_url}/invite/{invite_db.id}'

    ctx = {
        'base_url': get_base_host(base_url),
        'workspace': ws,
        'org': org,
        'invite_url': get_invite_url(base_url, invite),
    }
    dirname = os.path.dirname(__file__)
    loader = FileSystemLoader(os.path.join(dirname, 'templates'))
    jinja_env = Environment(loader=loader)
    template_file = os.path.basename('invite.html')
    template = jinja_env.get_template(template_file)
    rendered = template.render(ctx)
    subject = f'Invitation to "{ws.name if ws else org.name}" at {base_url}'

    send(email, subject, rendered)
    logger.info(f"Sent to user_email={email}.")
