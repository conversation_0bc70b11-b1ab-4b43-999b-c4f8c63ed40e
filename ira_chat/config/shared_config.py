import logging
import os

from ira_chat.files.local_manager import LocalManager
from ira_chat.files.remote_manager import RemoteManager
from ira_chat.doc_converter.local_converter import LocalConverter
from ira_chat.doc_converter.remote_converter import RemoteConverter
from ira_chat.utils import singleton

logger = logging.getLogger(__name__)


class SharedConfig(object, metaclass=singleton.Singleton):
    def __init__(self):
        if os.environ.get('FILE_API_URL'):
            logger.info('Initialize RemoteManager')
            file_manager = RemoteManager()
        else:
            logger.info('Initialize LocalManager')
            file_manager = LocalManager()

        if os.environ.get('CONVERTER_API_URL'):
            logger.info('Initialize RemoteConverter')
            doc_converter = RemoteConverter()
        else:
            logger.info('CONVERTER_API_URL is not set, Initialize LocalConverter')
            doc_converter = LocalConverter()

        self.file_manager = file_manager
        self.doc_converter = doc_converter
