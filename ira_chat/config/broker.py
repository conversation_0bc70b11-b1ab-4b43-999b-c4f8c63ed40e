
import os
import logging
from taskiq import InMemoryBroker
from taskiq.result_backends.dummy import DummyResultBackend
from taskiq_aio_pika import AioPikaBroker

from ira_chat.utils import utils

logger = logging.getLogger(__name__)


_BROKER = None


def get_broker() -> InMemoryBroker | AioPikaBroker:
    utils.setup_logging()
    global _BROKER
    if _BROKER is None:
        _BROKER = init_broker()
    return _BROKER


def init_broker():
    if os.environ.get('RABBITMQ_URL'):
        logger.info('RABBITMQ_URL is set, Initialize RabbitMQ broker')
        broker_url = os.environ.get('RABBITMQ_URL')
        broker = AioPikaBroker(
            broker_url
        ).with_result_backend(DummyResultBackend())
    else:
        logger.info('RABBITMQ_URL is not set, Initialize InMemory broker')
        broker = InMemoryBroker()

    return broker


async def startup_broker():
    broker = get_broker()

    await broker.startup()
    return broker


async def shutdown_broker():
    broker = get_broker()

    logger.info('Shutdown the broker connection...')
    await broker.shutdown()
    return broker
