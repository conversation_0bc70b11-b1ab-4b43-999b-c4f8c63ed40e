from alembic.config import Config
from alembic import command
from sqlalchemy import create_engine
from sqlalchemy.sql import text

from ira_chat.utils import utils


def run_migrations(script_location: str = None) -> None:
    lock_id = 1
    # Create sync engine for locks
    engine = create_engine(utils.generate_connection_string('psycopg2'))

    with engine.connect() as conn:
        stmt = text('SELECT pg_advisory_lock(:id);')
        params = {'id': lock_id}
        conn.execute(stmt, params)

        try:
            alembic_cfg = Config()
            alembic_cfg.set_main_option('script_location', script_location)
            command.upgrade(alembic_cfg, 'head')
        finally:
            stmt = text('SELECT pg_advisory_unlock(:id);')
            conn.execute(stmt, params)
