import os

import blowfish
import base64
import json


BLOCK_SIZE = 8
KEY = os.getenv('BLOWFISH_SALT', 'default blowfish salt').encode()


def check_and_pad(data: bytes, block_size=8):
    mod = len(data) % block_size
    if mod != 0:
        to_add = block_size - mod
        data += b'\0' * to_add
    elif len(data) == 0:
        data = b'\0' * block_size
    return data


def encrypt(data: bytes, key: bytes):
    data = check_and_pad(data)
    iv = data[:BLOCK_SIZE]
    cipher = blowfish.Cipher(key=key)

    encrypted = cipher.encrypt_cbc(iv + data, iv)
    encrypted = b''.join(list(encrypted))
    return encrypted


def encrypt_string(data: str) -> str:
    res = encrypt(data.encode(), KEY)
    return base64.encodebytes(res).decode()


def decrypt_string(data: str) -> str:
    raw_s = base64.decodebytes(data.encode())
    res = decrypt(raw_s, KEY)
    return res.decode()


def encrypt_dict(data: dict) -> str:
    s = json.dumps(data)
    return encrypt_string(s)


def decrypt_dict(data: str) -> dict:
    s = decrypt_string(data)
    return json.loads(s)


def decrypt(encrypted: bytes, key: bytes):
    dcipher = blowfish.Cipher(key=key)

    d_iv = encrypted[:BLOCK_SIZE]
    d_data = encrypted[BLOCK_SIZE:]
    decrypted = dcipher.decrypt_cbc(d_data, d_iv)
    decrypted = b''.join(list(decrypted))
    return decrypted.rstrip(b"\0")
