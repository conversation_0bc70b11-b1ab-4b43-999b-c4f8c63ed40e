import asyncio
import functools
import logging
import random
import time
from typing import Awaitable, Callable, Tuple, Type, Union

logger = logging.getLogger(__name__)


def retry(
    expected_exception: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception,
    tries: int = 2,
    delay: Union[int, float] = 0,
    backoff: float = 1,
    ignore_exceptions: bool = False,
    jitter: Union[int, float] = 0,
    max_delay: Union[int, float] = 0,
    on_exception: Union[Awaitable, Callable, None] = None,
):
    """Retry decorator for synchronous and asynchronous functions.

    Arguments:
        expected_exception:
            exception or tuple of exceptions (default BaseException).

        tries:
            how much times the function will be retried, value -1 is infinite (default 2).
        delay:
            time interval between the tries (default 0).
        backoff:
            current_delay = delay * backoff ^ (number_of_retries) (default False).
        ignore_exceptions:
            only log error but not raise exception if attempts exceeds (default False).
        jitter:
            maximum value of deviation from the current_delay (default 0).
        max_delay:
            current_delay = min(current_delay, maximum_backoff) (default 0).
        on_exception:
            function that called or await on error occurs (default None).
            Be aware if a decorating function is synchronous on_exception function must be 
            synchronous too and accordingly for asynchronous function on_exception must be
            asynchronous.
    """

    if backoff:
        assert delay > 0, "with backoff backoff must be greater than 0."
        if max_delay:
            assert max_delay > delay, "max_delay must be greater than backoff."
    else:
        assert delay >= 0, "delay must be greater than or equal 0."
        assert not max_delay, \
            "max_delay do not makes sense without backoff."

    def decorator(function):

        def handle_error(exception: Exception, count: int) -> Tuple[int, float]:
            logger.warning(f"Retry decorator catch error in {function.__name__}: {repr(exception)}")

            count += 1
            if tries == -1 or count >= tries:
                if ignore_exceptions:
                    current_delay = 0
                    return count, current_delay
                raise exception

            current_delay = delay
            if backoff:
                current_delay = delay * (backoff ** count)
            if jitter:
                current_delay = current_delay + jitter
            if max_delay:
                current_delay = min(current_delay, max_delay)

            return count, current_delay

        def attempts_exceeds():
            logger.error(f"Retry decorator exceeds attempts in {function.__name__}")

        @functools.wraps(function)
        def wrapper(*args, **kwargs):
            count = 0
            while tries == -1 or count < tries:
                try:
                    return function(*args, **kwargs)
                except expected_exception as e:
                    count, current_delay = handle_error(e, count)
                    if on_exception:
                        on_exception()
                    time.sleep(current_delay)
            attempts_exceeds()

        @functools.wraps(function)
        async def async_wrapper(*args, **kwargs):
            count = 0
            while tries == -1 or count < tries:
                try:
                    return await function(*args, **kwargs)
                except expected_exception as e:
                    count, current_delay = handle_error(e, count)
                    if on_exception:
                        await on_exception()
                    await asyncio.sleep(current_delay)
            attempts_exceeds()

        if asyncio.iscoroutinefunction(function):
            if on_exception:
                assert asyncio.iscoroutinefunction(on_exception), \
                    "on_exception must be async as decorating function"
            return async_wrapper
        else:
            if on_exception:
                assert not asyncio.iscoroutinefunction(on_exception), \
                    "on_exception must be sync as decorating function"
                assert callable(function), "on_exception must be callable"
            return wrapper

    return decorator
