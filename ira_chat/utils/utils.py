import hashlib
import json
import logging
import os
import uuid

import ulid
import yaml
from fastapi import HTTPException


API_PREFIX = '/api/v1'
DEFAULT_LLMS = {
    'gemini': 'gemini-1.5-pro-latest',
    'google': 'gemini-1.5-pro-latest',
    'ollama': 'mistral',
    'openai': 'gpt-4o',
    'azure_openai': 'gpt-4o',
    'anthropic': 'claude-3-haiku-20240307',
    'mistralai': 'mistral-large-latest',
    'groq': 'mixtral-8x7b-32768',
}

DEFAULT_VISION_LLMS = {
    'gemini': 'gemini-1.5-pro-latest',
    'google': 'gemini-1.5-pro-latest',
    'ollama': 'llava:34b',
    'openai': 'gpt-4o',
    'azure_openai': 'gpt-4o',
    'groq': 'llama-3.2-11b-vision-preview',
    'anthropic': 'claude-3-5-sonnet-20240620',  # claude-3-sonnet-20240229, claude-3-haiku-20240229
}


logger = logging.getLogger(__name__)


def setup_logging():
    level = logging.INFO
    if os.getenv('DEBUG') == 'true':
        level = logging.DEBUG
    if os.getenv('DEBUG_NEMOGUARDRAILS') != 'true':
        logging.getLogger('nemoguardrails').setLevel(logging.WARNING)
    logging.basicConfig(
        format='%(asctime)s %(levelname)-5s %(name)-10s [-] %(message)s',
        level=level
    )
    logging.root.setLevel(level)


def load_json(filename):
    with open(filename, 'r') as f:
        data = f.read()
    return json.loads(data)


def load_yaml(filename):
    with open(filename, 'r') as f:
        data = f.read()
    return yaml.safe_load(data)


def validate_dataset_name(name):
    if '/' in name:
        raise HTTPException(422, 'Dataset name cannot contain "/"')


def boolean_string(s):
    if isinstance(s, bool):
        return s
    if s is None:
        return False

    s = s.lower()
    if not s:
        return False
    if s not in {'false', 'true'}:
        raise ValueError('Not a valid boolean string')
    return s == 'true'


def generate_unicode_uuid():
    return str(uuid.uuid4())


def generate_unicode_ulid() -> str:
    return str(ulid.ULID().to_uuid4())


def generate_pass_from_uuid(uuid: str):
    u = uuid.replace('-', '')
    base = u[1:17:2]
    return ''.join([a.upper() if i % 2 == 0 else a for i, a in enumerate(base)])


def generate_password():
    return generate_pass_from_uuid(generate_unicode_uuid())


def get_limit_offset(limit, page):
    offset = limit * (page - 1)
    return limit, offset


def llm_model_name():
    return os.getenv('LLM_MODEL_NAME', 'gpt-4o')


def llm_model_name_by_llm_type(llm_type: str):
    if llm_type in DEFAULT_LLMS:
        return DEFAULT_LLMS[llm_type]
    return None


def llm_vision_model_by_llm_type(llm_type: str):
    if llm_type in DEFAULT_VISION_LLMS:
        return DEFAULT_VISION_LLMS[llm_type]
    return None


def generate_connection_string(connector='asyncpg'):
    db_type = os.environ.get('DB_TYPE', 'postgresql')
    user = os.environ.get('PGUSER')
    database = os.environ.get('PGDATABASE')
    password = os.environ.get('PGPASSWORD')
    host = os.environ.get('PGHOST')
    port = os.environ.get('PGPORT', 5432)

    return f'{db_type}+{connector}://{user}:{password}@{host}:{port}/{database}'


def get_base_url(req):
    global_base = get_global_base_url()
    req_base = str(req.base_url).removesuffix('/')
    if global_base.startswith('https'):
        req_base = req_base.replace('http', 'https', 1)
    return req_base


def get_global_base_url():
    return os.environ.get('BASE_URL', '')


def get_global_schema():
    return get_global_base_url().split(':')[0]


def get_schema(req):
    return req.base_url.scheme


def get_provided_answer_filename(ws):
    return f'provided_answers_ws_{ws.name}.txt'


async def log_exceptions(awaitable):
    try:
        await awaitable
    except Exception as e:
        logger.exception(str(e))


def build_status(status, msg=None, string=False) -> dict | str:
    st = {"status": status, "message": msg}
    return json.dumps(st) if string else st


def maybe_dict(v: str | None, default=None):
    if not v:
        return default
    # noinspection PyBroadException
    try:
        return json.loads(v)
    except:
        return v


def clean_string(s: str):
    return s.replace('\x00', '')


def hash_sha256(data: str | bytes) -> str:
    if isinstance(data, str):
        data = data.encode()
    return hashlib.sha256(data).hexdigest()
