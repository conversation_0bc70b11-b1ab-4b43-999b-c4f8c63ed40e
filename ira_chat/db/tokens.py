from sqlalchemy import delete
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def create_token(values: dict, session=None):
    token = models.Token(**values)

    try:
        session.add(token)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for WorkspaceToken: %s" % e
        )

    return token


@base.session_aware()
async def list_tokens(
    object_type: str,
    object_id: int = None,
    owner_id: int = None,
    session=None,
):
    query = select(models.Token)
    if object_id:
        query = query.where(models.Token.object_id == object_id)
    if owner_id:
        query = query.where(models.Token.owner_id == owner_id)
    query = query.where(models.Token.object_type == object_type)
    query = query.order_by(models.Token.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_token_by_id(token_id: str, session=None):
    query = select(models.Token).where(models.Token.id == token_id)
    res = await session.execute(query)

    return res.scalar()


@base.session_aware()
async def delete_token(id: str, session=None):
    delete_q = delete(models.Token).where(models.Token.id == id)
    await session.execute(delete_q)
