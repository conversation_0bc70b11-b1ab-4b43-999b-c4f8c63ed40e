import datetime

from sqlalchemy import delete, update
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def create_timeline_point_change(values, session=None):
    return await base.create_model(models.TimelinePointChange, values, session=session)


@base.session_aware()
async def list_timeline_point_changes(
    owner_id: int = None,
    workspace_id: int = None,
    timeline_id: int = None,
    point_id: int = None,
    end_id: int = None,
    end_date: datetime.date = None,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    query = select(models.TimelinePointChange)
    if owner_id:
        query = query.where(models.TimelinePointChange.owner_id == owner_id)
    if workspace_id is not None:
        query = query.where(models.TimelinePointChange.workspace_id == workspace_id)
    if timeline_id is not None:
        query = query.where(models.TimelinePointChange.timeline_id == timeline_id)
    if point_id is not None:
        query = query.where(models.TimelinePointChange.point_id == point_id)
    if end_id:
        query = query.where(models.TimelinePointChange.id <= end_id)
    if end_date:
        query = query.where(models.TimelinePointChange.date <= end_date)

    order_col = getattr(models.TimelinePointChange, order)
    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_timeline_point_change_by_id(id: int, session=None):
    return await base.get_by_id(models.TimelinePointChange, id, session=session)


@base.session_aware()
async def update_timeline_point_change(change: models.TimelinePointChange, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.TimelinePointChange).where(models.TimelinePointChange.id == change.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)
    change.update(new_values)
    return change


@base.session_aware()
async def update_timeline_point_changes(timeline_id: int, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.TimelinePointChange).where(models.TimelinePointChange.timeline_id == timeline_id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)


@base.session_aware()
async def delete_timeline_point_change(id: int, session=None):
    delete_q = delete(models.TimelinePointChange).where(models.TimelinePointChange.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_timeline_point_changes(timeline_id: int = None, point_id: int = None, session=None):
    delete_q = delete(models.TimelinePointChange).where(models.TimelinePointChange.timeline_id == timeline_id)
    if point_id:
        delete_q = delete_q.where(models.TimelinePointChange.id == point_id)
    await session.execute(delete_q)
