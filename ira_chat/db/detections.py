import sqlalchemy as sa
from sqlalchemy import delete, update, text
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.db.detection_items import get_detection_item_count_one
from ira_chat.utils import utils


@base.session_aware()
async def list_detections(
    owner_id: int = None,
    workspace_id: int = None,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    type: str = None,
    q: str = None,
    session=None,
):
    count = None
    offset = None
    if limit and page:
        count = await get_detection_count(
            workspace_id=workspace_id, owner_id=owner_id, type=type, q=q, session=session
        )
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.Detection)
    if owner_id:
        query = query.where(models.Detection.owner_id == owner_id)
    if workspace_id is not None:
        query = query.where(models.Detection.workspace_id == workspace_id)
    if type is not None:
        query = query.where(models.Detection.type == type)
    if q:
        query = query.where(
            sa.or_(
                models.Detection.title.ilike(f'%{q}%'),
                models.Detection.description.ilike(f'%{q}%'),
            )
        )

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)
    order_col = getattr(models.Detection, order)
    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)

    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def get_detection_by_id(id: int, session=None):
    return await base.get_by_id(models.Detection, id, session=session)


@base.session_aware()
async def create_detection(values, session=None):
    values['metrics'] = {
        'detection_item_count': 0,
        'last_activity': base.date_to_string(models.now()),
    }
    return await base.create_model(models.Detection, values, session=session)


@base.session_aware()
async def update_detection(detection: models.Detection, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.Detection).where(models.Detection.id == detection.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    detection.update(new_values)
    return detection


@base.session_aware()
async def update_metrics_for_detection(det: models.Detection, session=None):
    detection_item_count = await get_detection_item_count_one(det.id)
    metrics = {
        'detection_item_count': detection_item_count,
        'last_activity': base.date_to_string(models.now()),
    }
    det = await update_detection(det, {'metrics': metrics})
    return det


@base.session_aware()
async def delete_detection(id: int, session=None):
    delete_q = delete(models.Detection).where(models.Detection.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def get_detection_count(
    workspace_ids: list = None,
    workspace_id: int = None,
    owner_id: int = None,
    type: str = None,
    q: str = None,
    session=None
):
    where = []
    params = {}
    if workspace_ids:
        where.append('workspace_id in :workspace_ids')
        params['workspace_ids'] = workspace_ids
    if workspace_id:
        where.append('workspace_id = :workspace_id')
        params['workspace_id'] = workspace_id
    if owner_id:
        where.append('owner_id = :owner_id')
        params['owner_id'] = owner_id
    if type:
        where.append('type = :type')
        params['type'] = type
    if q:
        where.append(
            'title ILIKE :q OR description ILIKE :q'
        )
        params['q'] = f'%{q}%'

    sql = text(
        'SELECT count(*) AS count FROM detections '
        f'WHERE {" AND ".join(where)}'
    )
    if workspace_ids:
        sql = sql.bindparams(sa.bindparam("workspace_ids", expanding=True))

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
