from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.exceptions import NotFoundException


@base.session_aware()
async def create_user_confirm(values, session=None):
    confirm = models.UserConfirm(**values)

    try:
        session.add(confirm)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for UserConfirm: %s" % e
        )

    return confirm


@base.session_aware()
async def get_user_confirm(id: str, session=None):
    query = select(models.UserConfirm).where(models.UserConfirm.id == id)
    res = await session.execute(query)
    res = res.scalar()

    if not res:
        raise NotFoundException(f'Confirm not found for id: {id}')
    return res


@base.session_aware()
async def delete_user_confirm(id, session=None):
    confirm = await get_user_confirm(id)

    if not confirm:
        raise RuntimeError('Confirm not found for id: %s' % id)

    await session.delete(confirm)
