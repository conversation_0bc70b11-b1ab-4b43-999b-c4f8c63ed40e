import sqlalchemy as sa
from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.exceptions import NotFoundException
from ira_chat.utils import utils


@base.session_aware()
async def list_dataset_files(
    dataset_id: str = None,
    dataset_ids: list = None,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    status: str = None,
    source_type: str = None,
    session=None,
):
    count = None
    offset = None
    if limit and page:
        count = await get_dataset_file_count(dataset_id=dataset_id, q=q, source_type=source_type, status=status)
        limit, offset = utils.get_limit_offset(limit, page)
    query = select(models.DatasetFile)

    if dataset_id is not None:
        query = query.where(models.DatasetFile.dataset_id == dataset_id)
    if dataset_ids:
        query = query.where(models.DatasetFile.dataset_id.in_(dataset_ids))
    if q:
        query = query.where(models.DatasetFile.name.ilike(f'%{q}%'))
    if status:
        query = query.where(sa.func.json_extract_path_text(models.DatasetFile.status, 'status') == status)
    if source_type:
        query = query.where(models.DatasetFile.source_type == source_type)

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)
    order_col = getattr(models.DatasetFile, order)
    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)

    res = await session.execute(query)

    if limit and page:
        return res.scalars().fetchall(), count
    else:
        return res.scalars().fetchall()


@base.session_aware()
async def create_dataset_file(values, session=None):
    dataset_file = models.DatasetFile(**values)

    try:
        session.add(dataset_file)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for DatasetFile: %s" % e
        )

    return dataset_file


@base.session_aware()
async def get_dataset_file(id: str, dataset_id: str = None, session=None):
    query = select(models.DatasetFile).where(models.DatasetFile.id == id)
    if dataset_id:
        query = query.where(models.DatasetFile.dataset_id == dataset_id)
    res = await session.execute(query)
    res = res.scalar()

    if not res:
        raise NotFoundException(f'DatasetFile not found for id: {id}')
    return res


@base.session_aware()
async def get_dataset_file_by_hash(hash: str, dataset_ids: list[str] = None, session=None):
    query = select(models.DatasetFile).where(models.DatasetFile.hash == hash)
    if dataset_ids:
        query = query.where(models.DatasetFile.dataset_id.in_(dataset_ids))
    res = await session.execute(query)
    res = res.scalar()

    return res


@base.session_aware()
async def get_dataset_file_size_sum(dataset_id: str, session=None) -> int:
    raw = text(
        "SELECT COALESCE(SUM(size), 0) AS size FROM dataset_files WHERE dataset_id = :dataset_id"
    )
    res = await session.execute(raw, {'dataset_id': dataset_id})
    res = res.scalar()

    return res


@base.session_aware()
async def get_dataset_file_by_ds_and_name(dataset_id: str, name: str, session=None):
    query = select(models.DatasetFile).where(models.DatasetFile.dataset_id == dataset_id).where(models.DatasetFile.name == name)
    res = await session.execute(query)
    res = res.scalar()

    return res


@base.session_aware()
async def update_dataset_file(dataset_file: models.DatasetFile, new_values: dict, session=None):
    new_values['updated_at'] = models.now()
    update_q = update(models.DatasetFile).where(models.DatasetFile.id == dataset_file.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    dataset_file.update(new_values)

    return dataset_file


@base.session_aware()
async def update_dataset_files(ids: list[str], new_values: dict, session=None):
    new_values['updated_at'] = models.now()
    update_q = update(models.DatasetFile).where(models.DatasetFile.id.in_(ids))
    update_q = update_q.values(new_values)

    await session.execute(update_q)


@base.session_aware()
async def delete_dataset_file(id: str, session=None):
    delete_q = delete(models.DatasetFile).where(models.DatasetFile.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_dataset_files(dataset_id: str, session=None):
    delete_q = delete(models.DatasetFile).where(models.DatasetFile.dataset_id == dataset_id)
    await session.execute(delete_q)


@base.session_aware()
async def get_dataset_file_count(
    dataset_id: str = None,
    dataset_ids: list = None,
    owner_id: int = None,
    q: str = None,
    status: str = None,
    statuses: list[str] = None,
    source_type: str = None,
    session=None
):
    where = []
    params = {}
    if dataset_ids:
        where.append('dataset_id in :dataset_ids')
        params['dataset_ids'] = dataset_ids
    if dataset_id:
        where.append('dataset_id = :dataset_id')
        params['dataset_id'] = dataset_id
    if owner_id:
        where.append('owner_id = :owner_id')
        params['owner_id'] = owner_id
    if q:
        q = f'%{q}%'
        where.append('name ILIKE :q')
        params['q'] = q
    if status:
        where.append("status->>'status' = :status")
        params['status'] = status
    if statuses:
        where.append("status->>'status' in :statuses")
        params['statuses'] = statuses
    if source_type:
        where.append('source_type = :source_type')
        params['source_type'] = source_type

    sql = text(
        'SELECT count(*) AS count FROM dataset_files '
        f'WHERE {" AND ".join(where)}'
    )
    if dataset_ids:
        sql = sql.bindparams(sa.bindparam("dataset_ids", expanding=True))
    if statuses:
        sql = sql.bindparams(sa.bindparam("statuses", expanding=True))

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
