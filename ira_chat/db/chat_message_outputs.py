import datetime

from sqlalchemy import delete, update, text
from sqlalchemy.future import select
from sqlalchemy.orm import load_only

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.exceptions import NotFoundException
from ira_chat.utils import utils


@base.session_aware()
async def list_all_chat_message_outputs(workspace_id: int, ids: list[int] = None, session=None):
    query = select(models.ChatMessageOutput).where(models.Chat.workspace_id == workspace_id)
    query = query.where(models.ChatMessageOutput.chat_id == models.Chat.id)
    if ids:
        query = query.where(models.ChatMessageOutput.id.in_(ids))

    query = query.order_by(models.ChatMessageOutput.created_at)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def create_message_output(values, session=None):
    return await base.create_model(models.ChatMessageOutput, values, session=session)


@base.session_aware()
async def get_message_outputs_count_ws(chat_id: int, session=None):
    raw = 'select count(*) as count from chat_message_outputs where chat_id = :chat_id'
    params = {'chat_id': chat_id}

    sql = text(raw)
    res = await session.execute(sql, params)
    chat_count = res.scalar()

    return chat_count


@base.session_aware()
async def list_message_outputs(
    chat_id: int,
    start_id: int = None,
    end_id: int = None,
    page=None,
    limit=None,
    order_by='id',
    desc=False,
    role: str = None,
    chat_message_id: int = None,
    chat_message_ids: list = None,
    ignore_statuses=None,
    columns: list = None,
    session=None
):
    order_col = getattr(models.ChatMessageOutput, order_by)
    if desc:
        order_col = order_col.desc()

    offset = None
    if limit and page:
        count = await get_message_outputs_count_ws(chat_id=chat_id)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ChatMessageOutput).where(models.ChatMessageOutput.chat_id == chat_id).order_by(order_col)
    if limit is not None:
        query = query.limit(limit)
    if offset:
        query = query.offset(offset)
    if start_id is not None:
        query = query.where(models.ChatMessageOutput.id > start_id)
    if end_id is not None:
        query = query.where(models.ChatMessageOutput.id <= end_id)
    if role:
        query = query.where(models.ChatMessageOutput.role == role)
    if chat_message_id:
        query = query.where(models.ChatMessageOutput.chat_message_id == chat_message_id)
    if chat_message_ids:
        query = query.where(models.ChatMessageOutput.chat_message_id.in_(chat_message_ids))
    if ignore_statuses:
        query = query.where(~models.ChatMessageOutput.status.in_(ignore_statuses))
    if columns:
        query = query.options(load_only(*[getattr(models.ChatMessageOutput, col) for col in columns]))
    res = await session.execute(query)

    if limit and page:
        return res.scalars().fetchall(), count
    else:
        return res.scalars().fetchall()


@base.session_aware()
async def list_last_message_outputs(
    chat_id: int = None,
    workspace_id: int = None,
    end_id: int = None,
    ignore_statuses=None,
    limit: int = 0,
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    session=None
):
    if chat_id:
        query = select(models.ChatMessageOutput).distinct(models.ChatMessageOutput.chat_message_id)
        query = query.where(models.ChatMessageOutput.chat_id == chat_id)
    else:
        query = select(models.ChatMessageOutput).distinct(models.ChatMessageOutput.chat_id, models.ChatMessageOutput.chat_message_id)

    if workspace_id:
        query = query.where(models.ChatMessageOutput.chat_id == models.Chat.id).where(models.Chat.workspace_id == workspace_id)
    if chat_id:
        query = query.order_by(models.ChatMessageOutput.chat_message_id, models.ChatMessageOutput.id.desc())
    else:
        query = query.order_by(models.ChatMessageOutput.chat_id, models.ChatMessageOutput.chat_message_id, models.ChatMessageOutput.id.desc())
    if end_id is not None:
        query = query.where(models.ChatMessageOutput.id <= end_id)
    if ignore_statuses:
        query = query.where(~models.ChatMessageOutput.status.in_(ignore_statuses))
    if start_date:
        query = query.where(models.ChatMessageOutput.created_at >= start_date)
    if end_date:
        query = query.where(models.ChatMessageOutput.created_at <= end_date)
    if limit:
        query = query.limit(limit)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_last_message_output(
    chat_id: int,
    message_id: int,
    workspace_id: int = None,
    ignore_statuses=None,
    session=None
):
    query = select(models.ChatMessageOutput).distinct(models.ChatMessageOutput.chat_message_id)
    query = query.where(models.ChatMessageOutput.chat_id == chat_id)
    query = query.where(models.ChatMessageOutput.chat_message_id == message_id)
    if workspace_id:
        query = query.where(models.ChatMessageOutput.chat_id == models.Chat.id).where(models.Chat.workspace_id == workspace_id)
    query = query.order_by(models.ChatMessageOutput.chat_message_id, models.ChatMessageOutput.id.desc())
    if ignore_statuses:
        query = query.where(~models.ChatMessageOutput.status.in_(ignore_statuses))
    res = await session.execute(query)

    return res.scalar()


@base.session_aware()
async def get_message_output_by_id(id: int, session=None):
    q = select(models.ChatMessageOutput).where(models.ChatMessageOutput.id == id)
    res = await session.execute(q)
    res = res.scalar()

    if not res:
        raise NotFoundException(f'MessageOutput not found for id: {id}')
    return res


@base.session_aware()
async def update_message_output_by_id(id, values, session=None):
    message = await get_message_output_by_id(id, session=session)

    if not message:
        raise NotFoundException(f'Message not found for id: {id}')

    message.update(values.copy())

    return message


@base.session_aware()
async def update_message_output(message, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ChatMessageOutput).where(models.ChatMessageOutput.id == message.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    message.update(new_values)
    return message


@base.session_aware()
async def delete_message_output(id: int, session=None):
    delete_q = delete(models.ChatMessageOutput).where(models.ChatMessageOutput.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_message_outputs(
    chat_id: int,
    chat_message_id: int = None,
    chat_message_ids: list = None,
    session=None
):
    # message = await get_message_by_id(id, session=session)

    msg_delete_q = delete(models.ChatMessageOutput).where(models.ChatMessageOutput.chat_id == chat_id)
    if chat_message_id:
        msg_delete_q = msg_delete_q.where(models.ChatMessageOutput.chat_message_id == chat_message_id)
    if chat_message_ids:
        msg_delete_q = msg_delete_q.where(models.ChatMessageOutput.chat_message_id.in_(chat_message_ids))
    await session.execute(msg_delete_q)
    # await session.delete(message)
