from typing import List

# noinspection PyUnresolvedReferences
from ira_chat.db import base
# noinspection PyUnresolvedReferences
from ira_chat.db.chats import *
# noinspection PyUnresolvedReferences
from ira_chat.db.chat_message_outputs import *
# noinspection PyUnresolvedReferences
from ira_chat.db.chat_messages import *
# noinspection PyUnresolvedReferences
from ira_chat.db.chat_messages_cached import *
# noinspection PyUnresolvedReferences
from ira_chat.db.datasets import *
# noinspection PyUnresolvedReferences
from ira_chat.db.dataset_files import *
# noinspection PyUnresolvedReferences
from ira_chat.db.dataset_file_documents import *
# noinspection PyUnresolvedReferences
from ira_chat.db.dataset_file_embeddings import *
# noinspection PyUnresolvedReferences
from ira_chat.db.dataset_indexes import *
# noinspection PyUnresolvedReferences
from ira_chat.db.detections import *
# noinspection PyUnresolvedReferences
from ira_chat.db.detection_items import *
# noinspection PyUnresolvedReferences
from ira_chat.db.detection_item_files import *
# noinspection PyUnresolvedReferences
from ira_chat.db.detection_item_outputs import *
# noinspection PyUnresolvedReferences
from ira_chat.db.detection_item_files import *
# noinspection PyUnresolvedReferences
from ira_chat.db.documents import *
# noinspection PyUnresolvedReferences
from ira_chat.db.extractors import *
# noinspection PyUnresolvedReferences
from ira_chat.db.extractor_files import *
# noinspection PyUnresolvedReferences
from ira_chat.db.extractor_results import *
# noinspection PyUnresolvedReferences
from ira_chat.db.extractor_items import *
# noinspection PyUnresolvedReferences
from ira_chat.db.extractor_item_outputs import *
# noinspection PyUnresolvedReferences
from ira_chat.db.file_embeddings import *
# noinspection PyUnresolvedReferences
from ira_chat.db.group_users import *
# noinspection PyUnresolvedReferences
from ira_chat.db.groups import *
# noinspection PyUnresolvedReferences
from ira_chat.db.metrics import *
# noinspection PyUnresolvedReferences
from ira_chat.db.organization_domains import *
# noinspection PyUnresolvedReferences
from ira_chat.db.organization_users import *
# noinspection PyUnresolvedReferences
from ira_chat.db.organization_webhooks import *
# noinspection PyUnresolvedReferences
from ira_chat.db.organization_webhook_items import *
# noinspection PyUnresolvedReferences
from ira_chat.db.organizations import *
# noinspection PyUnresolvedReferences
from ira_chat.db.provided_answers import *
# noinspection PyUnresolvedReferences
from ira_chat.db.sessions import *
# noinspection PyUnresolvedReferences
from ira_chat.db.suggestions import *
# noinspection PyUnresolvedReferences
from ira_chat.db.timelines import *
# noinspection PyUnresolvedReferences
from ira_chat.db.timeline_points import *
# noinspection PyUnresolvedReferences
from ira_chat.db.timeline_point_changes import *
# noinspection PyUnresolvedReferences
from ira_chat.db.tokens import *
# noinspection PyUnresolvedReferences
from ira_chat.db.user_confirms import *
# noinspection PyUnresolvedReferences
from ira_chat.db.user_invites import *
# noinspection PyUnresolvedReferences
from ira_chat.db.user_service_accounts import *
# noinspection PyUnresolvedReferences
from ira_chat.db.users import *
# noinspection PyUnresolvedReferences
from ira_chat.db.workspaces import *
from ira_chat.policies import policies


def setup_db():
    base.get_engine()


@base.session_aware()
async def lock(id: int, session=None):
    sql = text('SELECT pg_advisory_lock(:id);')
    params = {'id': id}
    res = await session.execute(sql, params)


@base.session_aware()
async def unlock(id: int, session=None):
    sql = text('SELECT pg_advisory_unlock(:id);')
    params = {'id': id}
    res = await session.execute(sql, params)


@base.session_aware()
async def get_grouped_count(type: str, workspace_ids: list, session=None):
    sql = text(
        f'select workspace_id, count(*) as count from {type} '
        'where workspace_id in :workspace_ids group by workspace_id'
    )
    params = {
        'workspace_ids': workspace_ids,
    }
    sql = sql.bindparams(sa.bindparam("workspace_ids", expanding=True))

    res = await session.execute(sql, params)

    result = []
    fields = ['workspace_id', 'count']
    for tp in res:
        result.append(dict(zip(fields, tp)))

    result_map = {r['workspace_id']: r['count'] for r in result}
    return result_map


@base.session_aware()
async def get_grouped_count_param(type: str, group_name, group_filter: list, session=None):
    raw = (
        f'select {group_name}, count(*) as count from {type} '
        f'where {group_name} in :{group_name} group by {group_name}'
    )

    sql = text(raw)
    params = {group_name: group_filter}
    sql = sql.bindparams(sa.bindparam(group_name, expanding=True))

    res = await session.execute(sql, params)
    res = res.fetchall()

    result = []
    fields = [group_name, 'count']
    for tp in res:
        result.append(dict(zip(fields, tp)))

    result_map = {r[group_name]: r['count'] for r in result}
    return result_map


@base.session_aware()
async def get_ws_chat_message_count(workspace_id: int, session=None):
    raw = 'select count(*) from chat_messages join chats on chats.id = chat_messages.chat_id and chats.workspace_id = :workspace_id;'
    params = {'workspace_id': workspace_id}

    sql = text(raw)
    res = await session.execute(sql, params)
    count = res.scalar()

    return count


@base.session_aware()
async def get_grouped_chat_count(workspace_ids: list, session=None):
    return await get_grouped_count('chats', workspace_ids, session=session)


@base.session_aware()
async def get_grouped_file_count(workspace_ids: list, session=None):
    return await get_grouped_count('files', workspace_ids, session=session)


@base.session_aware()
async def get_grouped_detection_count(workspace_ids: list, session=None):
    return await get_grouped_count('detections', workspace_ids, session=session)


def get_roles() -> List[dict]:
    user = [policies.OrgAccessName.USER]
    dev = [
        policies.OrgAccessName.ALL_WS_VIEW,
        policies.OrgAccessName.WORKSPACE_CREATE,
        policies.OrgAccessName.WORKSPACE_DELETE,
        policies.OrgAccessName.DATASET_MANAGE,
    ]
    owner = [policies.OrgAccessName.OWNER]

    all_permissions = [('user', user), ('developer', dev), ('owner', owner)]
    roles = []
    for i, (name, perm) in enumerate(all_permissions):
        roles.append({
            'id': i + 1,
            'name': name,
            'permissions': policies.encode_permissions(perm, org=True),
        })

    return roles


def get_role_by_id(role_id: int) -> dict | None:
    # 1 - user, 2 - dev, 3 - owner
    roles = get_roles()
    for role in roles:
        if role['id'] == role_id:
            # role['permissions'] = policies.encode_permissions(role['permissions'], org=True)
            return role
    return None


def get_role_by_name(role_name: str):
    roles = get_roles()
    for role in roles:
        if role['name'] == role_name:
            # role['permissions'] = policies.encode_permissions(role['permissions'], org=True)
            return role
    return None
