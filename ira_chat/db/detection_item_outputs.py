from sqlalchemy import delete, update, text
from sqlalchemy.future import select
from sqlalchemy.orm import load_only

from ira_chat.db import base
from ira_chat.db import detection_items
from ira_chat.db import models
from ira_chat.utils import utils


@base.session_aware()
async def create_detection_item_output(values, session=None):
    return await base.create_model(models.DetectionItemOutput, values, session=session)


@base.session_aware()
async def get_detection_item_output_count_ws(
    workspace_id: int, detection_id: int = None, detection_item_id: int = None, session=None
):
    raw = 'select count(*) as count from detection_items where workspace_id = :workspace_id'
    params = {'workspace_id': workspace_id}
    if detection_item_id:
        params['detection_item_id'] = detection_item_id
        raw += ' AND detection_item_id = :detection_item_id'
    if detection_id:
        params['detection_id'] = detection_id
        raw += ' AND detection_id = :detection_id'

    sql = text(raw)
    res = await session.execute(sql, params)
    chat_count = res.scalar()

    return chat_count


@base.session_aware()
async def list_detection_item_outputs(
    workspace_id: int = None,
    detection_id: int = None,
    detection_item_id: int = None,
    detection_item_ids: list = None,
    limit: int = 50,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    columns: list = None,
    session=None,
):
    count = None
    offset = None
    if limit and page:
        count = await detection_items.get_detection_items_count_ws(
            workspace_id=workspace_id, detection_id=detection_id, session=session
        )
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.DetectionItemOutput)
    if columns:
        query = query.options(load_only(*[getattr(models.DetectionItemOutput, col) for col in columns]))
    if detection_item_id:
        query = query.where(models.DetectionItemOutput.detection_item_id == detection_item_id)
    if workspace_id is not None:
        query = query.where(models.DetectionItemOutput.workspace_id == workspace_id)
    if detection_id is not None:
        query = query.where(models.DetectionItemOutput.detection_id == detection_id)
    if detection_item_ids:
        query = query.where(models.DetectionItemOutput.detection_item_id.in_(detection_item_ids))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    order_col = getattr(models.DetectionItemOutput, order)
    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    if limit and page:
        return res.scalars().fetchall(), count
    else:
        return res.scalars().fetchall()


@base.session_aware()
async def get_detection_item_output_by_id(id: int, session=None):
    return await base.get_by_id(models.DetectionItemOutput, id, session=session)


@base.session_aware()
async def update_detection_item_output(detection, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.DetectionItemOutput).where(models.DetectionItemOutput.id == detection.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    detection.update(new_values)
    return detection


@base.session_aware()
async def update_detection_item_outputs(detection_id: int, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.DetectionItemOutput).where(models.DetectionItemOutput.detection_id == detection_id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)


@base.session_aware()
async def delete_detection_item_output(id: int, session=None):
    delete_q = delete(models.DetectionItemOutput).where(models.DetectionItemOutput.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_detection_item_outputs(detection_id: int = None, detection_item_id: int = None, session=None):
    if not detection_id and not detection_item_id:
        raise ValueError('Must supply filter')

    q = delete(models.DetectionItemOutput)
    if detection_id:
        q = q.where(models.DetectionItemOutput.detection_id == detection_id)
    if detection_item_id:
        q = q.where(models.DetectionItemOutput.detection_item_id == detection_item_id)
    await session.execute(q)
