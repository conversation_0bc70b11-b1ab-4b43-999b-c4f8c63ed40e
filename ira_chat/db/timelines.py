import sqlalchemy as sa
from sqlalchemy import delete, update, text
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.utils import utils


@base.session_aware()
async def list_timelines(
    owner_id: int = None,
    workspace_id: int = None,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_workspace_timeline_count(
            workspace_id=workspace_id, owner_id=owner_id, q=q, session=session
        )
        limit, offset = utils.get_limit_offset(limit, page)
    query = select(models.Timeline)
    if owner_id:
        query = query.where(models.Timeline.owner_id == owner_id)
    if workspace_id is not None:
        query = query.where(models.Timeline.workspace_id == workspace_id)
    if q:
        query = query.where(
            sa.or_(
                models.Timeline.title.ilike(f'%{q}%'),
                models.Timeline.description.ilike(f'%{q}%'),
            )
        )

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)
    order_col = getattr(models.Timeline, order)
    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)

    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def get_timeline_by_id(id: int, session=None):
    return await base.get_by_id(models.Timeline, id, session=session)


@base.session_aware()
async def create_timeline(values, session=None):
    return await base.create_model(models.Timeline, values, session=session)


@base.session_aware()
async def update_timeline(timeline, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.Timeline).where(models.Timeline.id == timeline.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    timeline.update(new_values)
    return timeline


@base.session_aware()
async def delete_timeline(id: int, session=None):
    delete_q = delete(models.Timeline).where(models.Timeline.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def get_timeline_count(workspace_ids: list, session=None):
    sql = text(
        'select count(*) as count from timelines '
        'where workspace_id in :workspace_ids'
    )
    params = {
        'workspace_ids': workspace_ids,
    }
    sql = sql.bindparams(sa.bindparam("workspace_ids", expanding=True))

    res = await session.execute(sql, params)
    count = res.scalar()

    return count


@base.session_aware()
async def get_workspace_timeline_count(
    owner_id: int = None,
    workspace_id: int = None,
    q: str = None,
    session=None
):
    where = []
    params = {}

    if workspace_id:
        where.append('workspace_id = :workspace_id')
        params['workspace_id'] = workspace_id
    if owner_id:
        where.append('owner_id = :owner_id')
        params['owner_id'] = owner_id
    if q:
        q = f'%{q}%'
        where.append('title ILIKE :q OR description ILIKE :q')
        params['q'] = q

    sql = text(
        'SELECT count(*) AS count FROM timelines '
        f'WHERE {" AND ".join(where)}'
    )

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
