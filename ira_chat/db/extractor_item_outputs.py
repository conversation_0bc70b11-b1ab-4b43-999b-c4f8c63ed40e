from sqlalchemy import delete, update, text
from sqlalchemy.future import select
from sqlalchemy.orm import load_only

from ira_chat.db import base
from ira_chat.db import extractor_items
from ira_chat.db import models
from ira_chat.utils import utils


@base.session_aware()
async def create_extractor_item_output(values, session=None):
    return await base.create_model(models.ExtractorItemOutput, values, session=session)


@base.session_aware()
async def get_extractor_item_output_count_ws(
    workspace_id: int, extractor_id: int = None, extractor_item_id: int = None, session=None
):
    raw = 'select count(*) as count from extractor_items where workspace_id = :workspace_id'
    params = {'workspace_id': workspace_id}
    if extractor_item_id:
        params['extractor_item_id'] = extractor_item_id
        raw += ' AND extractor_item_id = :extractor_item_id'
    if extractor_id:
        params['extractor_id'] = extractor_id
        raw += ' AND extractor_id = :extractor_id'

    sql = text(raw)
    res = await session.execute(sql, params)
    chat_count = res.scalar()

    return chat_count


@base.session_aware()
async def list_extractor_item_outputs(
    workspace_id: int = None,
    extractor_id: int = None,
    extractor_item_id: int = None,
    extractor_item_ids: list = None,
    limit: int = 50,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    columns: list = None,
    session=None,
):
    count = None
    offset = None
    if limit and page:
        count = await extractor_items.get_extractor_items_count_ws(
            workspace_id=workspace_id, extractor_id=extractor_id,
        )
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ExtractorItemOutput)
    if columns:
        query = query.options(load_only(*[getattr(models.ExtractorItemOutput, col) for col in columns]))
    if extractor_item_id:
        query = query.where(models.ExtractorItemOutput.extractor_item_id == extractor_item_id)
    if workspace_id is not None:
        query = query.where(models.ExtractorItemOutput.workspace_id == workspace_id)
    if extractor_id is not None:
        query = query.where(models.ExtractorItemOutput.extractor_id == extractor_id)
    if extractor_item_ids:
        query = query.where(models.ExtractorItemOutput.extractor_item_id.in_(extractor_item_ids))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    order_col = getattr(models.ExtractorItemOutput, order)
    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    if limit and page:
        return res.scalars().fetchall(), count
    else:
        return res.scalars().fetchall()


@base.session_aware()
async def get_extractor_item_output_by_id(id: int, session=None):
    return await base.get_by_id(models.ExtractorItemOutput, id, session=session)


@base.session_aware()
async def update_extractor_item_output(extractor, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ExtractorItemOutput).where(models.ExtractorItemOutput.id == extractor.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    extractor.update(new_values)
    return extractor


@base.session_aware()
async def update_extractor_item_outputs(extractor_id: int, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ExtractorItemOutput).where(models.ExtractorItemOutput.extractor_id == extractor_id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)


@base.session_aware()
async def delete_extractor_item_output(id: int, session=None):
    delete_q = delete(models.ExtractorItemOutput).where(models.ExtractorItemOutput.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_extractor_item_outputs(extractor_id: int = None, extractor_item_id: int = None, session=None):
    if not extractor_id and not extractor_item_id:
        raise ValueError('Must supply filter')

    q = delete(models.ExtractorItemOutput)
    if extractor_id:
        q = q.where(models.ExtractorItemOutput.extractor_id == extractor_id)
    if extractor_item_id:
        q = q.where(models.ExtractorItemOutput.extractor_item_id == extractor_item_id)
    await session.execute(q)
