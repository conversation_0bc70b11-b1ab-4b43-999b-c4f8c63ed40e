from sqlalchemy import delete, update
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def list_groups_id_by_user_id(user_id: int, workspace_id: int = None, session=None):
    query = select(models.GroupUser)
    query = query.where(models.GroupUser.user_id == user_id)
    if workspace_id:
        query = query.where(models.GroupUser.workspace_id == workspace_id)
    query = query.order_by(models.GroupUser.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def list_groups_by_user_id(user_id: int, workspace_id: int, session=None):
    query = select(models.Group).join(models.GroupUser, models.Group.id == models.GroupUser.group_id)
    query = query.where(models.GroupUser.user_id == user_id).where(models.GroupUser.workspace_id == workspace_id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_group_by_id(group_id: int, session=None):
    query = select(models.Group)

    query = query.where(models.Group.id == group_id)
    query = query.order_by(models.Group.id)
    res = await session.execute(query)

    return res.scalar()


@base.session_aware()
async def list_groups(workspace_id: int = None, workspace_ids: list = None, ids: list[int] = None, session=None):
    query = select(models.Group)
    if workspace_id:
        query = query.where(models.Group.workspace_id == workspace_id)
    if workspace_ids:
        query = query.where(models.Group.workspace_id.in_(workspace_ids))
    if ids:
        query = query.where(models.Group.id.in_(ids))
    query = query.order_by(models.Group.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def update_group(group: models.Group, new_values, session=None):
    update_q = update(models.Group).where(models.Group.id == group.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    group.update(new_values)

    return group


@base.session_aware()
async def create_group(values, session=None):
    ws = models.Group(**values)

    try:
        session.add(ws)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Group: %s" % e
        )

    return ws


@base.session_aware()
async def delete_group(id: int, session=None):
    msg_delete_q = delete(models.Group).where(models.Group.id == id)
    await session.execute(msg_delete_q)


@base.session_aware()
async def delete_groups(workspace_id: int, session=None):
    delete_q = delete(models.Group).where(models.Group.workspace_id == workspace_id)
    await session.execute(delete_q)
