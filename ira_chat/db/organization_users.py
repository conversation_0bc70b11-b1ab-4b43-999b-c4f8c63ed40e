from sqlalchemy import delete, update
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def create_org_user(values, session=None):
    ws = models.OrganizationUser(**values)

    try:
        session.add(ws)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for OrganizationUser: %s" % e
        )

    return ws


@base.session_aware()
async def get_org_user(org_id: int, user_id: int = None, user_login: str = None, session=None):
    query = select(models.OrganizationUser).where(models.OrganizationUser.org_id == org_id)
    if user_id:
        query = query.where(models.OrganizationUser.user_id == user_id)
    if user_login:
        query = query.where(models.OrganizationUser.user_login == user_login)
    res = await session.execute(query)

    return res.scalar()


@base.session_aware()
async def get_org_user_by_id(id: int, session=None):
    query = select(models.OrganizationUser).where(models.OrganizationUser.id == id)
    res = await session.execute(query)

    return res.scalar()


@base.session_aware()
async def delete_org_user(id: int, session=None):
    delete_q = delete(models.OrganizationUser).where(models.OrganizationUser.id == id)
    await session.execute(delete_q)
    # await session.delete(message)


@base.session_aware()
async def list_org_users(org_id: int, session=None):
    query = select(models.OrganizationUser).where(models.OrganizationUser.org_id == org_id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def update_org_user(user: models.OrganizationUser, new_values, session=None):
    update_q = update(models.OrganizationUser).where(models.OrganizationUser.id == user.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    user.update(new_values)

    return user
