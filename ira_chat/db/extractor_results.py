from sqlalchemy import delete, update, text
from sqlalchemy.future import select
from sqlalchemy.orm import load_only

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.utils import utils


@base.session_aware()
async def create_extractor_result(values, session=None):
    return await base.create_model(models.ExtractorResult, values, session=session)


@base.session_aware()
async def get_extractor_result_count_ws(
    workspace_id: int, extractor_id: int = None, session=None
):
    raw = 'select count(*) as count from extractor_results where workspace_id = :workspace_id'
    params = {'workspace_id': workspace_id}
    if extractor_id:
        params['extractor_id'] = extractor_id
        raw += ' AND extractor_id = :extractor_id'

    sql = text(raw)
    res = await session.execute(sql, params)
    chat_count = res.scalar()

    return chat_count


@base.session_aware()
async def list_extractor_results(
    workspace_id: int = None,
    extractor_id: int = None,
    ids: list = None,
    limit: int = 50,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    columns: list = None,
    session=None,
):
    count = None
    offset = None
    if limit and page:
        count = await get_extractor_result_count_ws(
            workspace_id=workspace_id, extractor_id=extractor_id,
        )
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ExtractorResult)
    if columns:
        query = query.options(load_only(*[getattr(models.ExtractorResult, col) for col in columns]))
    if workspace_id is not None:
        query = query.where(models.ExtractorResult.workspace_id == workspace_id)
    if extractor_id is not None:
        query = query.where(models.ExtractorResult.extractor_id == extractor_id)
    if ids is not None:
        query = query.where(models.ExtractorResult.id.in_(ids))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    order_col = getattr(models.ExtractorResult, order)
    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    if limit and page:
        return res.scalars().fetchall(), count
    else:
        return res.scalars().fetchall()


@base.session_aware()
async def get_extractor_result_by_id(id: int, session=None):
    return await base.get_by_id(models.ExtractorResult, id, session=session)


@base.session_aware()
async def get_last_extractor_result(extractor_id: int, session=None):
    query = select(models.ExtractorResult).where(models.ExtractorResult.extractor_id == extractor_id)
    query = query.order_by(models.ExtractorResult.id.desc()).limit(1)

    res = await session.execute(query)
    res = res.scalar()
    return res


@base.session_aware()
async def update_extractor_result(extractor, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ExtractorResult).where(models.ExtractorResult.id == extractor.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    extractor.update(new_values)
    return extractor


@base.session_aware()
async def update_extractor_results(extractor_id: int, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ExtractorResult).where(models.ExtractorResult.extractor_id == extractor_id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)


@base.session_aware()
async def delete_extractor_result(id: int, session=None):
    delete_q = delete(models.ExtractorResult).where(models.ExtractorResult.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_extractor_results(extractor_id: int = None, ids: list[int] = None, session=None):
    if not extractor_id:
        raise ValueError('Must supply filter')

    q = delete(models.ExtractorResult)
    if extractor_id:
        q = q.where(models.ExtractorResult.extractor_id == extractor_id)
    if ids:
        q = q.where(models.ExtractorResult.id.in_(ids))
    await session.execute(q)
