import sqlalchemy as sa
from sqlalchemy import delete, update, text
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.utils import utils


@base.session_aware()
async def create_extractor_item(values, session=None):
    return await base.create_model(models.ExtractorItem, values, session=session)


@base.session_aware()
async def get_extractor_items_count_ws(workspace_id: int, owner_id: int = None, extractor_id: int = None, session=None):
    raw = 'select count(*) as count from extractor_items where workspace_id = :workspace_id'
    params = {'workspace_id': workspace_id}
    if owner_id:
        params['owner_id'] = owner_id
        raw += ' AND owner_id = :owner_id'
    if extractor_id:
        params['extractor_id'] = extractor_id
        raw += ' AND extractor_id = :extractor_id'

    sql = text(raw)
    res = await session.execute(sql, params)
    chat_count = res.scalar()

    return chat_count


@base.session_aware()
async def list_extractor_items(
    owner_id: int = None, workspace_id: int = None, extractor_id: int = None,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    session=None,
):
    count = None
    offset = None
    if limit and page:
        count = await get_extractor_items_count_ws(
            workspace_id=workspace_id, owner_id=owner_id, extractor_id=extractor_id
        )
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ExtractorItem)
    if owner_id:
        query = query.where(models.ExtractorItem.owner_id == owner_id)
    if workspace_id is not None:
        query = query.where(models.ExtractorItem.workspace_id == workspace_id)
    if extractor_id is not None:
        query = query.where(models.ExtractorItem.extractor_id == extractor_id)

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    order_col = getattr(models.ExtractorItem, order)
    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def get_extractor_item_by_id(id: int, session=None):
    return await base.get_by_id(models.ExtractorItem, id, session=session)


@base.session_aware()
async def update_extractor_item(extractor, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ExtractorItem).where(models.ExtractorItem.id == extractor.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    extractor.update(new_values)
    return extractor


@base.session_aware()
async def update_extractor_items(extractor_id: int, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ExtractorItem).where(models.ExtractorItem.extractor_id == extractor_id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)


@base.session_aware()
async def delete_extractor_item(id: int, session=None):
    delete_q = delete(models.ExtractorItem).where(models.ExtractorItem.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_extractor_items(extractor_id: int, session=None):
    delete_q = delete(models.ExtractorItem).where(models.ExtractorItem.extractor_id == extractor_id)
    await session.execute(delete_q)


@base.session_aware()
async def get_extractor_item_count(workspace_ids: list, session=None):
    sql = text(
        'select count(*) as count from extractor_items '
        'where workspace_id in :workspace_ids'
    )
    params = {
        'workspace_ids': workspace_ids,
    }
    sql = sql.bindparams(sa.bindparam("workspace_ids", expanding=True))

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
