import sqlalchemy as sa
from fastapi import HTTPException
from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.exceptions import NotFoundException
from ira_chat.utils import utils


@base.session_aware()
async def list_datasets(
    org_id: int = None,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    scope: str = None,
    session=None,
):
    count = None
    offset = None
    if limit and page:
        count = await get_dataset_count(org_id=org_id, q=q)
        limit, offset = utils.get_limit_offset(limit, page)
    query = select(models.Dataset)

    if org_id is not None:
        query = query.where(models.Dataset.org_id == org_id)
    if q:
        query = query.where(models.Dataset.name.ilike(f'%{q}%'))
    if scope:
        query = query.where(models.Dataset.scope == scope)

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if '.' in order and order.count('.') == 1:
        col, order_field = order.split('.')
        order_col = getattr(models.Dataset, col)[order_field].astext
        # if order_field in ['message_count']:
        #     order_col = order_col.cast(sa.Integer())
    else:
        if not hasattr(models.Dataset, order):
            # Set default order
            order = 'id'
        order_col = getattr(models.Dataset, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)

    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def create_dataset(values, session=None):
    dataset = models.Dataset(**values)

    try:
        session.add(dataset)
        await session.flush()
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Dataset: %s" % e
        )

    return dataset


@base.session_aware()
async def get_dataset(id: str, session=None):
    query = select(models.Dataset).where(models.Dataset.id == id)
    res = await session.execute(query)
    res = res.scalar()

    if not res:
        raise NotFoundException(f'Dataset not found for id: {id}')
    return res


@base.session_aware()
async def get_dataset_by_org_and_name(org_id: int, name: str, notfoundok=False, session=None):
    query = select(models.Dataset).where(models.Dataset.org_id == org_id).where(models.Dataset.name == name)
    res = await session.execute(query)
    res = res.scalar()

    if not res and not notfoundok:
        raise NotFoundException(f'Dataset not found for name: {name}')
    return res


@base.session_aware()
async def list_dataset_by_org_and_names(org_id: int, names: list[str], session=None):
    query = select(models.Dataset).where(models.Dataset.org_id == org_id).where(models.Dataset.name.in_(names))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def list_datasets_by_org_and_names(org_id: int, names: list[str], session=None):
    query = select(models.Dataset).where(models.Dataset.org_id == org_id).where(models.Dataset.name.in_(names))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def update_dataset(dataset: models.Dataset, new_values: dict, session=None):
    new_values['updated_at'] = models.now()
    update_q = update(models.Dataset).where(models.Dataset.id == dataset.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    dataset.update(new_values)

    return dataset


@base.session_aware()
async def delete_dataset(id: str, session=None):
    delete_q = delete(models.Dataset).where(models.Dataset.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def get_dataset_count(
    org_ids: list = None,
    org_id: int = None,
    owner_id: int = None,
    q: str = None,
    session=None
):
    where = []
    params = {}
    if org_ids:
        where.append('org_id in :org_ids')
        params['org_ids'] = org_ids
    if org_id:
        where.append('org_id = :org_id')
        params['org_id'] = org_id
    if owner_id:
        where.append('owner_id = :owner_id')
        params['owner_id'] = owner_id
    if q:
        q = f'%{q}%'
        where.append('name ILIKE :q')
        params['q'] = q

    sql = text(
        'SELECT count(*) AS count FROM datasets '
        f'WHERE {" AND ".join(where)}'
    )
    if org_ids:
        sql = sql.bindparams(sa.bindparam("org_ids", expanding=True))

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
