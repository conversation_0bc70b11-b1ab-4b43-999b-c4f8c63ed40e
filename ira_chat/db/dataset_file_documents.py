from typing import Sequence

from sqlalchemy import delete, text
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def create_dataset_file_document(values: dict, session=None):
    return await base.create_model(models.DatasetFileDocument, values, session=session)


@base.session_aware_sync()
def create_dataset_file_document_sync(values: dict, session=None):
    doc = models.DatasetFileDocument(**values)
    session.add(doc)
    return doc


@base.session_aware()
async def list_dataset_file_documents(file_id: int = None, ids: Sequence[str] = None, session=None):
    query = select(models.DatasetFileDocument)
    if file_id:
        query = query.where(models.DatasetFileDocument.dataset_file_id == file_id)
    if ids:
        query = query.where(models.DatasetFileDocument.id.in_(ids))
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def delete_dataset_file_documents(file_id: int = None, file_ids: list[int] = None, session=None):
    delete_q = delete(models.DatasetFileDocument)
    if file_id:
        delete_q = delete_q.where(models.DatasetFileDocument.dataset_file_id == file_id)
    if file_ids:
        delete_q = delete_q.where(models.DatasetFileDocument.dataset_file_id.in_(file_ids))
    await session.execute(delete_q)


@base.session_aware_sync()
def list_dataset_file_documents_by_ids_sync(ids: Sequence[str], session=None):
    query = select(models.DatasetFileDocument)
    query = query.where(models.DatasetFileDocument.id.in_(ids))
    res = session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def list_dataset_file_documents_by_ids_async_order(ids: Sequence[str], session=None):
    if not ids:
        return []
    raw = text(
        "SELECT * FROM dataset_file_documents d "
        f"JOIN UNNEST(array{str(ids)}) "
        "WITH ORDINALITY AS arr(elem, ord) ON d.id = arr.elem ORDER BY arr.ord"
    )
    query = select(models.DatasetFileDocument).from_statement(raw)
    res = await session.execute(query)
    # q = session.query(models.DatasetFileDocument).from_statement(raw)
    # res = await session.execute(q)
    return res.scalars().fetchall()


@base.session_aware_sync()
def list_dataset_file_documents_by_ids_sync_order(ids: Sequence[str], session=None):
    if not ids:
        return []
    raw = text(
        "SELECT * FROM dataset_file_documents d "
        f"JOIN UNNEST(array{str(ids)}) "
        "WITH ORDINALITY AS arr(elem, ord) ON d.id = arr.elem ORDER BY arr.ord"
    )
    q = select(models.DatasetFileDocument).from_statement(raw)

    res = session.execute(q)
    return res.scalars().fetchall()
