from sqlalchemy import delete, update
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def create_extractor_file(values, session=None):
    return await base.create_model(models.ExtractorFile, values, session=session)


@base.session_aware()
async def list_extractor_files(
    owner_id: int = None,
    workspace_id: int = None,
    extractor_id: int = None,
    extractor_ids: list = None,
    order: str = None,
    session=None
):
    query = select(models.ExtractorFile)
    if owner_id:
        query = query.where(models.ExtractorFile.owner_id == owner_id)
    if workspace_id is not None:
        query = query.where(models.ExtractorFile.workspace_id == workspace_id)
    if extractor_id is not None:
        query = query.where(models.ExtractorFile.extractor_id == extractor_id)
    if extractor_ids is not None:
        query = query.where(models.ExtractorFile.extractor_id.in_(extractor_ids))
    if order:
        query = query.order_by(models.ExtractorFile.name)
    else:
        query = query.order_by(models.ExtractorFile.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_extractor_file_by_id(id: str, session=None):
    return await base.get_by_id(models.ExtractorFile, id, session=session)


@base.session_aware()
async def get_extractor_file_by_hash(hash: str, timeline_id: int = None, extractor_id: int = None, session=None):
    q = select(models.ExtractorFile).where(models.ExtractorFile.hash == hash)
    if timeline_id:
        q = q.where(models.TimelinePoint.extractor_id == models.ExtractorFile.extractor_id)
        q = q.where(models.TimelinePoint.timeline_id == timeline_id)
    if extractor_id:
        q = q.where(models.TimelinePoint.extractor_id == extractor_id)
    # res = await session.get(models.Chat, id)
    res = await session.execute(q)
    res = res.scalar()

    return res


@base.session_aware()
async def delete_extractor_file(id: str, session=None):
    delete_q = delete(models.ExtractorFile).where(models.ExtractorFile.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def update_extractor_file(file: models.ExtractorFile, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ExtractorFile).where(models.ExtractorFile.id == file.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)
    file.update(new_values)
    return file


@base.session_aware()
async def update_extractor_files(file_ids: list[int], new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ExtractorFile).where(models.ExtractorFile.id.in_(file_ids))
    update_q = update_q.values(new_values)

    await session.execute(update_q)


@base.session_aware()
async def delete_extractor_files(extractor_id: int = None, file_id: int = None, session=None):
    delete_q = delete(models.ExtractorFile).where(models.ExtractorFile.extractor_id == extractor_id)
    if file_id:
        delete_q = delete_q.where(models.ExtractorFile.id == file_id)
    await session.execute(delete_q)
