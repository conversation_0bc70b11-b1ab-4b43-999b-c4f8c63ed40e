import collections
import datetime
import math
import logging

import sqlalchemy as sa
from sqlalchemy import delete, text, update
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


logger = logging.getLogger(__name__)


@base.session_aware()
async def messages_metrics(
    workspace_id,
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    session=None
):
    if not start_date:
        start_date = base.now() - datetime.timedelta(days=1)
    if not end_date:
        end_date = base.now()
    sql = text(
        'select count(*) as count from chat_messages '
        'join chats on chats.id = chat_messages.chat_id '
        'where workspace_id = :workspace_id '
        'AND chat_messages.updated_at < :end_date AND chat_messages.updated_at > :start_date'
    )
    params = {'workspace_id': workspace_id, 'start_date': start_date, 'end_date': end_date}
    res = await session.execute(sql, params)
    message_count = res.scalar()

    return {
        'message_count': message_count,
        'start_date': base.date_to_string(start_date),
        'end_date': base.date_to_string(end_date)
    }


@base.session_aware()
async def create_metric(values, session=None):
    if 'object_id' in values and values['object_id']:
        values['object_id'] = str(values['object_id'])
    metric = models.Metric(**values)

    try:
        session.add(metric)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Metric: %s" % e
        )

    return metric


@base.session_aware()
async def create_metrics(values: list[dict], session=None):
    if not values:
        return []
    for v in values:
        logger.info(f'Got metric {v["type"]}={v["value"]}')
        if 'object_id' in v:
            v['object_id'] = str(v['object_id'])

    metrics = [models.Metric(**v) for v in values]

    try:
        session.add_all(metrics)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Metric: %s" % e
        )

    return metrics


def create_metric_sync(values, session=None):
    metric = models.Metric(**values)

    try:
        session.add(metric)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Metric: %s" % e
        )

    return metric


@base.session_aware()
async def delete_metrics(workspace_id: int, session=None):
    delete_q = delete(models.Metric).where(models.Metric.workspace_id == workspace_id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_metrics_by(
    workspace_id: int,
    app_type: str = None,
    app_id: int = None,
    object_id: int | str = None,
    session=None
):
    delete_q = delete(models.Metric).where(models.Metric.workspace_id == workspace_id)
    if app_type:
        delete_q = delete_q.where(models.Metric.app_type == app_type)
    if app_id:
        delete_q = delete_q.where(models.Metric.app_id == app_id)
    if object_id:
        delete_q = delete_q.where(models.Metric.object_id == str(object_id))
    await session.execute(delete_q)


@base.session_aware()
async def delete_metric(metric_id: int, session=None):
    delete_q = delete(models.Metric).where(models.Metric.id == metric_id)
    await session.execute(delete_q)


@base.session_aware()
async def get_last_metric(
    workspace_id: int,
    app_type: str = None,
    app_id: int = None,
    object_id: int = None,
    session=None
):
    q = select(models.Metric).where(models.Metric.workspace_id == workspace_id)
    if app_type:
        q = q.where(models.Metric.app_type == app_type)
    if app_id:
        q = q.where(models.Metric.app_id == app_id)
    if object_id:
        q = q.where(models.Metric.object_id == object_id)

    q.order_by(models.Metric.id.desc()).limit(1)
    res = await session.execute(q)
    res = res.scalar()

    return res


@base.session_aware()
async def get_last_metrics(
    workspace_id: int,
    app_type=None,
    app_id: int = None,
    object_id: str = None,
    split_by: tuple = ('type',),
    filter_by: str = None,
    filter_values: list = None,
    session=None
):
    where = ['workspace_id = :workspace_id']
    params = {'workspace_id': workspace_id}

    if app_type:
        where.append('app_type = :app_type')
        params['app_type'] = app_type
    if app_id:
        where.append('app_id = :app_id')
        params['app_id'] = app_id
    if object_id:
        where.append('object_id = :object_id')
        params['object_id'] = str(object_id)

    if filter_by and filter_values:
        if len(filter_values) > 1:
            str_filter = tuple(filter_values)
        else:
            if isinstance(filter_values[0], str):
                str_filter = f"('{filter_values[0]}')"
            else:
                str_filter = f"({filter_values[0]})"
        where.append(f'{filter_by} in {str_filter}')
        # params['filter_values'] = filter_values

    fields = ['id', 'app_id', 'object_id', 'type', 'value', 'updated_at']
    # r = (
    #     f"SELECT DISTINCT ON (type, object_id) id, app_id, object_id, type, value, updated_at "
    #     f"FROM metrics WHERE app_id=10 ORDER BY type, object_id, id DESC"
    # )
    # raw = (
    #     f"SELECT DISTINCT ON ({', '.join(split_by)}) {', '.join(fields)} "
    #     f"FROM metrics WHERE {' AND '.join(where)} ORDER BY {', '.join(split_by)}, id DESC"
    # )
    subquery_split_by = ' AND '.join([f'm.{s} = subquery.{s}' for s in split_by])
    raw = f"""SELECT m.id as id, m.app_id as app_id, m.object_id as object_id, m.type as type, m.value as value, m.updated_at as updated_at 
FROM metrics m 
JOIN (
    SELECT {', '.join(split_by)}, MAX(id) AS max_id
    FROM metrics
    WHERE {' AND '.join(where)}
    GROUP BY {', '.join(split_by)}
) subquery ON {subquery_split_by} AND m.id = subquery.max_id
    """

    sql = sa.text(raw)
    # if filter_by and filter_values:
    #     sql = sql.bindparams(sa.bindparam("filter_values", expanding=True))

    res = await session.execute(sql, params)
    res = res.fetchall()
    result = []
    for tp in res:
        result.append(dict(zip(fields, tp)))
        result[-1]['created_at'] = base.date_to_string(result[-1]['updated_at'])

    return result
    # group by metric type
    # result_map = {r['type']: r['value'] for r in result}
    # return result_map


@base.session_aware()
async def get_metrics_graph(
    workspace_id: int,
    app_type: str = None,
    app_id: int = None,
    object_id: int = None,
    type: list = None,
    object_ids: list = None,
    type_groups: dict = None,
    object_base_type: type = str,
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    session=None
):
    where = ['workspace_id = :workspace_id']
    params: dict = {'workspace_id': workspace_id}
    postgres_type = 'TEXT' if object_base_type == str else 'INTEGER'

    if app_type:
        where.append('app_type = :app_type')
        params['app_type'] = app_type
    if app_id:
        where.append('app_id = :app_id')
        params['app_id'] = app_id
    if object_id:
        where.append('object_id = :object_id')
        params['object_id'] = object_id
    if type:
        where.append('type IN :type')
        params['type'] = type
    if object_ids is not None:
        object_ids = object_ids if len(object_ids) > 0 else [None]
        where.append('object_id IN :object_ids')
        params['object_ids'] = object_ids
    if start_date:
        where.append('updated_at >= :start_date')
        params['start_date'] = start_date
    if end_date:
        where.append('updated_at <= :end_date')
        params['end_date'] = end_date

    if type_groups:
        fields = [
            f'object_id::{postgres_type} as object_id', 'type',
            'min(updated_at) as updated_at', 'min(extra::TEXT)::JSON as extra'
        ]
        agg_type_map = collections.defaultdict(list)
        for metric, agg in type_groups.items():
            agg_type_map[agg].append(metric)

        case_field = (
            'CASE ' +
            '\n '.join([f"WHEN type in ('{to_comma_separated(v)}') THEN {agg}(value) " for agg, v in
                        agg_type_map.items()]) +
            ' END as value'
        )
        fields.append(case_field)
        raw = (
            f"SELECT {', '.join(fields)} "
            f"FROM metrics WHERE {' AND '.join(where)} GROUP BY object_id::{postgres_type}, type ORDER BY object_id::{postgres_type}"
        )
        # needed to unpack results
        fields = ['object_id', 'type', 'updated_at', 'extra', 'value']
    else:
        fields = [f'object_id::{postgres_type} as object_id', 'type', 'updated_at', 'extra', 'value']
        raw = (
            f"SELECT {', '.join(fields)} "
            f"FROM metrics WHERE {' AND '.join(where)} ORDER BY id"
        )
        # needed to unpack results
        fields = ['object_id', 'type', 'updated_at', 'extra', 'value']

    sql = sa.text(raw)
    if type:
        sql = sql.bindparams(sa.bindparam("type", expanding=True))
    if object_ids:
        sql = sql.bindparams(sa.bindparam("object_ids", expanding=True))

    res = await session.execute(sql, params)
    res = res.fetchall()
    result_raw = []
    for tp in res:
        result_raw.append(dict(zip(fields, tp)))
        result_raw[-1]['timestamp'] = result_raw[-1]['updated_at'].timestamp()
        result_raw[-1]['updated_at'] = base.date_to_string(result_raw[-1]['updated_at'])

    result = collections.defaultdict(list)
    for row in result_raw:
        result[row['type']].append(row)

    return result


@base.session_aware()
async def get_metrics_graph_sa(
    workspace_id: int,
    app_type: str = None,
    app_id: int = None,
    object_id: int = None,
    type: list = None,
    session=None
):
    query = select(models.Metric).where(models.Metric.workspace_id == workspace_id)
    if app_type:
        query = query.where(models.Metric.app_type == app_type)
    if app_id:
        query = query.where(models.Metric.app_id == app_id)
    if app_type:
        query = query.where(models.Metric.object_id == object_id)
    if type:
        query = query.where(models.Metric.type.in_(type))

    query = query.order_by(models.Metric.id)

    res = await session.execute(query)
    res = res.scalars().fetchall()
    result = collections.defaultdict(list)
    for row in res:
        result[row.type].append(row)

    return result


@base.session_aware()
async def get_metrics(
    workspace_ids: list,
    type_groups: dict,
    metric_types: list = None,
    app_type=None,
    app_id: int = None,
    object_id: int | str = None,
    start_date=None,
    end_date=None,
    tag: str = None,
    agg_groups: list = None,
    session=None
):
    # if not start_date:
    #     start_date = base.now() - datetime.timedelta(days=1)
    # if not end_date:
    #     end_date = base.now()

    select_groups = ''
    groupby_groups = ''

    # Group by type always
    agg_groups = [] if agg_groups is None else agg_groups
    agg_groups.append('type')

    if agg_groups:
        select_groups = ', ' + ', '.join(agg_groups)
        groupby_groups = f' GROUP BY {", ".join(agg_groups)} '

    if 'extra' in agg_groups:
        select_groups = select_groups.replace('extra', 'extra::TEXT::JSON')
        groupby_groups = groupby_groups.replace('extra', 'extra::TEXT')

    agg_type_map = collections.defaultdict(list)
    for metric, agg in type_groups.items():
        agg_type_map[agg].append(metric)

    case_field = (
        'CASE ' +
        '\n '.join([
            f"WHEN type in ('{to_comma_separated(v)}') THEN {agg}(value) " for agg, v in agg_type_map.items()
        ]) + ' END as value'
    )

    start_where = f"AND metrics.updated_at > '{str(start_date)}'" if start_date else ''
    end_where = f"AND metrics.updated_at < '{str(end_date)}'" if end_date else ''
    raw = (
        f"SELECT {case_field}{select_groups} FROM metrics "
        "WHERE workspace_id IN :workspace_ids "
        f"{start_where} {end_where}"
    )
    params: dict = {'workspace_ids': workspace_ids}
    if app_type:
        raw += ' AND app_type = :app_type'
        params['app_type'] = app_type
    if app_id:
        raw += ' AND app_id = :app_id'
        params['app_id'] = app_id
    if object_id:
        raw += ' AND object_id = :object_id'
        params['object_id'] = str(object_id)
    if metric_types:
        raw += ' AND type IN :metric_types'
        params['metric_types'] = metric_types
    if tag:
        if tag == 'null':
            raw += ' AND tag IS NULL'
        else:
            raw += ' AND tag = :tag'
            params['tag'] = tag

    raw += f"{groupby_groups}"

    sql = sa.text(raw)
    sql = sql.bindparams(sa.bindparam("workspace_ids", expanding=True))
    if metric_types:
        sql = sql.bindparams(sa.bindparam("metric_types", expanding=True))
    res = await session.execute(sql, params)
    res = res.fetchall()

    result = []
    fields = ['value'] + agg_groups
    for tp in res:
        result.append(dict(zip(fields, tp)))

    return result


@base.session_aware()
async def get_metric(
    workspace_ids: list,
    metric_type,
    app_type=None,
    app_id: int = None,
    object_id: int = None,
    start_date=None,
    end_date=None,
    agg_func='count',
    agg_groups: list = None,
    session=None
):
    # if not start_date:
    #     start_date = base.now() - datetime.timedelta(days=1)
    # if not end_date:
    #     end_date = base.now()

    select_groups = ''
    groupby_groups = ''
    if agg_groups:
        select_groups = ', ' + ', '.join(agg_groups)
        groupby_groups = f' GROUP BY {", ".join(agg_groups)} '

    start_date_str = f"AND metrics.updated_at > '{str(start_date)}'" if start_date else ''
    end_date_str = f"AND metrics.updated_at < '{str(end_date)}'" if end_date else ''
    raw = (
        f"SELECT {agg_func}(value) AS count{select_groups} FROM metrics "
        "WHERE workspace_id IN :workspace_ids AND type = :type "
        f"{start_date_str} {end_date_str}"
        f"{groupby_groups}"
    )
    params = {
        'workspace_ids': workspace_ids,
        'type': metric_type,
    }
    if app_type:
        raw += ' AND app_type = :app_type'
        params['app_type'] = app_type
    if app_id:
        raw += ' AND app_id = :app_id'
        params['app_id'] = app_id
    if object_id:
        raw += ' AND object_id = :object_id'
        params['object_id'] = object_id

    sql = sa.text(raw)
    sql = sql.bindparams(sa.bindparam("workspace_ids", expanding=True))
    res = await session.execute(sql, params)
    if not agg_groups:
        count = res.scalar()
        if not count or math.isnan(count):
            count = 0
        else:
            count = float(count)
        return count
    else:
        res = res.fetchall()
        result = []
        fields = ['count'] + agg_groups
        for tp in res:
            result.append(dict(zip(fields, tp)))

        return result


@base.session_aware()
async def get_metric_one(
    workspace_id: int,
    metric_type: str,
    app_type: str = None,
    app_id: int = None,
    object_id: str | int = None,
    session=None
):
    q = select(models.Metric).where(models.Metric.workspace_id == workspace_id)
    if app_type:
        q = q.where(models.Metric.app_type == app_type)
    if app_id:
        q = q.where(models.Metric.app_id == app_id)
    if object_id:
        q = q.where(models.Metric.object_id == str(object_id))
    q = q.where(models.Metric.type == metric_type)

    res = await session.execute(q)
    res = res.scalar()

    return res


@base.session_aware()
async def update_metric(metric, new_values, session=None):
    if not new_values.get('updated_at'):
        new_values['updated_at'] = base.now()
    if 'object_id' in new_values and new_values['object_id']:
        new_values['object_id'] = str(new_values['object_id'])

    update_q = update(models.Metric).where(models.Metric.id == metric.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    metric.update(new_values)
    return metric


@base.session_aware()
async def update_metrics(new_values, workspace_id: int, app_id: int = None, session=None, **filters):
    if not filters:
        raise ValueError('Must supply filter')
    if 'object_id' in new_values and new_values['object_id']:
        new_values['object_id'] = str(new_values['object_id'])

    update_q = update(models.Metric).where(models.Metric.workspace_id == workspace_id)
    if app_id:
        update_q = update_q.where(models.Metric.app_id == app_id)
    if filters:
        for k, v in filters.items():
            if k == 'object_id':
                v = str(v)
            update_q = update_q.where(getattr(models.Metric, k) == v)
    update_q = update_q.values(new_values)

    await session.execute(update_q)


@base.session_aware()
async def get_metrics_by_days(
    workspace_ids: list, metric_type, app_type=None,
    app_id: int = None, object_id: int = None,
    start_date=None, end_date=None, days=90, agg_func='count', session=None
):
    if not start_date:
        start_date = base.now() - datetime.timedelta(days=1)

    if not end_date:
        end_date = base.now()
    end_date_day = end_date.date()

    optional_filters = ''
    params = {'days': days, 'workspace_ids': workspace_ids, 'type': metric_type}
    if app_type:
        optional_filters = ' AND app_type = :app_type'
        params['app_type'] = app_type
    if app_id:
        optional_filters += ' AND app_id = :app_id'
        params['app_id'] = app_id
    if object_id:
        optional_filters += ' AND object_id = :object_id'
        params['object_id'] = object_id

    raw = f"""
    SELECT d.date, {agg_func}(m.value)
    FROM 
    (
    SELECT to_char(date_trunc('day', (DATE('{str(end_date_day)}') - offs)), 'YYYY-MM-DD') AS date 
       FROM generate_series(0, :days, 1) AS offs
    ) d
    LEFT OUTER JOIN metrics m 
      ON d.date = to_char(date_trunc('day', m.updated_at), 'YYYY-MM-DD') 
      AND workspace_id IN :workspace_ids AND type = :type{optional_filters}
    GROUP BY d.date order by date;
    """

    sql = text(raw)
    sql = sql.bindparams(sa.bindparam("workspace_ids", expanding=True))
    res = await session.execute(sql, params)
    by_days = []
    for date, count in res:
        by_days.append({'date': date, 'count': count or 0})

    # Fix first and last day
    if start_date.date() != end_date.date():
        first_eod = start_date.replace(hour=23, minute=59, second=59)
        last_bod = end_date.replace(hour=0, minute=0, second=1)
        first_day = await get_metric(
            workspace_ids, metric_type, app_type=app_type,
            app_id=app_id, object_id=object_id,
            start_date=start_date, end_date=first_eod, agg_func=agg_func
        )
        last_day = await get_metric(
            workspace_ids, metric_type, app_type=app_type,
            app_id=app_id, object_id=object_id,
            start_date=last_bod, end_date=end_date, agg_func=agg_func
        )
        by_days[0]['count'] = first_day or 0
        by_days[-1]['count'] = last_day or 0
    else:
        usage = await get_metric(
            workspace_ids, metric_type, app_type=app_type,
            app_id=app_id, object_id=object_id,
            start_date=start_date, end_date=end_date, agg_func=agg_func
        )
        by_days[0]['count'] = usage or 0

    return by_days


@base.session_aware()
async def get_all_metrics_by_days(
    workspace_ids: list,
    type_groups: dict,
    app_type=None,
    app_id: int = None,
    object_id: int = None,
    start_date=None,
    end_date=None,
    tag: str = None,
    agg_groups: list = None,
    days=90,
    insert_missing_values=0,
    session=None
):
    if not start_date:
        start_date = base.now() - datetime.timedelta(days=1)

    if not end_date:
        end_date = base.now()
    start_date_day = start_date.date()
    end_date_day = end_date.date()
    end_date_day1 = end_date.date() + datetime.timedelta(days=1)

    optional_filters = ''
    params = {'days': days, 'workspace_ids': workspace_ids}
    if app_type:
        optional_filters = ' AND app_type = :app_type'
        params['app_type'] = app_type
    if app_id:
        optional_filters += ' AND app_id = :app_id'
        params['app_id'] = app_id
    if object_id:
        optional_filters += ' AND object_id = :object_id'
        params['object_id'] = object_id
    if tag:
        if tag == 'null':
            optional_filters += ' AND tag IS NULL'
        else:
            optional_filters += ' AND tag = :tag'
            params['tag'] = tag

    agg_type_map = collections.defaultdict(list)
    for metric, agg in type_groups.items():
        agg_type_map[agg].append(metric)

    additional_where = ''
    if type_groups:
        additional_where = 'WHERE type IN :type'
        params['type'] = list(type_groups.keys())

    case_field = (
        'CASE ' +
        '\n '.join([
            f"WHEN type in ('{to_comma_separated(v)}') THEN {agg}(value) " for agg, v in agg_type_map.items()
        ]) + ' END as value'
    )

    raw = f"""
    WITH dates AS (
    SELECT date_trunc('day', (DATE('{str(end_date_day)}') - offs)) AS date
    FROM generate_series(0, :days, 1) AS offs
    )
    SELECT to_char(d.date, 'YYYY-MM-DD') as date, {case_field}, type
    FROM dates d
    LEFT OUTER JOIN metrics m 
      ON d.date = date_trunc('day', m.updated_at)
      AND workspace_id IN :workspace_ids {optional_filters}
      AND m.updated_at >= '{start_date_day}' AND m.updated_at <= '{end_date_day1}'
      {additional_where}
    GROUP BY d.date, m.type order by date;
    """

    sql = text(raw)
    sql = sql.bindparams(sa.bindparam("workspace_ids", expanding=True))
    if type_groups:
        sql = sql.bindparams(sa.bindparam("type", expanding=True))
    res = await session.execute(sql, params)
    res = res.fetchall()
    # by_days = []
    by_days_by_type = collections.defaultdict(list)

    for date, count, type_ in res:
        if not type_:
            continue
        by_days_by_type[type_].append({'date': date, 'count': count or insert_missing_values})
        # by_days.append({'date': date, 'count': count or 0, 'type': type_})

    # === Insert zeros for each date ===
    per_metric = (end_date_day - start_date_day).days + 1
    for type_, by_days in by_days_by_type.items():
        if len(by_days) == per_metric:
            continue
        current_date = start_date_day
        i = 0

        while i < len(by_days):
            by_day = by_days[i]
            if by_day['date'] != str(current_date):
                by_days.insert(i, {'date': str(current_date), 'count': insert_missing_values})
            i += 1
            current_date += datetime.timedelta(days=1)
        if current_date < end_date_day + datetime.timedelta(days=1):
            days = (end_date_day + datetime.timedelta(days=1) - current_date).days
            for d in range(days):
                by_days.append({'date': str(current_date + datetime.timedelta(days=d)), 'count': insert_missing_values})
    # === Done insert zeros for each date ===

    # Fix first and last day
    if start_date.date() != end_date.date():
        first_eod = start_date.replace(hour=23, minute=59, second=59)
        last_bod = end_date.replace(hour=0, minute=0, second=1)
        first_day = await get_metrics(
            workspace_ids, type_groups, app_type=app_type,
            app_id=app_id, object_id=object_id,
            start_date=start_date, end_date=first_eod,
        )
        last_day = await get_metrics(
            workspace_ids, type_groups, app_type=app_type,
            app_id=app_id, object_id=object_id,
            start_date=last_bod, end_date=end_date,
        )
        first_day_by_type = {m['type']: m['value'] for m in first_day}
        last_day_by_type = {m['type']: m['value'] for m in last_day}
        for type_, by_days in by_days_by_type.items():
            by_days[0]['count'] = first_day_by_type.get(type_, insert_missing_values) or insert_missing_values
            by_days[-1]['count'] = last_day_by_type.get(type_, insert_missing_values) or insert_missing_values
    else:
        usage = await get_metrics(
            workspace_ids, type_groups, app_type=app_type,
            app_id=app_id, object_id=object_id,
            start_date=start_date, end_date=end_date,
        )
        usage_by_type = {m['type']: m['value'] for m in usage}
        for type_, by_days in by_days_by_type.items():
            by_days[0]['count'] = usage_by_type.get(type_, insert_missing_values) or insert_missing_values

    return by_days_by_type


def to_comma_separated(v):
    return "', '".join(v)
