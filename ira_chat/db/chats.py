import datetime

import sqlalchemy as sa
from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy import func, exists
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.db.chat_messages import get_messages_count_ws
from ira_chat.utils import utils


@base.session_aware()
async def create_chat(values, session=None):
    values['metrics'] = {
        'message_count': 0,
        'last_activity': base.date_to_string(models.now()),
    }
    chat = models.Chat(**values)

    try:
        session.add(chat)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Chat: %s" % e
        )

    return chat


@base.session_aware()
async def update_chat(chat: models.Chat, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.Chat).where(models.Chat.id == chat.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    chat.update(new_values)

    return chat


@base.session_aware()
async def delete_chat(id: int, session=None):
    chat = await get_chat_by_id(id, session=session)

    msg_delete_q = delete(models.ChatMessage).where(models.ChatMessage.chat_id == id)
    await session.execute(msg_delete_q)
    await session.delete(chat)


metric_sort_sql_tpl = """
WITH aggregated_metrics AS (
    SELECT
        m.app_id,
        {metric_agg_func}(m.value) AS value
    FROM
        metrics m
    WHERE
        m.workspace_id = :workspace_id
        AND m.app_type = 'chat'
        AND m.type = 'message'
        {metric_start_date_filter}
        {metric_end_date_filter}
    GROUP BY
        m.app_id
),
ranked_chats AS (
    SELECT
        c.*
    FROM
        chats c
    LEFT JOIN
        aggregated_metrics metrics
    ON
        c.id = metrics.app_id
    WHERE
        c.workspace_id = :workspace_id
        {owner_filter}
    ORDER BY
        COALESCE(metrics.value, 0) {desc}
)
SELECT
    *
FROM
    ranked_chats
{limit_offset};"""
# LIMIT :limit OFFSET :offset;"""


@base.session_aware()
async def list_chats(
    owner_id: int = None,
    workspace_id: int = None,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    q_items: str = None,
    q_tags: str = None,
    q_grade_selector: str = None,
    metric_agg_func: str = 'sum',
    metric_start_date: datetime.datetime = None,
    metric_end_date: datetime.datetime = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_workspace_chat_count(
            owner_id=owner_id, workspace_id=workspace_id, q=q,
            q_tags=q_tags, q_items=q_items, q_grade_selector=q_grade_selector, session=session
        )
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.Chat)
    if owner_id:
        query = query.where(models.Chat.owner_id == owner_id)
    if workspace_id is not None:
        query = query.where(models.Chat.workspace_id == workspace_id)
    if q:
        query = query.where(models.Chat.title.ilike(f'%{q}%'))
    if q_items:
        # Search in chat messages and chat message outputs using EXISTS
        subquery = select(1).select_from(models.ChatMessage).where(
            models.ChatMessage.chat_id == models.Chat.id
        ).join(
            models.ChatMessageOutput,
            models.ChatMessageOutput.chat_message_id == models.ChatMessage.id
        ).where(
            models.ChatMessage.content.ilike(f'%{q_items}%') |
            models.ChatMessageOutput.content.ilike(f'%{q_items}%')
        )
        query = query.where(exists(subquery))

    if q_tags:
        if q_tags == 'null':
            # Find chats with messages that have NULL tags
            subquery = select(1).select_from(models.ChatMessage).where(
                models.ChatMessage.chat_id == models.Chat.id,
                models.ChatMessage.tags.is_(None)
            )
            query = query.where(exists(subquery))
        else:
            # Using jsonb containment operator via text
            q_tags_json = f'["{q_tags}"]'
            subquery = select(1).select_from(models.ChatMessage).where(
                models.ChatMessage.chat_id == models.Chat.id
            ).where(
                text("chat_messages.tags::jsonb @> :q_tags")
            ).params(q_tags=q_tags_json)
            query = query.where(exists(subquery))

    if q_grade_selector:
        # Find chats with messages that have the specified grade_selector
        subquery = select(1).select_from(models.ChatMessage).where(
            models.ChatMessage.chat_id == models.Chat.id
        ).join(
            models.ChatMessageOutput,
            models.ChatMessageOutput.chat_message_id == models.ChatMessage.id
        ).where(
            models.ChatMessageOutput.grade_selector == q_grade_selector
        )
        query = query.where(exists(subquery))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    order_field = None
    if '.' in order and order.count('.') == 1:
        col, order_field = order.split('.')
        order_col = getattr(models.Chat, col)[order_field].astext
        if order_field in ['message_count']:
            order_col = order_col.cast(sa.Integer())
    else:
        if not hasattr(models.Chat, order):
            # Set default order
            order = 'id'
        order_col = getattr(models.Chat, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    if order_field in [
        models.METRIC_MESSAGE, models.METRIC_MESSAGE_RESPONSE_TIME, models.METRIC_CONTEXT_UTIL,
        models.METRIC_RELEVANCE, models.METRIC_FAITHFULNESS, models.METRIC_WEIGHTED_EVAL,
    ]:
        # Special case for metric ordering
        owner_filter = '' if owner_id is None else 'AND c.owner_id = :owner_id'
        limit_offset = f'LIMIT {limit} OFFSET {offset}' if limit and page else ''
        raw = text(metric_sort_sql_tpl.format(
            owner_filter=owner_filter, limit_offset=limit_offset,
            metric_agg_func=metric_agg_func, desc='DESC' if desc else 'ASC',
            metric_start_date_filter=(
                f"AND m.created_at >= '{str(metric_start_date)}'"
                if metric_start_date else ''
            ),
            metric_end_date_filter=(
                f"AND m.created_at <= '{str(metric_end_date)}'"
                if metric_end_date else ''
            )
        ))
        query = select(models.Chat).from_statement(raw)
        res = await session.execute(query, {'workspace_id': workspace_id})
    else:
        res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def list_chats_all(
    ids: list[int] = None,
    workspace_id: int = None,
    session=None
):
    query = select(models.Chat)
    if ids:
        query = query.where(models.Chat.id.in_(ids))
    if workspace_id:
        query = query.where(models.Chat.workspace_id == workspace_id)

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def list_chats_with_metrics(
    owner_id: int = None,
    workspace_id: int = None,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    metrics: str = 'message,message_count,last_activity',
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_workspace_chat_count(owner_id=owner_id, workspace_id=workspace_id, q=q)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.Chat)
    if owner_id:
        query = query.where(models.Chat.owner_id == owner_id)
    if workspace_id is not None:
        query = query.where(models.Chat.workspace_id == workspace_id)
    if q:
        query = query.where(models.Chat.title.ilike(f'%{q}%'))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if '.' in order and order.count('.') == 1:
        col, order_field = order.split('.')
        order_col = getattr(models.Chat, col)[order_field].astext
        if order_field in ['message_count']:
            order_col = order_col.cast(sa.Integer())
    else:
        if not hasattr(models.Chat, order):
            # Set default order
            order = 'id'
        order_col = getattr(models.Chat, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)

    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def get_chat_by_id(id: int, session=None):
    return await base.get_by_id(models.Chat, id, session=session)


@base.session_aware()
async def get_workspace_chat_count(
    owner_id: int = None,
    workspace_id: int = None,
    q: str = None,
    q_tags: str = None,
    q_items: str = None,
    q_grade_selector: str = None,
    session=None
):
    where = []
    params = {}

    if workspace_id:
        where.append('workspace_id = :workspace_id')
        params['workspace_id'] = workspace_id
    if owner_id:
        where.append('owner_id = :owner_id')
        params['owner_id'] = owner_id
    if q:
        q = f'%{q}%'
        where.append('title ILIKE :q')
        params['q'] = q

    joins = ''
    if q_tags:
        # AND EXISTS (SELECT 1 FROM chat_messages m WHERE m.chat_id = chats.id AND m.tags::jsonb @> '["WAP"]')
        if q_tags == 'null':
            where.append('EXISTS (SELECT 1 FROM chat_messages m WHERE m.chat_id = chats.id AND m.tags is NULL)')
        else:
            where.append('EXISTS (SELECT 1 FROM chat_messages m WHERE m.chat_id = chats.id AND m.tags::jsonb @> :q_tags)')
            q_tags = f'["{q_tags}"]'
            params['q_tags'] = q_tags

    if q_items:
        where.append(
            'EXISTS (\n'
            'SELECT 1 FROM chat_messages m \n'
            'JOIN chat_message_outputs o ON o.chat_message_id = m.id '
            'WHERE m.chat_id = chats.id AND (m.content ILIKE :q_items OR o.content ILIKE :q_items))'
        )
        params['q_items'] = f'%{q_items}%'

    if q_grade_selector:
        # AND EXISTS (
        #     SELECT 1
        #     FROM chat_messages AS m
        #     JOIN chat_message_outputs AS o
        #       ON o.chat_message_id = m.id
        #     WHERE
        #       m.chat_id = c.id
        #       AND o.grade_selector = 'Incorrect answer'
        #   )
        where.append(
            'EXISTS (\n'
            'SELECT 1 FROM chat_messages m \n'
            'JOIN chat_message_outputs o ON o.chat_message_id = m.id '
            'WHERE m.chat_id = chats.id AND o.grade_selector = :q_grade_selector)'
        )
        params['q_grade_selector'] = q_grade_selector

    sql = text(
        f'SELECT count(*) AS count FROM chats {joins} '
        f'WHERE {" AND ".join(where)}'
    )

    res = await session.execute(sql, params)
    count = res.scalar()

    return count


@base.session_aware()
async def get_chat_count(workspace_ids: list, session=None):
    sql = text(
        'select count(*) as count from chats '
        'where workspace_id in :workspace_ids'
    )
    params = {
        'workspace_ids': workspace_ids,
    }
    sql = sql.bindparams(sa.bindparam("workspace_ids", expanding=True))

    res = await session.execute(sql, params)
    chat_count = res.scalar()

    return chat_count


@base.session_aware()
async def update_metrics_for_chat(chat: models.Chat, session=None):
    chat_message_count = await get_messages_count_ws(chat.id)
    metrics = {
        'message_count': chat_message_count,
        'last_activity': base.date_to_string(models.now()),
    }
    chat = await update_chat(chat, {'metrics': metrics})
    return chat
