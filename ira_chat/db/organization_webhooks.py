from sqlalchemy import delete, update
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def list_org_webhooks(org_id: int, session=None):
    query = select(models.OrganizationWebhook).where(models.OrganizationWebhook.org_id == org_id)
    query = query.order_by(models.OrganizationWebhook.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def update_org_webhook(webhook: models.OrganizationWebhook, new_values: dict, session=None):
    new_values['updated_at'] = base.now()

    update_q = update(models.OrganizationWebhook).where(models.OrganizationWebhook.id == webhook.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    webhook.update(new_values)

    return webhook


@base.session_aware()
async def delete_org_webhooks(org_id: int, session=None):
    msg_delete_q = delete(models.OrganizationWebhook).where(models.OrganizationWebhook.org_id == org_id)
    await session.execute(msg_delete_q)


@base.session_aware()
async def delete_org_webhook(id: int, session=None):
    msg_delete_q = delete(models.OrganizationWebhook).where(models.OrganizationWebhook.id == id)
    await session.execute(msg_delete_q)


@base.session_aware()
async def create_org_webhook(values, session=None):
    res = models.OrganizationWebhook(**values)

    try:
        session.add(res)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for OrganizationWebhook: %s" % e
        )

    return res


@base.session_aware()
async def get_org_webhook_by_id(id: int, session=None):
    return await base.get_by_id(models.OrganizationWebhook, id, session=session)


@base.session_aware()
async def list_orgs_webhooks(org_ids: list[int], session=None):
    query = select(models.OrganizationWebhook).where(models.OrganizationWebhook.org_id.in_(org_ids))
    query = query.order_by(models.OrganizationWebhook.id)
    res = await session.execute(query)

    return res.scalars().fetchall()
