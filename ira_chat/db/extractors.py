import sqlalchemy as sa
from sqlalchemy import delete, update, text
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.utils import utils


@base.session_aware()
async def list_extractors(
    owner_id: int = None,
    workspace_id: int = None,
    ids: list[int] = None,
    show_hidden=True,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_workspace_extractor_count(
            workspace_id=workspace_id, owner_id=owner_id, q=q, session=session
        )
        limit, offset = utils.get_limit_offset(limit, page)
    query = select(models.Extractor)
    if owner_id:
        query = query.where(models.Extractor.owner_id == owner_id)
    if workspace_id is not None:
        query = query.where(models.Extractor.workspace_id == workspace_id)
    if ids:
        query = query.where(models.Extractor.id.in_(ids))
    if not show_hidden:
        query = query.where(~models.Extractor.hidden)
    if q:
        query = query.where(
            sa.or_(
                models.Extractor.title.ilike(f'%{q}%'),
                models.Extractor.description.ilike(f'%{q}%'),
            )
        )

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)
    order_col = getattr(models.Extractor, order)
    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)

    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def get_extractor_by_id(id: int, session=None):
    return await base.get_by_id(models.Extractor, id, session=session)


@base.session_aware()
async def create_extractor(values, session=None):
    return await base.create_model(models.Extractor, values, session=session)


@base.session_aware()
async def update_extractor(extractor, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.Extractor).where(models.Extractor.id == extractor.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    extractor.update(new_values)
    return extractor


@base.session_aware()
async def update_extractors(ids: list, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.Extractor).where(models.Extractor.id.in_(ids))
    update_q = update_q.values(new_values)

    await session.execute(update_q)


@base.session_aware()
async def delete_extractor(id: int, session=None):
    delete_q = delete(models.Extractor).where(models.Extractor.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def get_extractor_count(workspace_ids: list, session=None):
    sql = text(
        'select count(*) as count from extractors '
        'where workspace_id in :workspace_ids'
    )
    params = {
        'workspace_ids': workspace_ids,
    }
    sql = sql.bindparams(sa.bindparam("workspace_ids", expanding=True))

    res = await session.execute(sql, params)
    count = res.scalar()

    return count


@base.session_aware()
async def get_workspace_extractor_count(
    owner_id: int = None,
    workspace_id: int = None,
    q: str = None,
    session=None
):
    where = []
    params = {}

    if workspace_id:
        where.append('workspace_id = :workspace_id')
        params['workspace_id'] = workspace_id
    if owner_id:
        where.append('owner_id = :owner_id')
        params['owner_id'] = owner_id
    if q:
        q = f'%{q}%'
        where.append('title ILIKE :q OR description ILIKE :q')
        params['q'] = q

    sql = text(
        'SELECT count(*) AS count FROM extractors '
        f'WHERE {" AND ".join(where)}'
    )

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
