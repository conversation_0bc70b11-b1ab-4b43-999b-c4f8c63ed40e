from sqlalchemy import delete
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def list_org_domains(org_id: int, session=None):
    query = select(models.OrganizationDomain).where(models.OrganizationDomain.org_id == org_id)
    query = query.order_by(models.OrganizationDomain.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def delete_org_domains(org_id: int, session=None):
    msg_delete_q = delete(models.OrganizationDomain).where(models.OrganizationDomain.org_id == org_id)
    await session.execute(msg_delete_q)


@base.session_aware()
async def create_org_domain(values, session=None):
    res = models.OrganizationDomain(**values)

    try:
        session.add(res)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for OrganizationDomain: %s" % e
        )

    return res


@base.session_aware()
async def list_orgs_domains(org_ids: list[int], session=None):
    query = select(models.OrganizationDomain).where(models.OrganizationDomain.org_id.in_(org_ids))
    query = query.order_by(models.OrganizationDomain.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_workspace_domain(domain: str, session=None):
    query = select(models.OrganizationDomain).where(models.OrganizationDomain.domain == domain)
    res = await session.execute(query)

    return res.scalar()
