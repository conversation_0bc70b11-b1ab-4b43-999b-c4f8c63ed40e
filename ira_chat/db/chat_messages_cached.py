from sqlalchemy import delete, update
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def create_message_cached(values, session=None):
    return await base.create_model(models.ChatMessageCached, values, session=session)


@base.session_aware()
async def get_message_cached_by_hash(hash: str, dataset_id: int = None, org_id: int = None, session=None):
    q = select(models.ChatMessageCached).where(models.ChatMessageCached.hash == hash)
    if dataset_id:
        q = q.where(models.ChatMessageCached.dataset_id == dataset_id)
    if org_id:
        q = q.where(models.ChatMessageCached.org_id == org_id)
    res = await session.execute(q)
    res = res.scalar()

    return res


@base.session_aware()
async def update_message_cached(message, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ChatMessageCached).where(models.ChatMessageCached.id == message.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    message.update(new_values)
    return message


@base.session_aware()
async def delete_message_cached(id: int, session=None):
    delete_q = delete(models.ChatMessageCached).where(models.ChatMessageCached.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_messages_cached_expired(session=None):
    delete_q = delete(models.ChatMessageCached).where(models.ChatMessageCached.expires_at < base.now())
    await session.execute(delete_q)
