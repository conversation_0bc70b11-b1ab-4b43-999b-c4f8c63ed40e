import sqlalchemy as sa
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>
from sqlalchemy import delete, update
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def get_workspace(name: str, org_id=None, session=None):
    query = select(models.Workspace).where(models.Workspace.name == name)
    if org_id:
        query = query.where(models.Workspace.org_id == org_id)
    res = await session.execute(query)

    return res.scalar()


@base.session_aware()
async def create_workspace(values, session=None):
    ws = models.Workspace(**values)

    try:
        session.add(ws)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Workspace: %s" % e
        )

    return ws


@base.session_aware()
async def get_workspace_by_id(id: int, notfoundok=False, session=None):
    query = select(models.Workspace).where(models.Workspace.id == id)
    res = await session.execute(query)
    res = res.scalar()

    if not res and not notfoundok:
        raise HTTPException(404, 'Workspace not found')

    return res


@base.session_aware()
async def list_workspaces_using_dataset(dataset_name: str, session=None):
    query = select(models.Workspace)

    query = query.where(models.Workspace.config['index'].astext.startswith(dataset_name))

    query = query.order_by(models.Workspace.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def list_workspaces(ids=None, org_id: int = None, public_access_types=False, session=None):
    if public_access_types:
        conditions = [models.Workspace.access_type.in_(models.ACCESS_TYPES_ANY)]
        generic = []
        if ids is not None:
            generic.append(models.Workspace.id.in_(ids))
        if org_id is not None:
            conditions.append(models.Workspace.org_id == org_id)
            generic.append(models.Workspace.org_id == org_id)

        query = select(models.Workspace).filter(
            sa.or_(
                sa.and_(*conditions),
                sa.and_(*generic)
            )
        )
    else:
        query = select(models.Workspace)
        if ids is not None:
            query = query.where(models.Workspace.id.in_(ids))
        if org_id is not None:
            query = query.where(models.Workspace.org_id == org_id)

    query = query.order_by(models.Workspace.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def update_workspace(ws: models.Workspace, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.Workspace).where(models.Workspace.id == ws.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    ws.update(new_values)

    return ws


@base.session_aware()
async def delete_workspace(id: int, session=None):
    delete_q = delete(models.Workspace).where(models.Workspace.id == id)
    await session.execute(delete_q)
    # await session.delete(message)
