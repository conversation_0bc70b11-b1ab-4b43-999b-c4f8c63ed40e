from sqlalchemy import update
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def list_provided_answers(workspace_id: int, app_id: int = None, object_ids: list[int] = None, session=None):
    q = select(models.ProvidedAnswer).where(models.ProvidedAnswer.workspace_id == workspace_id)
    if app_id:
        q = q.where(models.ProvidedAnswer.app_id == app_id)
    if object_ids:
        q = q.where(models.ProvidedAnswer.object_id.in_(object_ids))
    q = q.order_by(models.ProvidedAnswer.id)

    res = await session.execute(q)

    return res.scalars().fetchall()


@base.session_aware()
async def get_provided_answer_by(
    id: int = None,
    workspace_id: int = None,
    app_id: int = None,
    object_id: int = None,
    session=None
):
    q = select(models.ProvidedAnswer)
    if id:
        q = q.where(models.ProvidedAnswer.id == id)
    if workspace_id:
        q = q.where(models.ProvidedAnswer.workspace_id == workspace_id)
    if app_id:
        q = q.where(models.ProvidedAnswer.app_id == app_id)
    if object_id:
        q = q.where(models.ProvidedAnswer.object_id == object_id)
    res = await session.execute(q)
    res = res.scalar()

    return res


@base.session_aware()
async def create_provided_answer(values, session=None):
    return await base.create_model(models.ProvidedAnswer, values, session=session)


@base.session_aware()
async def update_provided_answer(answer, new_values, session=None):
    # new_values['updated_at'] = models.now()
    update_q = update(models.ProvidedAnswer).where(models.ProvidedAnswer.id == answer.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    answer.update(new_values)

    return answer
