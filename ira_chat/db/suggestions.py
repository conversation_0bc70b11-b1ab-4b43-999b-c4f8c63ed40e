from sqlalchemy import delete
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def create_suggestion(values, session=None):
    message = models.ChatSuggestion(**values)

    try:
        session.add(message)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ChatSuggestion: %s" % e
        )

    return message


@base.session_aware()
async def list_suggestions(
        chat_id: int, message_start_id: int = None, message_end_id: int = None, message_id: int = None,
        message_ids: list = None, limit=None, order_by='id', desc=False,
        session=None
):
    order_col = getattr(models.ChatSuggestion, order_by)
    if desc:
        order_col = order_col.desc()

    query = select(models.ChatSuggestion).where(models.ChatSuggestion.chat_id == chat_id).order_by(order_col)
    if limit is not None:
        query = query.limit(limit)
    if message_start_id is not None:
        query = query.where(models.ChatSuggestion.message_id > message_start_id)
    if message_end_id is not None:
        query = query.where(models.ChatSuggestion.message_id <= message_end_id)
    if message_id is not None:
        query = query.where(models.ChatSuggestion.message_id == message_id)
    if message_ids is not None:
        query = query.where(models.ChatSuggestion.message_id.in_(message_ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def delete_suggestions(chat_id: int, message_id: int = None, message_ids: list[int] = None, session=None):
    delete_q = delete(models.ChatSuggestion).where(models.ChatSuggestion.chat_id == chat_id)
    if message_id:
        delete_q = delete_q.where(models.ChatSuggestion.message_id == message_id)
    if message_ids:
        delete_q = delete_q.where(models.ChatSuggestion.message_id.in_(message_ids))
    await session.execute(delete_q)


@base.session_aware()
async def delete_suggestions_for_ids(chat_id: int, ids: list[int], session=None):
    delete_q = delete(models.ChatSuggestion).where(models.ChatSuggestion.id.in_(ids))
    delete_q = delete_q.where(models.ChatSuggestion.chat_id == chat_id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_suggestions_for_messages(chat_id: int, message_ids: list[int], session=None):
    delete_q = delete(models.ChatSuggestion).where(models.ChatSuggestion.chat_id == chat_id)
    delete_q = delete_q.where(models.ChatSuggestion.message_id.in_(message_ids))
    await session.execute(delete_q)


@base.session_aware()
async def delete_suggestions_for_chat(chat_id: int, session=None):
    delete_q = delete(models.ChatSuggestion).where(models.ChatSuggestion.chat_id == chat_id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_suggestions_for_chats(chat_ids: list[int], session=None):
    delete_q = delete(models.ChatSuggestion).where(models.ChatSuggestion.chat_id.in_(chat_ids))
    await session.execute(delete_q)
