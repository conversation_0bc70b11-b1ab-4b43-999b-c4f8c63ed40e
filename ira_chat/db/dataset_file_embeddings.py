from sqlalchemy import delete
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def create_dataset_file_embeddings(values: list, session=None):
    embeddings = [models.DatasetFileEmbeddings(**v) for v in values]

    try:
        session.add_all(embeddings)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for DatasetFileEmbeddings: %s" % e
        )

    return embeddings


@base.session_aware()
async def list_dataset_file_embeddings(file_id: str = None, index_id: str = None, session=None):
    query = select(models.DatasetFileEmbeddings)
    if file_id:
        query = query.where(models.DatasetFileEmbeddings.dataset_file_id == file_id)
    if index_id:
        query = query.where(models.DatasetFileEmbeddings.index_id == index_id)

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def delete_dataset_file_embeddings(file_id: int = None, index_id: str = None, ids: list[str] = None, session=None):
    query = delete(models.DatasetFileEmbeddings)
    if not file_id and not index_id:
        raise ValueError('Provide a filter for delete')
    if file_id:
        query = query.where(models.DatasetFileEmbeddings.dataset_file_id == file_id)
    if index_id:
        query = query.where(models.DatasetFileEmbeddings.index_id == index_id)
    if ids:
        query = query.where(models.DatasetFileEmbeddings.id.in_(ids))

    delete_q = query
    await session.execute(delete_q)
