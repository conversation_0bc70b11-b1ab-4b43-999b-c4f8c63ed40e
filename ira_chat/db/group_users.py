from sqlalchemy import delete, update
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def create_group_user(values, session=None):
    ws = models.GroupUser(**values)

    try:
        session.add(ws)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for GroupUser: %s" % e
        )

    return ws


@base.session_aware()
async def create_group_users(values: list, session=None):
    data = [models.GroupUser(**v) for v in values]

    try:
        session.add_all(data)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for GroupUser: %s" % e
        )

    return data


@base.session_aware()
async def update_group_user(group_user: models.GroupUser, new_values, session=None):
    update_q = update(models.GroupUser).where(models.GroupUser.id == group_user.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    group_user.update(new_values)

    return group_user


@base.session_aware()
async def update_group_users(user_id: int = None, user_login: str = None, new_values=None, session=None):
    if user_id:
        update_q = update(models.GroupUser).where(models.GroupUser.user_id == user_id)
    if user_login:
        update_q = update(models.GroupUser).where(models.GroupUser.user_login == user_login)
    update_q = update_q.values(new_values)

    await session.execute(update_q)


@base.session_aware()
async def list_group_users(group_id: int = None, workspace_id: int = None,
                           workspace_ids: list = None, user_id: int = None, session=None):
    query = select(models.GroupUser)
    if group_id:
        query = query.where(models.GroupUser.group_id == group_id)
    if workspace_id:
        query = query.where(models.GroupUser.workspace_id == workspace_id)
    if workspace_ids:
        query = query.where(models.GroupUser.workspace_id.in_(workspace_ids))
    if user_id:
        query = query.where(models.GroupUser.user_id == user_id)
    query = query.order_by(models.GroupUser.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def list_group_users_for_groups(group_ids: list[int], session=None):
    query = select(models.GroupUser)
    query = query.where(models.GroupUser.group_id.in_(group_ids))
    query = query.order_by(models.GroupUser.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_group_user(group_id: int, user_id: int, session=None):
    query = select(models.GroupUser)

    query = query.where(models.GroupUser.group_id == group_id).where(models.GroupUser.user_id == user_id)
    query = query.order_by(models.GroupUser.id)
    res = await session.execute(query)

    return res.scalar()


@base.session_aware()
async def get_group_user_by_id(id: int, session=None):
    query = select(models.GroupUser)

    query = query.where(models.GroupUser.id == id)
    query = query.order_by(models.GroupUser.id)
    res = await session.execute(query)

    return res.scalar()


@base.session_aware()
async def get_group_user_by_login(group_id: int, login: str, session=None):
    query = select(models.GroupUser)

    query = query.where(models.GroupUser.group_id == group_id).where(models.GroupUser.user_login == login)
    query = query.order_by(models.GroupUser.id)
    res = await session.execute(query)

    return res.scalar()


@base.session_aware()
async def delete_group_user(id: int, session=None):
    msg_delete_q = delete(models.GroupUser).where(models.GroupUser.id == id)
    await session.execute(msg_delete_q)


@base.session_aware()
async def delete_users_from_group(group_id: int, session=None):
    msg_delete_q = delete(models.GroupUser).where(models.GroupUser.group_id == group_id)
    await session.execute(msg_delete_q)


@base.session_aware()
async def delete_users_from_workspace(workspace_id: int, session=None):
    delete_q = delete(models.GroupUser).where(models.GroupUser.workspace_id == workspace_id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_user_from_workspace(workspace_id: int, user_id: int = None, user_login: str = None, session=None):
    delete_q = delete(models.GroupUser).where(models.GroupUser.workspace_id == workspace_id)
    if user_id:
        delete_q = delete_q.where(models.GroupUser.user_id == user_id)
    if user_login:
        delete_q = delete_q.where(models.GroupUser.user_login == user_login)
    await session.execute(delete_q)
