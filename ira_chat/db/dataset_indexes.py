import sqlalchemy as sa
from fastapi import HTTPException
from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.exceptions import NotFoundException
from ira_chat.utils import utils


@base.session_aware()
async def list_dataset_indexes(
    dataset_id: str = None,
    dataset_ids: list[str] = None,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    session=None,
):
    count = None
    offset = None
    if limit and page:
        count = await get_dataset_index_count(dataset_id=dataset_id, q=q)
        limit, offset = utils.get_limit_offset(limit, page)
    query = select(models.DatasetIndex)

    if dataset_id is not None:
        query = query.where(models.DatasetIndex.dataset_id == dataset_id)
    if q:
        query = query.where(models.DatasetIndex.name.ilike(f'%{q}%'))

    if dataset_ids:
        query = query.where(models.DatasetIndex.dataset_id.in_(dataset_ids))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)
    order_col = getattr(models.DatasetIndex, order)
    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)

    res = await session.execute(query)

    if limit and page:
        return res.scalars().fetchall(), count
    else:
        return res.scalars().fetchall()


@base.session_aware()
async def create_dataset_index(values, session=None):
    dataset_index = models.DatasetIndex(**values)

    try:
        session.add(dataset_index)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for DatasetIndex: %s" % e
        )
    except Exception as e:
        if 'duplicate key value violates unique constraint' in str(e):
            raise HTTPException(
                409, "Duplicate entry for Dataset: %s" % e
            )
        else:
            raise

    return dataset_index


@base.session_aware()
async def get_dataset_index(id: str, session=None):
    query = select(models.DatasetIndex).where(models.DatasetIndex.id == id)
    res = await session.execute(query)
    res = res.scalar()

    if not res:
        raise NotFoundException(f'DatasetIndex not found for id: {id}')
    return res


@base.session_aware()
async def get_dataset_index_by_dataset_and_name(dataset_id: str, name: str, session=None):
    query = select(models.DatasetIndex)
    query = query.where(models.DatasetIndex.dataset_id == dataset_id).where(models.DatasetIndex.name == name)
    res = await session.execute(query)
    res = res.scalar()

    if not res:
        raise NotFoundException(f'DatasetIndex not found for name: {name}')
    return res


@base.session_aware()
async def get_dataset_index_file_count(index_id: str, session=None):
    sql = text("SELECT count(DISTINCT dataset_file_id) FROM dataset_file_embeddings WHERE index_id = :index_id")

    res = await session.execute(sql, {"index_id": index_id})
    res = res.scalar()

    return res


@base.session_aware()
async def get_dataset_index_by_ws_and_name(ws_id: int, name: str, session=None):
    query = select(models.DatasetIndex).where(models.DatasetIndex.dataset_id == ws_id).where(models.DatasetIndex.name == name)
    res = await session.execute(query)
    res = res.scalar()

    return res


@base.session_aware()
async def update_dataset_index(dataset_index: models.DatasetIndex, new_values: dict, session=None):
    new_values['updated_at'] = models.now()
    update_q = update(models.DatasetIndex).where(models.DatasetIndex.id == dataset_index.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    dataset_index.update(new_values)

    return dataset_index


@base.session_aware()
async def delete_dataset_index(id: str, session=None):
    delete_q = delete(models.DatasetIndex).where(models.DatasetIndex.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_dataset_indexes(dataset_id: str, session=None):
    delete_q = delete(models.DatasetIndex).where(models.DatasetIndex.dataset_id == dataset_id)
    await session.execute(delete_q)


@base.session_aware()
async def get_dataset_index_count(
    dataset_id: str = None,
    dataset_ids: list = None,
    owner_id: int = None,
    status: str = None,
    q: str = None,
    session=None
):
    where = []
    params = {}
    if dataset_ids:
        where.append('dataset_id in :dataset_ids')
        params['dataset_ids'] = dataset_ids
    if dataset_id:
        where.append('dataset_id = :dataset_id')
        params['dataset_id'] = dataset_id
    if owner_id:
        where.append('owner_id = :owner_id')
        params['owner_id'] = owner_id
    if status:
        where.append("status->>'status' = :status")
        params['status'] = status
    if q:
        q = f'%{q}%'
        where.append('name ILIKE :q')
        params['q'] = q

    sql = text(
        'SELECT count(*) AS count FROM dataset_indexes '
        f'WHERE {" AND ".join(where)}'
    )
    if dataset_ids:
        sql = sql.bindparams(sa.bindparam("dataset_ids", expanding=True))

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
