import datetime

from sqlalchemy import delete, update
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def create_timeline_point(values, session=None):
    return await base.create_model(models.TimelinePoint, values, session=session)


@base.session_aware()
async def list_timeline_points(
    owner_id: int = None,
    workspace_id: int = None,
    timeline_id: int = None,
    timeline_ids: list = None,
    end_id: int = None,
    start_date: datetime.date = None,
    end_date: datetime.date = None,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    query = select(models.TimelinePoint)
    if owner_id:
        query = query.where(models.TimelinePoint.owner_id == owner_id)
    if workspace_id is not None:
        query = query.where(models.TimelinePoint.workspace_id == workspace_id)
    if timeline_id is not None:
        query = query.where(models.TimelinePoint.timeline_id == timeline_id)
    if timeline_ids is not None:
        query = query.where(models.TimelinePoint.timeline_id.in_(timeline_ids))
    if end_id:
        query = query.where(models.TimelinePoint.id <= end_id)
    if end_date:
        query = query.where(models.TimelinePoint.date <= end_date)
    if start_date:
        query = query.where(models.TimelinePoint.date > start_date)

    order_col = getattr(models.TimelinePoint, order)
    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_timeline_point_by_id(id: int, session=None):
    return await base.get_by_id(models.TimelinePoint, id, session=session)


@base.session_aware()
async def get_timeline_point_by_extractor(extractor_id: int, session=None):
    query = select(models.TimelinePoint)
    query = query.where(models.TimelinePoint.extractor_id == extractor_id)

    res = await session.execute(query)
    res = res.scalar()
    return res


@base.session_aware()
async def get_timeline_point_by_date(timeline_id: int, date: datetime.date, session=None):
    query = select(models.TimelinePoint)
    query = query.where(models.TimelinePoint.timeline_id == timeline_id).where(models.TimelinePoint.date == date)

    res = await session.execute(query)
    res = res.scalar()
    return res


@base.session_aware()
async def get_timeline_prev_point(timeline_id: int, point: models.TimelinePoint, session=None):
    query = select(models.TimelinePoint)
    query = query.where(models.TimelinePoint.timeline_id == timeline_id)
    query = query.where(models.TimelinePoint.date < point.date)
    query = query.order_by(models.TimelinePoint.date.desc()).limit(1)

    res = await session.execute(query)
    res = res.scalar()
    return res


@base.session_aware()
async def delete_timeline_point(id: int, session=None):
    delete_q = delete(models.TimelinePoint).where(models.TimelinePoint.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def update_timeline_point(point: models.TimelinePoint, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.TimelinePoint).where(models.TimelinePoint.id == point.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)
    point.update(new_values)
    return point


@base.session_aware()
async def update_timeline_points(timeline_id: int = None, ids: list = None, new_values=None, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.TimelinePoint)
    if timeline_id:
        update_q = update_q.where(models.TimelinePoint.timeline_id == timeline_id)
    if ids:
        update_q = update_q.where(models.TimelinePoint.id.in_(ids))

    update_q = update_q.values(new_values)

    await session.execute(update_q)


@base.session_aware()
async def delete_timeline_points(timeline_id: int = None, point_id: int = None, session=None):
    delete_q = delete(models.TimelinePoint).where(models.TimelinePoint.timeline_id == timeline_id)
    if point_id:
        delete_q = delete_q.where(models.TimelinePoint.id == point_id)
    await session.execute(delete_q)
