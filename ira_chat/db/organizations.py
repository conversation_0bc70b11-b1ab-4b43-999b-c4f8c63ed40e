from sqlalchemy import exc
from sqlalchemy import update
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.utils import blowfish_utils


@base.session_aware()
async def get_org_by_domain(domain: str, session=None):
    query = select(models.Organization).where(models.OrganizationDomain.domain == domain)
    query = query.where(models.OrganizationDomain.org_id == models.Organization.id)
    res = await session.execute(query)

    return res.scalar()


@base.session_aware()
async def get_org_by_id(id: int, session=None):
    query = select(models.Organization).where(models.Organization.id == id)
    res = await session.execute(query)

    return res.scalar()


@base.session_aware()
async def get_org(name: str, session=None):
    query = select(models.Organization).where(models.Organization.name == name)
    res = await session.execute(query)

    return res.scalar()


@base.session_aware()
async def update_org(org: models.Organization, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    if 'config' in new_values:
        new_values['config'] = blowfish_utils.encrypt_string(new_values['config'])

    update_q = update(models.Organization).where(models.Organization.id == org.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    org.update(new_values)

    return org


@base.session_aware()
async def delete_org(id: int, session=None):
    return await base.delete_by_id(models.Organization, id, session=session)


@base.session_aware()
async def list_orgs_all(session=None):
    query = select(models.Organization)
    query = query.order_by(models.Organization.id)

    query = query.where(models.Organization.deleted_at.is_(None))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def list_orgs(ids: list = None, session=None):
    query = select(models.Organization)
    if ids:
        query = query.where(models.Organization.id.in_(ids))

    query = query.where(models.Organization.deleted_at.is_(None))

    query = query.order_by(models.Organization.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def create_org(values, session=None):
    values['config'] = blowfish_utils.encrypt_string(values['config'])
    ws = models.Organization(**values)

    try:
        session.add(ws)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Organization: %s" % e
        )

    return ws
