from sqlalchemy import delete, text
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def list_org_webhook_items(webhook_id: int, session=None):
    query = select(models.OrganizationWebhookItem).where(models.OrganizationWebhookItem.webhook_id == webhook_id)
    query = query.order_by(models.OrganizationWebhookItem.id.desc())
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_org_webhook_item_count(webhook_id: int, session=None):
    raw = 'SELECT COUNT(*) as count FROM organization_webhook_items WHERE webhook_id = :webhook_id'
    params = {'webhook_id': webhook_id}

    sql = text(raw)
    res = await session.execute(sql, params)
    count = res.scalar()

    return count


@base.session_aware()
async def delete_org_webhook_items(org_id: int, session=None):
    msg_delete_q = delete(models.OrganizationWebhookItem).where(models.OrganizationWebhookItem.org_id == org_id)
    await session.execute(msg_delete_q)


@base.session_aware()
async def delete_org_webhook_item(id: int, session=None):
    msg_delete_q = delete(models.OrganizationWebhookItem).where(models.OrganizationWebhookItem.id == id)
    await session.execute(msg_delete_q)


@base.session_aware()
async def keep_n_webhook_items(webhook_id: int, n: int = 10, session=None):
    count = await get_org_webhook_item_count(webhook_id, session=session)
    if count > n:
        raw = (
            "DELETE FROM organization_webhook_items "
            f"WHERE id = ANY(ARRAY("
            f"SELECT id FROM organization_webhook_items WHERE webhook_id = :webhook_id ORDER BY id LIMIT {count-n})) "
        )
        q = text(raw)
        await session.execute(q, {'webhook_id': webhook_id})

    return


@base.session_aware()
async def create_org_webhook_item(values, session=None):
    res = models.OrganizationWebhookItem(**values)

    try:
        session.add(res)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for OrganizationWebhookItem: %s" % e
        )

    return res


@base.session_aware()
async def get_org_webhook_item_by_id(id: int, session=None):
    return await base.get_by_id(models.OrganizationWebhookItem, id, session=session)


@base.session_aware()
async def list_orgs_webhook_items(org_ids: list[int], session=None):
    query = select(models.OrganizationWebhookItem).where(models.OrganizationWebhookItem.org_id.in_(org_ids))
    query = query.order_by(models.OrganizationWebhookItem.id)
    res = await session.execute(query)

    return res.scalars().fetchall()
