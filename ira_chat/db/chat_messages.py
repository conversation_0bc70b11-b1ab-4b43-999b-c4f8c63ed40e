import datetime

from sqlalchemy import delete, update, text
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.exceptions import NotFoundException
from ira_chat.utils import utils


@base.session_aware()
async def list_all_chat_messages(
    workspace_id: int,
    chat_id: int = None,
    ids: list[int] = None,
    ignore_statuses=None,
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    session=None
):
    query = select(models.ChatMessage).where(models.Chat.workspace_id == workspace_id)
    if chat_id:
        query = query.where(models.ChatMessage.chat_id == chat_id).where(models.ChatMessage.chat_id == models.Chat.id)
    else:
        query = query.where(models.ChatMessage.chat_id == models.Chat.id)
    if ids:
        query = query.where(models.ChatMessage.id.in_(ids))

    if ignore_statuses:
        query = query.where(~models.ChatMessage.status.in_(ignore_statuses))
    if start_date:
        query = query.where(models.ChatMessage.created_at >= start_date)
    if end_date:
        query = query.where(models.ChatMessage.created_at <= end_date)

    query = query.order_by(models.ChatMessage.created_at)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def create_message(values, session=None):
    return await base.create_model(models.ChatMessage, values, session=session)


@base.session_aware()
async def get_messages_count_ws(chat_id: int, session=None):
    raw = 'select count(*) as count from chat_messages where chat_id = :chat_id'
    params = {'chat_id': chat_id}

    sql = text(raw)
    res = await session.execute(sql, params)
    chat_count = res.scalar()

    return chat_count


@base.session_aware()
async def list_messages(
    chat_id: int,
    start_id: int = None,
    end_id: int = None,
    page=None,
    limit=None,
    order_by='id',
    desc=False,
    role: str = None,
    ignore_statuses=None,
    session=None
):
    order_col = getattr(models.ChatMessage, order_by)
    if desc:
        order_col = order_col.desc()

    offset = None
    if limit and page:
        count = await get_messages_count_ws(chat_id=chat_id)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ChatMessage).where(models.ChatMessage.chat_id == chat_id).order_by(order_col)
    if limit is not None:
        query = query.limit(limit)
    if offset:
        query = query.offset(offset)
    if start_id is not None:
        query = query.where(models.ChatMessage.id > start_id)
    if end_id is not None:
        query = query.where(models.ChatMessage.id <= end_id)
    if role:
        query = query.where(models.ChatMessage.role == role)
    if ignore_statuses:
        query = query.where(~models.ChatMessage.status.in_(ignore_statuses))
    res = await session.execute(query)

    if limit and page:
        return res.scalars().fetchall(), count
    else:
        return res.scalars().fetchall()


@base.session_aware()
async def get_message_by_id(id: int, session=None):
    q = select(models.ChatMessage).where(models.ChatMessage.id == id)
    res = await session.execute(q)
    res = res.scalar()

    if not res:
        raise NotFoundException(f'Message not found for id: {id}')
    return res


@base.session_aware()
async def update_message_by_id(id, values, session=None):
    message = await get_message_by_id(id, session=session)

    if not message:
        raise NotFoundException(f'Message not found for id: {id}')

    message.update(values.copy())

    return message


@base.session_aware()
async def update_message(message: models.ChatMessage, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ChatMessage).where(models.ChatMessage.id == message.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    message.update(new_values)
    return message


@base.session_aware()
async def delete_message(id: int, session=None):
    delete_q = delete(models.ChatMessage).where(models.ChatMessage.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_messages(chat_id: int, session=None):
    # message = await get_message_by_id(id, session=session)

    msg_delete_q = delete(models.ChatMessage).where(models.ChatMessage.chat_id == chat_id)
    await session.execute(msg_delete_q)
    # await session.delete(message)
