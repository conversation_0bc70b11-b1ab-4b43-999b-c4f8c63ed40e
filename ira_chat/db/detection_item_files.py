from sqlalchemy import delete, update
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def create_detection_file(values, session=None):
    return await base.create_model(models.DetectionItemFile, values, session=session)


@base.session_aware()
async def list_detection_files(
    owner_id: int = None, workspace_id: int = None,
    detection_item_id: int = None, detection_item_ids: list = None, ids: list = None, session=None
):
    query = select(models.DetectionItemFile)
    if owner_id:
        query = query.where(models.DetectionItemFile.owner_id == owner_id)
    if workspace_id is not None:
        query = query.where(models.DetectionItemFile.workspace_id == workspace_id)
    if detection_item_id is not None:
        query = query.where(models.DetectionItemFile.detection_item_id == detection_item_id)
    if detection_item_ids is not None:
        query = query.where(models.DetectionItemFile.detection_item_id.in_(detection_item_ids))
    if ids is not None:
        query = query.where(models.DetectionItemFile.id.in_(ids))

    query = query.order_by(models.DetectionItemFile.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_detection_file_by_id(id: str, session=None):
    return await base.get_by_id(models.DetectionItemFile, id, session=session)


@base.session_aware()
async def delete_detection_file(id: str, session=None):
    delete_q = delete(models.DetectionItemFile).where(models.DetectionItemFile.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def update_detection_file(id: int, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.DetectionItemFile).where(models.DetectionItemFile.id == id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)


@base.session_aware()
async def delete_detection_files(detection_item_id: int = None, file_id: int = None, ids: list = None, session=None):
    delete_q = delete(models.DetectionItemFile).where(models.DetectionItemFile.detection_item_id == detection_item_id)
    if file_id:
        delete_q = delete_q.where(models.DetectionItemFile.id == file_id)
    if ids:
        delete_q = delete_q.where(models.DetectionItemFile.id.in_(ids))
    await session.execute(delete_q)
