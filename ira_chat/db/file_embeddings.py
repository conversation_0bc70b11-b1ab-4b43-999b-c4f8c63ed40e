from sqlalchemy import delete
from sqlalchemy import exc
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def create_embeddings(values: list, session=None):
    embeddings = [models.FileEmbeddings(**v) for v in values]

    try:
        session.add_all(embeddings)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Embeddings: %s" % e
        )

    return embeddings


@base.session_aware()
async def list_embeddings(file_id: int = None, extractor_file_id: int = None, session=None):
    query = select(models.FileEmbeddings)
    if file_id:
        query = query.where(models.FileEmbeddings.file_id == file_id)
    if extractor_file_id:
        query = query.where(models.FileEmbeddings.extractor_file_id == extractor_file_id)
    query = query.where(models.FileEmbeddings.file_id == file_id)
    query = query.order_by(models.FileEmbeddings.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def delete_embeddings(file_id: int = None, extractor_file_id: int = None, session=None):
    query = delete(models.FileEmbeddings)
    if not file_id and not extractor_file_id:
        raise ValueError('Provide a filter for delete')
    if file_id:
        query = query.where(models.FileEmbeddings.file_id == file_id)
    if extractor_file_id:
        query = query.where(models.FileEmbeddings.extractor_file_id == extractor_file_id)

    delete_q = query
    await session.execute(delete_q)
