import asyncio
import functools
import logging
import os

import requests
from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>

from ira_chat.doc_converter.base import DocConverter

logger = logging.getLogger(__name__)


class RemoteConverter(DocConverter):
    base_url = os.getenv("CONVERTER_API_URL")
    session = requests.Session()

    async def aconvert_file(self, name, from_, to, fd) -> bytes:
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.convert_file, name, from_, to, fd)

    def convert_file(self, name, from_, to, fd) -> bytes:
        logger.info(f"[Converter] Convert {name} from {from_} to {to}")
        u = os.path.join(self.base_url, f'api/v1/files/convert-raw?from={from_}&to={to}')

        post = functools.partial(self.session.post, data=fd, params={'filename': name})
        resp = post(u)
        self._raise_for_status(resp)

        converted_data = resp.content
        resp.close()
        logger.info(f"[Converter] Done convert {name} from {from_} to {to}")
        return converted_data

    def _raise_for_status(self, resp: requests.Response):
        if resp.status_code < 400:
            return
        # {"status":404,"reason":"","msg":"msg"}
        try:
            error_data = resp.json()
        except:
            error_data = {'status': 500, 'msg': resp.content}

        raise HTTPException(error_data['status'], error_data['msg'])
