import asyncio
import logging
import os
import random
import shlex
import shutil
import subprocess
import tempfile

from ira_chat.doc_converter.base import DocConverter

logger = logging.getLogger(__name__)


class LocalConverter(DocConverter):
    data_dir = os.path.expanduser(os.getenv("DATA_DIR", "~/.ira-chat"))

    async def aconvert_file(self, name, from_, to, fd):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.convert_file, name, from_, to, fd)

    def convert_file(self, name, from_, to, fd):
        tmp_input_path = tempfile.mktemp(suffix=f'.{from_.removeprefix(".")}')
        with open(tmp_input_path, 'wb') as f:
            if isinstance(fd, bytes):
                f.write(fd)
            elif isinstance(fd, str):
                f.write(fd.encode())
            else:
                f.write(fd.read())

        out_path, out_dir = self.convert_path(from_, to, tmp_input_path)
        with open(out_path, 'rb') as f:
            data = f.read()

        try:
            os.remove(tmp_input_path)
        except:
            pass
        try:
            os.remove(out_path)
        except:
            pass
        try:
            os.rmdir(out_dir)
        except:
            pass

        return data

    def convert_path(self, from_: str, to: str, path: str) -> tuple[str, str]:
        logger.info(f"[Local Converter] Convert {from_} to {to}")
        tmpdir = tempfile.mkdtemp()
        out_path = os.path.join(tmpdir, os.path.basename(path).removesuffix(from_) + to.removeprefix('.'))
        if 'pdf' in to:
            to = 'pdf:writer_pdf_Export'

        random_num = random.randint(0, 100000)
        user_dir = f'/tmp/test{random_num}'
        cmd = f"libreoffice --headless -env:UserInstallation=file://{user_dir} --convert-to {to} --outdir {tmpdir} '{path}'"
        p = subprocess.run(shlex.split(cmd), check=True)

        try:
            shutil.rmtree(user_dir)
        except:
            pass
        logger.info(f"[Local Converter] Done convert {from_} to {to} [exitcode={p.returncode}]")
        return out_path, tmpdir
