from collections import defaultdict
from typing import List

from langchain.retrievers import MultiVectorRetriever, ParentDocumentRetriever
from langchain.retrievers.multi_vector import SearchType
from langchain_core.callbacks import CallbackManagerForRetrieverRun, AsyncCallbackManagerForRetrieverRun
from langchain_core.documents import Document
from langchain_core.vectorstores import VectorStoreRetriever


def measure_blocking_code(f):
    import time
    import asyncio

    async def wrapper(*args, **kwargs):
        t = 0
        coro = f(*args, **kwargs)
        try:
            while True:
                t0 = time.perf_counter()
                future = coro.send(None)
                t1 = time.perf_counter()
                t += t1 - t0
                while not future.done():
                    await asyncio.sleep(0)
                future.result()  # raises exceptions if any
        except StopIteration as e:
            print(f'Function took {t:.2e} sec')
            return e.value
    return wrapper


class VectorStoreRetrieverWithScore(VectorStoreRetriever):
    def _get_relevant_documents(
        self, query: str, *, run_manager: CallbackManagerForRetrieverRun, **kwargs
    ) -> List[Document]:
        if self.search_type == "similarity":
            docs_and_similarities = self.vectorstore.similarity_search_with_relevance_scores(
                query, **self.search_kwargs
            )
            (docs, scores) = zip(*docs_and_similarities) if docs_and_similarities else ([], [])
        elif self.search_type == "similarity_score_threshold":
            docs_and_similarities = (
                self.vectorstore.similarity_search_with_relevance_scores(
                    query, **self.search_kwargs
                )
            )
            (docs, scores) = zip(*docs_and_similarities) if docs_and_similarities else ([], [])
        elif self.search_type == "mmr":
            docs = self.vectorstore.max_marginal_relevance_search(
                query, **self.search_kwargs
            )
            scores = [0.88] * len(docs)
        else:
            raise ValueError(f"search_type of {self.search_type} not allowed.")

        if scores:
            for doc, score in zip(docs, scores):
                doc.metadata["score"] = score

        return docs

    # @measure_blocking_code
    async def _aget_relevant_documents(
        self, query: str, *, run_manager: AsyncCallbackManagerForRetrieverRun, **kwargs
    ) -> List[Document]:
        if self.search_type == "similarity":
            docs_and_similarities = await self.vectorstore.asimilarity_search_with_relevance_scores(
                query, **self.search_kwargs
            )
            (docs, scores) = zip(*docs_and_similarities) if docs_and_similarities else ([], [])
        elif self.search_type == "similarity_score_threshold":
            docs_and_similarities = (
                await self.vectorstore.asimilarity_search_with_relevance_scores(
                    query, **self.search_kwargs
                )
            )
            (docs, scores) = zip(*docs_and_similarities) if docs_and_similarities else ([], [])
        elif self.search_type == "mmr":
            docs = await self.vectorstore.amax_marginal_relevance_search(
                query, **self.search_kwargs
            )
            scores = [0.88] * len(docs)
        else:
            raise ValueError(f"search_type of {self.search_type} not allowed.")

        if scores:
            for doc, score in zip(docs, scores):
                doc.metadata["score"] = score
        return docs


class ParentDocumentRetrieverWithScore(ParentDocumentRetriever):
    def _get_relevant_documents(
        self, query: str, *, run_manager: CallbackManagerForRetrieverRun
    ) -> List[Document]:
        """Get documents relevant to a query.
        Args:
            query: String to find relevant documents for
            run_manager: The callbacks handler to use
        Returns:
            List of relevant documents
        """
        sub_docs, scores = [], []
        if self.search_type == SearchType.mmr:
            sub_docs = self.vectorstore.max_marginal_relevance_search(
                query, **self.search_kwargs
            )
        elif self.search_type == SearchType.similarity_score_threshold:
            sub_docs_and_similarities = (
                self.vectorstore.similarity_search_with_relevance_scores(
                    query, **self.search_kwargs
                )
            )
            if sub_docs_and_similarities:
                sub_docs, scores = zip(*sub_docs_and_similarities)
        else:
            sub_docs_and_similarities = self.vectorstore.similarity_search_with_relevance_scores(
                query, **self.search_kwargs
            )
            if sub_docs_and_similarities:
                sub_docs, scores = zip(*sub_docs_and_similarities)

        # We do this to maintain the order of the ids that are returned
        ids = []
        parent_doc_id_to_scores = defaultdict(list)
        for i, d in enumerate(sub_docs):
            if scores:
                parent_doc_id_to_scores[d.metadata[self.id_key]].append(scores[i])
            if self.id_key in d.metadata and d.metadata[self.id_key] not in ids:
                ids.append(d.metadata[self.id_key])
        docs = self.docstore.mget(ids)
        docs = [d for d in docs if d is not None]
        for doc in docs:
            doc_id = doc.metadata['_id']
            if doc_id in parent_doc_id_to_scores:
                doc.metadata['score'] = sum(parent_doc_id_to_scores[doc_id]) / len(parent_doc_id_to_scores[doc_id])

        return docs

    async def _aget_relevant_documents(
        self, query: str, *, run_manager: AsyncCallbackManagerForRetrieverRun
    ) -> List[Document]:
        """Asynchronously get documents relevant to a query.
        Args:
            query: String to find relevant documents for
            run_manager: The callbacks handler to use
        Returns:
            List of relevant documents
        """
        sub_docs, scores = [], []
        if self.search_type == SearchType.mmr:
            sub_docs = await self.vectorstore.amax_marginal_relevance_search(
                query, **self.search_kwargs
            )
        elif self.search_type == SearchType.similarity_score_threshold:
            sub_docs_and_similarities = (
                await self.vectorstore.asimilarity_search_with_relevance_scores(
                    query, **self.search_kwargs
                )
            )
            if sub_docs_and_similarities:
                sub_docs, scores = zip(*sub_docs_and_similarities)
        else:
            sub_docs_and_similarities = await self.vectorstore.asimilarity_search_with_relevance_scores(
                query, **self.search_kwargs
            )
            if sub_docs_and_similarities:
                sub_docs, scores = zip(*sub_docs_and_similarities)

        # We do this to maintain the order of the ids that are returned
        ids = []
        parent_doc_id_to_scores = defaultdict(list)
        for i, d in enumerate(sub_docs):
            if scores:
                parent_doc_id_to_scores[d.metadata[self.id_key]].append(scores[i])
            if self.id_key in d.metadata and d.metadata[self.id_key] not in ids:
                ids.append(d.metadata[self.id_key])
        docs = await self.docstore.amget(ids)
        docs = [d for d in docs if d is not None]
        for doc in docs:
            doc_id = doc.metadata['_id']
            if doc_id in parent_doc_id_to_scores:
                doc.metadata['score'] = sum(parent_doc_id_to_scores[doc_id]) / len(parent_doc_id_to_scores[doc_id])

        return docs
