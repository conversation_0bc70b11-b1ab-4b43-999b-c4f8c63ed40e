from ira_chat.db import models
from ira_chat.db import api as db_api
from ira_chat.services import dataset_utils


async def get_dataset_for_point(org_id: int, point: models.TimelinePoint):
    ext = await db_api.get_extractor_by_id(point.extractor_id)
    dataset_name, index_name = dataset_utils.get_dataset_index_names(ext.get_config())
    if dataset_name:
        dataset = await db_api.get_dataset_by_org_and_name(org_id, dataset_name)
        return dataset

    return None


async def list_datasets_for_points(org_id: int, points: list[models.TimelinePoint], get_extractors=False):
    if not points:
        datasets = []
        extractors = []
    else:
        extractor_ids = [point.extractor_id for point in points]
        extractors, _ = await db_api.list_extractors(ids=extractor_ids)
        names = [dataset_utils.get_dataset_index_names(ext.get_config())[0] for ext in extractors]
        datasets = await db_api.list_datasets_by_org_and_names(org_id, names)
        # Resort datasets in the same order as points
        datasets.sort(key=lambda d: names.index(d.name))

    if get_extractors:
        return datasets, extractors
    return datasets
