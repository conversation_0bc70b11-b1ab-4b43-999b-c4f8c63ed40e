import collections
import logging

from langchain_community.embeddings import OllamaEmbeddings
from langchain_community.chat_models.ollama import ChatOllama
from langchain_core.language_models import BaseChatModel

from ira_chat.services.llms import base

logger = logging.getLogger(__name__)


class OllamaLLM(base.InitLLM):
    def _init_llm(self, params: dict):
        if self.need_vision:
            params['model_name'] = params.get('vision_model', 'llava:34b')
        llm = ChatOllama(
            temperature=float(params['temperature']),
            model=self.llm_params.get('model_name', 'mistral'),
            base_url=self.llm_params.get('ollama_url', 'http://ollama.ollama:11434'),
            callbacks=self.get_callbacks(),
        )
        return llm

    def init_streaming_llm(self, params: dict) -> BaseChatModel:
        stream_callback = base.StreamingCallbackHandler(params['stream_queue'], params['stream_event'])
        llm = self.init_llm(params)
        llm.callbacks.append(stream_callback)

        return llm

    def init_embeddings(self, override: dict = None):
        logger.info("INIT EMBEDDING OLLAMA")
        params = self.get_embedding_model_params(self.org_config)
        if override:
            params.update(override)
        return OllamaEmbeddings(
            model=params.get('embedding_model') or 'nomic-embed-text',
            base_url=params.get('ollama_url', 'http://ollama.ollama:11434')
        )

    @staticmethod
    def available_models():
        return {
            'models': [
                'deepseek-r1:32b',
                'deepseek-r1:70b',
                'deepseek-r1:671b',
                'llama-3.1:70b',
                'llama-3.2-11b-vision-preview',
                'llama-3.2-90b-vision-preview',
                'llama-3.3:70b',
                'llava:34b'
            ],
            'vision_models': [
                'llava:34b',
                'llama-3.2-11b-vision-preview',
                'llama-3.2-90b-vision-preview'
            ],
            'embedding_models': [
                'nomic-embed-text',
                'mxbai-embed-large',
                'all-minilm',
            ],
        }

    @staticmethod
    def prices_per_model():
        return {}

    @staticmethod
    def token_limit_per_model():
        return collections.defaultdict(lambda: 128000)

    @classmethod
    def compute_cost(cls, messages, output, input_tokens, output_tokens, model_name):
        return 0
