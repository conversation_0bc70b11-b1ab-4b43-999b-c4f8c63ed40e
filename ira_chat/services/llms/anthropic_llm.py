import collections
import copy
import logging

from langchain_anthropic import ChatAnthropic
from langchain_core.language_models import BaseChatModel

from ira_chat.services.llms import base
from ira_chat.utils.utils import llm_vision_model_by_llm_type

logger = logging.getLogger(__name__)


class Anthropic(base.InitLLM):
    def __init__(self, org_config: dict, params: dict, org_id: int, workspace_id: int = None):
        super(Anthropic, self).__init__(org_config, params, org_id, workspace_id)

        # Change default retries
        from anthropic import _constants
        _constants.DEFAULT_MAX_RETRIES = 15

    def _init_llm(self, params: dict):
        if self.need_vision:
            params['model_name'] = params.get('vision_model', llm_vision_model_by_llm_type(self.llm_type))
            params['max_output_tokens'] = min(params.get('max_output_tokens', 4096), 4096)
        logger.info("INIT ANTHROPIC")
        llm = ChatAnthropic(
            model=params.get('model_name') or 'claude-3-5-sonnet-20240620',
            max_tokens=params.get('max_output_tokens') or 4096,
            temperature=float(params['temperature']),
            anthropic_api_key=self.llm_params.get('anthropic_api_key'),
            callbacks=self.get_callbacks(),
        )
        return llm

    def init_streaming_llm(self, params: dict) -> BaseChatModel:
        stream_callback = base.StreamingCallbackHandler(params['stream_queue'], params['stream_event'])
        llm = self.init_llm(params)
        llm.streaming = True
        llm.callbacks.append(stream_callback)

        return llm

    def init_embeddings(self, override: dict = None):
        logger.info("INIT EMBEDDING ANTHROPIC")

        params = self.get_embedding_model_params(self.org_config)
        # Voyage / OpenAI

        embedding_provider = params.get('embedding_provider', 'openai')
        if embedding_provider == 'anthropic':
            raise ValueError('Embedding provider should be one of {openai, voyage}')
        org_config_copy = copy.deepcopy(self.org_config)
        org_config_copy['llm_type'] = embedding_provider
        if override:
            org_config_copy.update(override)
        # Make a recursion and initialize embedding model referring new llm_type
        from ira_chat.services.vectorstore import get_embedding_model

        return get_embedding_model(org_config_copy, self.org_id, self.workspace_id)

    @staticmethod
    def available_models():
        return {
            'models': [
                'claude-3-opus-20240229',
                'claude-3-sonnet-20240229',
                'claude-3-haiku-20240307',
                'claude-3-5-haiku-20241022',
                'claude-3-5-sonnet-20240620'
            ],
            'vision_models': [
                'claude-3-opus-20240229',
                'claude-3-sonnet-20240229',
                'claude-3-haiku-20240307',
                'claude-3-5-sonnet-20240620'
            ],
            'embedding_models': [],
            'embedding_providers': ['openai', 'voyage'],
        }

    @staticmethod
    def prices_per_model() -> dict:
        # anthropic models, price for 1M tokens
        prices = {
            'claude-3-opus-20240229': [15, 75],
            'claude-3-sonnet-20240229': [3, 15],
            'claude-3-5-sonnet-20240620': [3, 15],
            'claude-3-haiku-20240307': [0.25, 1.25],
            'claude-3-5-haiku-20241022': [1, 5],
        }
        return prices

    @staticmethod
    def token_limit_per_model():
        limits = collections.defaultdict(lambda: [200000, 4096])
        limits.update({
            'claude-3-opus-20240229': [200000, 4096],
            'claude-3-sonnet-20240229': [200000, 4096],
            'claude-3-5-sonnet-20240620': [200000, 4096],
            'claude-3-haiku-20240307': [200000, 4096],
            'claude-3-5-haiku-20241022': [200000, 4096],
        })
        return limits

    @classmethod
    def compute_cost(cls, messages, output, input_tokens, output_tokens, model_name):
        prices = cls.prices_per_model().get(model_name)
        if not prices:
            if 'haiku' in model_name:
                prices = cls.prices_per_model().get('claude-3-haiku-20240307')
            elif 'sonnet' in model_name:
                prices = cls.prices_per_model().get('claude-3-sonnet-20240229')
            elif 'opus' in model_name:
                prices = cls.prices_per_model().get('claude-3-opus-20240229')

        multiplier = 1e6
        price_input, price_output = prices
        total_cost = input_tokens * price_input / multiplier + output_tokens * price_output / multiplier
        return total_cost
