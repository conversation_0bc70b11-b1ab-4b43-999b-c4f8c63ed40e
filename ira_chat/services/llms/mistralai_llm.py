import collections
import logging

from langchain_core.language_models import BaseChatModel
from langchain_mistralai import ChatMistralAI, MistralAIEmbeddings

from ira_chat.services.llms import base

logger = logging.getLogger(__name__)


class MistralAI(base.InitLLM):
    def _init_llm(self, params: dict) -> ChatMistralAI:
        # if self.need_vision:
        #     params['model_name'] = params.get('vision_model') if params.get('vision_model') else 'gemini-pro-vision'

        logger.info("INIT MISTRALAI")
        llm = ChatMistralAI(
            model=params.get('model_name') or 'mistral-large-latest',
            max_tokens=params['max_tokens'],
            max_retries=int(params.get('max_retries', self.default_max_retries)),
            temperature=float(params['temperature']),
            mistral_api_key=self.llm_params.get('mistral_api_key'),
            callbacks=self.get_callbacks()
        )
        return llm

    def init_streaming_llm(self, params: dict) -> BaseChatModel:
        stream_callback = base.StreamingCallbackHandler(params['stream_queue'], params['stream_event'])
        llm = self.init_llm(params)
        llm.streaming = True
        llm.callbacks.append(stream_callback)

        return llm

    def init_embeddings(self, override: dict = None):
        params = self.get_embedding_model_params(self.org_config)
        logger.info("INIT EMBEDDING MISTRALAI")
        if override:
            params.update(override)

        return MistralAIEmbeddings(
            mistral_api_key=params.get('mistral_api_key'),
            model=params.get('embedding_model', 'mistral-embed')
        )

    @staticmethod
    def available_models():
        return {
            'models': [
                'mistral-small-latest',
                'mistral-medium-latest',
                'mistral-large-latest',
                'codestral-latest',
                'pixtral-large-latest'
            ],
            'vision_models': [
                'pixtral-large-latest'
            ],
            'embedding_models': [
                'mistral-embed',
            ],
        }

    @staticmethod
    def prices_per_model() -> dict:
        # mistralai models, prices for 1M tokens
        prices = {
            'open-mistral-7b': [0.25, 0.25],
            'open-mixtral-8x7b': [0.7, 0.7],
            'mistral-small-latest': [1, 3],
            'mistral-medium-latest': [2.75, 8.1],
            'mistral-large-latest': [3, 9],
            'open-mistral-nemo': [0.3, 0.3],
        }
        return prices

    @staticmethod
    def token_limit_per_model():
        limits = collections.defaultdict(lambda: [32768, 4096])
        limits.update({
            'open-mistral-7b': [32768, 4096],
            'open-mixtral-8x7b': [32768, 4096],
            'mistral-small-latest': [32768, 4096],
            'mistral-medium-latest': [32768, 4096],
            'mistral-large-latest': [128000, 4096],
            'open-mistral-nemo': [128000, 4096],
        })
        return limits

    @classmethod
    def compute_cost(cls, messages, output, input_tokens, output_tokens, model_name):
        prices = cls.prices_per_model().get(model_name, [8, 24])
        multiplier = 1e6
        price_input, price_output = prices
        total_cost = input_tokens * price_input / multiplier + output_tokens * price_output / multiplier
        return total_cost
