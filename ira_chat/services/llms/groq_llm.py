import collections
import copy
import logging

from langchain_groq import ChatGroq
from langchain_core.language_models import BaseChatModel

from ira_chat.services.llms import base
from ira_chat.utils.utils import llm_model_name_by_llm_type, llm_vision_model_by_llm_type

logger = logging.getLogger(__name__)


class Groq(base.InitLLM):
    def __init__(self, org_config: dict, params: dict, org_id: int, workspace_id: int = None):
        super(Groq, self).__init__(org_config, params, org_id, workspace_id)

    def _init_llm(self, params: dict):
        if self.need_vision:
            params['model_name'] = params.get('vision_model') or llm_vision_model_by_llm_type(self.llm_type)
        logger.info("INIT GROQ")
        llm = ChatGroq(
            model=params.get('model_name') or llm_model_name_by_llm_type(self.llm_type),
            # max_tokens=params.get('max_tokens') or 4096,
            temperature=float(params['temperature']),
            groq_api_key=self.llm_params.get('groq_api_key'),
            callbacks=self.get_callbacks(),
            max_retries=self.default_max_retries,
        )
        return llm

    def init_streaming_llm(self, params: dict) -> BaseChatModel:
        stream_callback = base.StreamingCallbackHandler(params['stream_queue'], params['stream_event'])
        llm = self.init_llm(params)
        llm.streaming = True
        llm.callbacks.append(stream_callback)

        return llm

    def init_embeddings(self, override: dict = None):
        logger.info("INIT EMBEDDING GROQ")

        params = self.get_embedding_model_params(self.org_config)
        # Voyage / OpenAI

        embedding_provider = params.get('embedding_provider', 'openai')
        if embedding_provider == 'groq':
            raise ValueError('Embedding provider should be one of {openai, voyage}')
        org_config_copy = copy.deepcopy(self.org_config)
        org_config_copy['llm_type'] = embedding_provider
        if override:
            org_config_copy.update(override)
        # Make a recursion and initialize embedding model referring new llm_type
        from ira_chat.services.vectorstore import get_embedding_model

        return get_embedding_model(org_config_copy, self.org_id, self.workspace_id)

    @staticmethod
    def available_models():
        return {
            'models': [
                'gemma-7b-it',
                'gemma2-9b-it'
                'llama3-8b-8192',
                'llama3-70b-8192',
                'llama-3.1-405b-reasoning',
                'llama-3.1-70b-versatile',
                'llama-3.1-8b-instant',
                'llama-3.2-1b-preview',
                'llama-3.2-3b-preview',
                'llama-3.2-11b-vision-preview',
                'llama-3.2-90b-vision-preview',
                'mixtral-8x7b-32768',
                'deepseek-r1-distill-llama-70b'
            ],
            'vision_models': [
                'llama-3.2-11b-vision-preview',
                'llama-3.2-90b-vision-preview',
            ],
            'embedding_models': [

            ],
            'embedding_providers': ['openai', 'voyage'],
        }

    @staticmethod
    def prices_per_model() -> dict:
        # groq models, price for 1M tokens
        prices = {
            'gemma-7b-it': [0.07, 0.07],
            'gemma2-9b-it': [0.20, 0.20],
            'llama3-8b-8192': [0.05, 0.08],
            'llama3-70b-8192': [0.59, 0.79],
            'llama-3.1-405b-reasoning': [0.0, 0.0],
            'llama-3.1-70b-versatile': [0.59, 0.79],
            'llama-3.1-8b-instant': [0.05, 0.08],
            'llama-3.2-1b-preview': [0.0, 0.0],
            'llama-3.2-3b-preview': [0.0, 0.0],
            'llama-3.2-11b-vision-preview': [0.18, 0.18],
            'llama-3.2-90b-vision-preview': [0.9, 0.9],
            'mixtral-8x7b-32768': [0.24, 0.24],
            'deepseek-r1-distill-llama-70b': [0.59, 0.79],
        }
        return prices

    @staticmethod
    def token_limit_per_model():
        limits = collections.defaultdict(lambda: [8192, 4096])
        limits.update({
            'mixtral-8x7b-32768': [32768, 4096],
            'llama3-8b-8192': [8192, 4096],
            'llama3-70b-8192': [8192, 4096],
            'gemma-7b-it': [8192, 4096],
            'llama-3.1-405b-reasoning': [128000, 4096],
            'llama-3.1-70b-versatile': [128000, 4096],
            'llama-3.1-8b-instant': [128000, 4096],
            'gemma2-9b-it': [8192, 4096],
            'llama-3.2-1b-preview': [128000, 2048],
            'llama-3.2-3b-preview': [128000, 2048],
            'llama-3.2-11b-vision-preview': [128000, 2048],
            'llama-3.2-90b-vision-preview': [128000, 2048],
            'deepseek-r1-distill-llama-70b': [128000, 4096],
            # 'whisper-large-v3': [8192, 4096],
        })
        return limits

    @classmethod
    def compute_cost(cls, messages, output, input_tokens, output_tokens, model_name):
        prices = cls.prices_per_model().get(model_name)
        # if not prices:
        #     if 'haiku' in model_name:
        #         prices = cls.prices_per_model().get('claude-3-haiku-20240307')

        multiplier = 1e6
        price_input, price_output = prices
        total_cost = input_tokens * price_input / multiplier + output_tokens * price_output / multiplier
        return total_cost
