import logging
from typing import List

from langchain_voyageai.embeddings import VoyageAIEmbeddings
from langchain_core.messages import BaseMessage

from ira_chat.services.llms import base

logger = logging.getLogger(__name__)


class Voyage(base.InitLLM):
    provides_llm = False

    @classmethod
    def compute_cost(cls, messages: List[List[BaseMessage]], output, input_tokens, output_tokens, model_name):
        pass

    def init_llm(self, params: dict):
        raise NotImplementedError('Voyage does not have text model')

    def init_streaming_llm(self, params: dict):
        raise NotImplementedError('Voyage does not have text model')

    def init_embeddings(self, override: dict = None):
        params = self.get_embedding_model_params(self.org_config)
        if override:
            params.update(override)
        logger.info("INIT EMBEDDING VOYAGE")
        return VoyageAIEmbeddings(
            voyage_api_key=params.get('voyage_api_key'),
            model=params.get('embedding_model', 'voyage-3'),  # 1024 size, or voyage-3-lite / 512 size
            # request_timeout=60.0,
        )

    @staticmethod
    def available_models():
        return {
            'models': [],
            'vision_models': [],
            'embedding_models': [
                'voyage-3',
                'voyage-3-lite'
            ],
        }

    @staticmethod
    def token_limit_per_model():
        return {}
