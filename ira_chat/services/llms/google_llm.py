import collections
import logging
from typing import Union, Dict, Type, Any, List, Optional, Iterator, AsyncIterator

from langchain_core.callbacks import CallbackManagerForLLMRun, AsyncCallbackManagerForLLMRun
from langchain_core.language_models import BaseChatModel, LanguageModelInput
from langchain_core.messages import BaseMessage, message_chunk_to_message
from langchain_core.outputs import ChatResult, ChatGenerationChunk, ChatGeneration
from langchain_core.runnables import Runnable
from langchain_google_genai import ChatGoogleGenerativeAI, GoogleGenerativeAIEmbeddings
from pydantic import BaseModel

from ira_chat.services.llms import base
from ira_chat.utils.utils import llm_vision_model_by_llm_type, llm_model_name_by_llm_type

logger = logging.getLogger(__name__)


class Google(base.InitLLM):
    def _init_llm(self, params: dict) -> 'ChatGoogleGenerativeAIWithStreaming':
        if self.need_vision:
            params['model_name'] = params.get('vision_model', llm_vision_model_by_llm_type(self.llm_type))

        logger.info("INIT GOOGLE")
        model = params.get('model_name') or llm_model_name_by_llm_type(self.llm_type)
        model = f'models/{model}' if not model.startswith('models/') else model

        llm = ChatGoogleGenerativeAIWithStreaming(
            google_api_key=self.llm_params.get('gemini_api_key'),
            temperature=float(params.get('temperature') or self.default_temperature),
            model=model,
            max_output_tokens=params.get('max_output_tokens') or 4096,
            # convert_system_message_to_human=True,
            callbacks=self.get_callbacks(),
            max_retries=int(params.get('max_retries', self.default_max_retries)),
        )
        return llm

    def init_streaming_llm(self, params: dict) -> BaseChatModel:
        stream_callback = base.StreamingCallbackHandler(params['stream_queue'], params['stream_event'])
        llm = self.init_llm(params)
        llm.streaming = True
        llm.callbacks.append(stream_callback)

        return llm

    def init_embeddings(self, override: dict = None):
        params = self.get_embedding_model_params(self.org_config)
        logger.info("INIT EMBEDDING GOOGLE")
        if override:
            params.update(override)

        model = params.get('embedding_model') or 'embedding-001'
        model = f'models/{model}'

        return GoogleGenerativeAIEmbeddings(
            google_api_key=params.get('gemini_api_key'),
            model=model,
        )

    @staticmethod
    def available_models():
        return {
            'models': [
                'gemini-1.5-pro-latest',
                'gemini-1.5-flash-latest',
                'gemini-2.0-flash',
                'gemini-2.0-flash-lite',
                'gemini-2.5-flash-preview-04-17',
                'gemini-2.5-pro-preview-03-25',
            ],
            'vision_models': [
                'gemini-1.5-pro-latest',
                'gemini-1.5-flash-latest',
                'gemini-2.0-flash-001',
                'gemini-2.0-flash-lite',
                'gemini-2.5-flash-preview-04-17',
                'gemini-2.5-pro-preview-03-25',
            ],
            'embedding_models': [
                'embedding-001',  # size 768
                'gemini-embedding-exp-03-07',  # size 3072
                'text-embedding-004',  # size 768
            ],
        }

    @staticmethod
    def prices_per_model() -> dict:
        # gemini models, price for 1M characters
        prices = collections.defaultdict(lambda: [2.5, 7.5])
        prices.update({
            'gemini-pro': [0.125, 0.375],
            'gemini-1.0-pro': [0.125, 0.375],
            'gemini-1.0-pro-latest': [0.125, 0.375],
            'gemini-1.5-pro': [1.25, 2.5],
            'gemini-1.5-pro-latest': [1.25, 2.5],
            'gemini-pro-vision': [0.125, 0.375],
            'image': 0.0001315,  # per 1 image
            'gemini-1.0-pro-image': 0.0025,  # per 1 image
            'gemini-1.5-pro-image': 0.0001315,  # per 1 image
            'gemini-1.5-flash': [0.075, 0.375],
            'gemini-1.5-flash-latest': [0.075, 0.375],
            'gemini-2.0-flash': [0.1, 0.40],
            'gemini-2.0-flash-001': [0.1, 0.40],
            'gemini-2.0-flash-lite-preview-02-05': [0.075, 0.30],
            'gemini-2.0-flash-lite': [0.075, 0.30],
            'gemini-2.5-flash-preview-04-17': [0.15, 0.60],
            'gemini-2.5-pro-preview-03-25': [1.25, 10.0],
            'video': 0.002,  # per 1 second
        })
        return prices

    @staticmethod
    def token_limit_per_model():
        limits = collections.defaultdict(lambda: [1048576, 8192])
        limits.update({
            'gemini-pro': [30720, 2048],
            'gemini-1.0-pro': [30720, 2048],
            'gemini-1.0-pro-latest': [30720, 2048],
            'gemini-1.5-pro': [1048576, 8192],
            'gemini-1.5-pro-latest': [1048576, 8192],
            'gemini-1.5-flash': [1048576, 8192],
            'gemini-1.5-flash-latest': [1048576, 8192],
            'gemini-pro-vision': [12288, 4096],
            'gemini-2.0-flash-001': [1048576, 8192],
            'gemini-2.0-flash': [1048576, 8192],
            'gemini-2.0-flash-lite-preview-02-05': [1048576, 8192],
            'gemini-2.0-flash-lite': [1048576, 8192],
            'gemini-2.5-flash-preview-04-17': [1048576, 65536],
            'gemini-2.5-pro-preview-03-25': [1048576, 65536],
        })
        return limits

    @staticmethod
    def vision_models():
        return ['gemini-pro-vision']

    def token_limits(self, model_name: str = None):
        if model_name:
            model_name = model_name.removeprefix('models/')
        return super().token_limits(model_name)

    @classmethod
    def compute_cost(cls, messages, output, input_tokens, output_tokens, model_name):
        prices = cls.prices_per_model().get(model_name, [0.075, 0.375])
        price_input, price_output = prices
        if '1.5' in model_name:
            image_price = cls.prices_per_model()['gemini-1.5-pro-image']
        else:
            image_price = cls.prices_per_model()['gemini-1.0-pro-image']

        # Compute token count manually
        input_chars = 0
        images = 0
        messages = messages[0]
        for m in messages:
            if isinstance(m.content, str):
                input_chars += len(m.content)
            elif isinstance(m.content, list):
                for inner in m.content:
                    if isinstance(inner, dict):
                        if inner['type'] == 'text':
                            input_chars += len(inner['text'])
                        if inner['type'] == 'image':
                            images += 1

        output_chars = sum(len(m.text) for m in output)
        if model_name in cls.vision_models() and images == 0:
            images = 1

        image_cost = images * image_price
        text_cost = input_chars * price_input / 1e6 + output_chars * price_output / 1e6

        total_cost = image_cost + text_cost
        return total_cost


class ChatGoogleGenerativeAIWithStreaming(ChatGoogleGenerativeAI):
    streaming: bool = False

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        stream: Optional[bool] = None,
        **kwargs: Any,
    ) -> ChatResult:
        should_stream = stream if stream is not None else self.streaming
        nkw = kwargs.copy()
        nkw.pop('temperature', None)
        if should_stream:
            stream_iter = self._stream(
                messages, stop=stop, run_manager=run_manager, **nkw
            )

            return generate_from_stream(stream_iter)

        return super(ChatGoogleGenerativeAIWithStreaming, self)._generate(
            messages, stop, run_manager, **nkw
        )

    async def _agenerate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[AsyncCallbackManagerForLLMRun] = None,
        stream: Optional[bool] = None,
        **kwargs: Any,
    ) -> ChatResult:
        should_stream = stream if stream is not None else self.streaming
        nkw = kwargs.copy()
        nkw.pop('temperature', None)
        if should_stream:
            stream_iter = self._astream(
                messages=messages, stop=stop, run_manager=run_manager, **nkw
            )
            return await agenerate_from_stream(stream_iter)
        return await super(ChatGoogleGenerativeAIWithStreaming, self)._agenerate(
            messages, stop, run_manager, **nkw
        )


def generate_from_stream(stream: Iterator[ChatGenerationChunk]) -> ChatResult:
    """Generate from a stream."""

    generation: Optional[ChatGenerationChunk] = None
    for chunk in stream:
        if generation is None:
            generation = chunk
        else:
            generation += chunk
    assert generation is not None
    return ChatResult(
        generations=[
            ChatGeneration(
                message=message_chunk_to_message(generation.message),
                generation_info=generation.generation_info,
            )
        ]
    )


async def agenerate_from_stream(
    stream: AsyncIterator[ChatGenerationChunk],
) -> ChatResult:
    """Async generate from a stream."""

    generation: Optional[ChatGenerationChunk] = None
    async for chunk in stream:
        if generation is None:
            generation = chunk
        else:
            generation += chunk
    assert generation is not None
    return ChatResult(
        generations=[
            ChatGeneration(
                message=message_chunk_to_message(generation.message),
                generation_info=generation.generation_info,
            )
        ]
    )
