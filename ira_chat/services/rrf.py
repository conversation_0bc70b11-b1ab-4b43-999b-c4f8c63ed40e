import logging
from typing import List, Dict, Any

from langchain_core.documents import Document

logger = logging.getLogger(__name__)


def apply_rrf_reranking(
    documents: List[Document],
    rrf_k: int = 10,  # Using k=10 for more distinguishable scores
) -> List[Document]:
    """
    Apply Reciprocal Rank Fusion (RRF) to rerank documents based on initial order and graded scores.

    Args:
        documents: The list of documents that passed the grading threshold
        rrf_k: The constant k used in the RRF formula (default: 60)

    Returns:
        A list of documents reranked using the RRF algorithm
    """
    # Create initial ranking based on the order documents were returned from the retriever
    # This preserves the original vector similarity ranking
    initial_ranking = {}
    doc_key = '_id'
    scored_docs_sorted = sorted(documents, key=lambda d: d.metadata.get('score', 0), reverse=True)
    for i, doc in enumerate(scored_docs_sorted):
        initial_ranking[doc.metadata[doc_key]] = i + 1  # 1-based ranking

    # Create graded ranking based on the LLM-assigned scores (higher score = better rank)
    # Sort graded_docs by grade_score in descending order
    # for doc in documents:
    #     print(f"{doc.metadata['source']}: {doc.metadata['score']}, {doc.metadata['grade_score']}")
    graded_docs_sorted = sorted(documents, key=lambda d: d.metadata.get('grade_score', 0), reverse=True)
    graded_ranking = {}
    for i, doc in enumerate(graded_docs_sorted):
        graded_ranking[doc.metadata[doc_key]] = float(11 - doc.metadata.get('grade_score', 0) + i) / 2

    # Calculate RRF scores for each document
    rrf_scores = {}
    for doc in documents:
        doc_id = doc.metadata[doc_key]
        # RRF formula: 1/(k + r) where r is the rank and k is a constant
        initial_score = 1.0 / (rrf_k + initial_ranking.get(doc_id, len(documents) + 1))
        graded_score = 1.0 / (rrf_k + graded_ranking.get(doc_id, len(documents) + 1))
        # Combine scores
        rrf_scores[doc_id] = initial_score + graded_score
        # Store the RRF score in the document metadata for debugging
        doc.metadata['rrf_score'] = rrf_scores[doc_id]
        # doc.metadata['initial_rank'] = initial_ranking.get(doc_id, len(documents) + 1)
        # doc.metadata['graded_rank'] = graded_ranking.get(doc_id, len(documents) + 1)

    # Set RRF scores to be between min initial vector score and 1
    min_score = min(doc.metadata['score'] for doc in documents)
    max_score = 1
    for doc in documents:
        doc.metadata['rrf_score'] = min_score + (doc.metadata['rrf_score'] * (max_score - min_score))

    # Sort documents by RRF score (higher is better)
    reranked_docs = sorted(documents, key=lambda d: d.metadata.get('rrf_score', 0), reverse=True)

    logger.info(f"---RRF RERANKING: Reordered {len(reranked_docs)} documents---")
    for i, doc in enumerate(reranked_docs):
        logger.info(
            f"---RANK {i+1}: [name={doc.metadata['source']} "
            f"vector_score={doc.metadata.get('score'):.4f} "
            f"grade_score={doc.metadata.get('grade_score')} "
            f"rrf_score={doc.metadata.get('rrf_score'):.4f}]---"
        )

    return reranked_docs
