from typing import Sequence, Any, Iterable, List
import itertools

from langchain_core.documents import BaseDocumentTransformer, Document


class CorrectedAnswerChunker(BaseDocumentTransformer):
    """Chunker that splits documents into chunks based on a keyword.

    Example of data:
        Human: "Hello, how are you?"
        AI: "I'm fine, thank you!"

        Human: "How's the weather?"
        AI: "It's sunny today."

    """
    def __init__(self, keyword='Human: '):
        self.keyword = keyword

    def transform_documents(self, documents: Sequence[Document], **kwargs: Any) -> Sequence[Document]:
        # Transform flatten list of documents
        return self.split_documents(documents)

    def split_documents(self, documents: Iterable[Document]) -> List[Document]:
        return list(itertools.chain(*[self.transform(doc) for doc in documents]))

    def transform(self, doc: Document) -> list[Document]:
        meta = doc.metadata
        text = doc.page_content
        splitted = self.split_text(text)
        return [Document(page_content=s, metadata=meta, id=doc.id, type=doc.type) for s in splitted]

    def split_text(self, text: str) -> list[str]:
        splitted = text.split(self.keyword)
        chunks = []
        for t in splitted:
            if not t.strip():
                continue
            chunks.append(self.keyword + t)

        return chunks
