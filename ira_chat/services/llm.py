import logging
from typing import List

import tiktoken

from ira_chat.cache import function_cache
from ira_chat.db.models import ChatMessage
from ira_chat.services.llms.anthropic_llm import Anthropic
from ira_chat.services.llms.azure_openai_llm import AzureOpenAI
from ira_chat.services.llms.google_llm import Google
from ira_chat.services.llms.groq_llm import Groq
from ira_chat.services.llms.mistralai_llm import MistralAI
from ira_chat.services.llms.ollama_llm import OllamaLLM
from ira_chat.services.llms.openai_llm import OpenAI
from ira_chat.services.llms.voyage_embedding import Voyage

logger = logging.getLogger(__name__)
llms = {
    'anthropic': Anthropic,
    'azure_openai': AzureOpenAI,
    'gemini': Google,
    'groq': Groq,
    'mistralai': MistralAI,
    'ollama': OllamaLLM,
    'openai': OpenAI,
    'voyage': Voyage,
}

all_token_limits = {}
for init_llm in llms.values():
    all_token_limits.update(init_llm.token_limit_per_model())

# prices_config = utils.load_yaml(os.getenv("PRICES_CONFIG", "prices_config.yaml"))


def get_config_property(chat_config, name, default=None, lower=False):
    val = default
    if chat_config and chat_config.get(name):
        val = chat_config[name]
        if lower:
            val = val.lower()

    return val


def get_prompt_params(default_prompts: dict, chat_config=None) -> dict:
    prompt_config = chat_config.get('prompt_config', {})
    opts = {}
    for k, v in default_prompts.items():
        val = get_config_property(prompt_config, k, v)
        if val:
            opts[k] = val
    return opts


@function_cache.cached
def init_llm_class(org_config: dict, params: dict, org_id: int, workspace_id: int = None):
    llm_type = org_config.get('llm_type', 'openai')
    init_class = llms.get(llm_type)
    if init_class is None:
        raise TypeError(f'Unsupported llm_type {llm_type}')
    return init_class(org_config, org_id=org_id, workspace_id=workspace_id, params=params)


def optimize_messages_to_limit(
    messages: List[ChatMessage | tuple],
    limit: int,
    subtract_s: str = '',
    model_name: str = None,
) -> List[ChatMessage]:
    if len(messages) == 0:
        return messages

    if model_name is not None:
        try:
            enc = tiktoken.encoding_for_model(model_name)
        except KeyError:
            enc = tiktoken.get_encoding("cl100k_base")
    else:
        enc = tiktoken.get_encoding("cl100k_base")
    role_tokens = 3

    def get_tokens(s: str):
        if s is None or not s.strip():
            return []
        tokens = enc.encode(s)
        return tokens

    def split_by_ntokens(s, tokens, n):
        decoded = enc.decode(tokens[:n])
        return s[:len(decoded)]

    def content(msg):
        if isinstance(msg, tuple):
            return msg[1]
        return msg.content

    def set_content(msgs, i, content):
        if isinstance(msgs[0], tuple):
            msgs[i] = (msgs[i][0], content)
        else:
            msgs[i].content = content

    if subtract_s:
        limit -= len(get_tokens(subtract_s))

    last_tokens = get_tokens(content(messages[-1]))
    if len(last_tokens) + role_tokens > limit:
        # Only query
        messages[-1].content = split_by_ntokens(content(messages[-1]), last_tokens, limit)
        return [messages[-1]]
    if len(messages) > 1:
        tokens = get_tokens(content(messages[-1]))
        start_tokens = get_tokens(content(messages[0]))
        if len(start_tokens) + role_tokens + len(tokens) + role_tokens > limit:
            # Context + query
            remain = limit - len(tokens) - role_tokens
            messages[0].content = split_by_ntokens(content(messages[0]), start_tokens, remain)
            return [messages[0], messages[-1]]

    # No need to optimize
    if len(messages) <= 2:
        return messages

    # Context, history, query
    context = messages[0]
    query = messages[-1]
    context_tokens = get_tokens(content(context))
    query_tokens = get_tokens(content(query))
    remain = limit - len(context_tokens) - len(query_tokens)

    history = []
    for message in messages[-2:0:-1]:
        msg_tokens = get_tokens(content(message))
        if len(msg_tokens) + role_tokens >= remain:
            message.content = split_by_ntokens(content(message), msg_tokens, remain)
            history.append(message)
            break
        history.append(message)
        remain -= len(msg_tokens) + role_tokens

    return [context, *history[::-1], query]
