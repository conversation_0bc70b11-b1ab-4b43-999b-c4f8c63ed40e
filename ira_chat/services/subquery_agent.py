import asyncio
import logging
from typing import List

from langchain_core.language_models import BaseChatModel
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.retrievers import BaseRetriever
from langgraph.graph import StateGraph
from pydantic import BaseModel, Field

from ira_chat.services import agents
from ira_chat.services.agents import AgentState

logger = logging.getLogger(__name__)

# Define new paths for the subquery processing
PATH_SMART_QUERY_PLANNING = 'smart_query_planning'
PATH_SUBQUERY_EXECUTION = 'subquery_execution'
PATH_SUBQUERY_GRADING = 'subquery_grading'
PATH_RESULTS_COMBINATION = 'results_combination'


class SubQueries(BaseModel):
    """Generated subqueries for a complex query."""
    reasoning: str = Field(
        description="Reasoning behind the generated subqueries."
    )
    subqueries: List[str] = Field(
        description="List of subqueries to execute."
    )
    search_k_per_subquery: List[int] = Field(
        description="Number of documents to retrieve for each subquery, should be 8 or bigger."
    )


class QueryPlan(BaseModel):
    """Query plan for processing a complex query."""
    reasoning: str = Field(
        description="Reasoning behind the decision to decompose or not."
    )
    needs_decomposition: bool = Field(
        description="Whether the query needs to be decomposed into subqueries."
    )


class SmartQueryPlan(QueryPlan):
    """Smart query plan that combines planning and subquery generation."""
    search_k: int = Field(
        description="Number of documents to retrieve for the main query if not decomposed (4-12 based on complexity)."
    )
    subqueries: List[str] = Field(
        description="List of subqueries to execute if decomposition is needed."
    )
    search_k_per_subquery: List[int] = Field(
        description="Number of documents to retrieve for each subquery if decomposition is needed (3-7 per subquery)."
    )


def smart_query_planning_fn(llm: BaseChatModel, config: dict):
    """Create a function that analyzes a query and generates subqueries if needed in a single LLM call."""
    async def smart_query_planning(state: AgentState):
        logger.info("---SMART QUERY PLANNING---")
        question = state["question"]

        system_prompt = """You are an expert at analyzing complex user queries and determining when—and how—to break them into simpler sub-queries for more accurate retrieval and processing.

When a user query arrives, apply these rules in order:

1. Single-Topic vs Multi-Element  
   - **Single-Topic, Single-Step**: If the query truly involves only one discrete information need or action, leave it intact.  
   - **Multi-Element**: If the user is asking for multiple items of the same type (e.g. “What are the boiling points of water, ethanol, and acetone?”, “For each city—Tokyo, Paris, and Cairo—report the current temperature”), decompose into one sub-query per element.

2. Entity-Plus-Interaction Pattern (Strict Enforcement)
  Before generating any sub-query about an interaction, comparison, or relationship between two (or more) entities, you must first generate one focused sub-query per entity.

  a. One focused sub-query on Entity A alone.
  b. One focused sub-query on Entity B alone.
  c. Then, one explicit sub-query on the interaction/relationship between A and B.

  This applies even if the original query already structures its questions as “mechanism” or “action”—you still must fetch each entity’s standalone profile first.

  Example: “Interaction between X and Y” →
  - “Information on entity X (characteristics, context, etc.)”
  - “Information on entity Y (characteristics, context, etc.)”
  - “Interaction/relationship between entity X and entity Y”

3. Comparisons & Relationships  
   - Always decompose queries that explicitly ask for similarities, differences, or relationships between two or more entities (e.g. “differences between X and Y,” “what A and B have in common,” “how X affects Y”).
   - Follow the same strict three-step breakdown: one sub-query per entity, then the relationship sub-query.

4. Composite Scenario / Repeatable Operation  
   - If a narrative describes several discrete cases or data points that each require the same processing step (e.g. coding different burn depths & locations, calculating BMI for each patient in a list), break out each case into its own sub-query.

5. Enumerated Lists  
   - Detect when the user enumerates multiple items (e.g. “A, B, and C”) in service of the same ask. Treat each enumeration as its own query.

6. Complexity Threshold  
   - If a single query is so dense or convoluted that answering in one pass risks missing or conflating details, decompose to ensure clarity.

Only leave a query intact if none of the above criteria apply.

Examples of queries that should be decomposed:
- 'What do the iPhone 12 and iPhone 13 have in common, and how do they differ?'
- 'What are the differences between Python and JavaScript?'
- 'List the calories, protein, and fiber content of apples, bananas, and carrots.'
- 'How does daily exercise affect sleep quality and stress levels?'
- 'Calculate the BMI for a list of patients with the following height and weight pairs: (170 cm, 70 kg), (180 cm, 85 kg), (160 cm, 60 kg)'

**IMPORTANT**: If you decide the query needs decomposition, immediately generate 2-5 focused subqueries that together cover all aspects of the original query. Each subquery should:
- Focus on a specific aspect or subtopic of the original query
- Be clear and specific enough to retrieve relevant information
- Be formulated to maximize the chance of finding relevant documents
- Always apply Rule 2’s strict enforcement for any interaction/comparison/relationship request - no shortcuts.
- Only leave a query intact if none of the above criteria apply.

For comparison queries (asking about similarities, differences, or relationships):
- Create separate subqueries for each entity being compared / involved in the relationship
- Create an additional subquery specifically about their relationship/commonalities/differences/interaction
- Example: For 'What do A and B have in common?', create subqueries about A, about B, and about their commonalities
- Example: For 'How A and B interact?', create subqueries about A, about B, and about their interaction

**SMART SEARCH_K SELECTION**: Choose the optimal number of documents (search_k, minimum 3-4) based on query characteristics:

For SINGLE queries (not decomposed):
- Simple, specific questions (e.g., "What is X?", "How to do Y?"): 4-6 documents
- Medium complexity questions (e.g., "Explain the relationship between X and Y"): 6-8 documents
- Complex or broad questions (e.g., "Comprehensive analysis of X"): 8-12 documents

For SUBQUERIES (when decomposed):
- Focused entity queries (e.g., "Information about cats"): 4-5 documents
- Comparison/relationship queries (e.g., "What do X and Y have in common?"): 5-7 documents
- Technical/detailed queries: 8-10 documents
- Total budget: Aim for no more than 40 documents across all subqueries combined

Consider these factors:
- Query specificity: More specific = fewer documents needed
- Topic breadth: Broader topics = more documents needed
- Expected answer complexity: Complex answers = more documents needed
- Number of subqueries: More subqueries = fewer documents per subquery
"""

        human_prompt = (
            "Analyze this query and determine if it should be decomposed into subqueries. "
            "If decomposition is needed, generate the subqueries. Apply all the rules 1-6 mentioned earlier.\n\n"
            "Query: {question}\n"
        )
        generated_question = state.get("generated_question", question)
        if generated_question != question:
            human_prompt += f"\nRephrased query: {generated_question}"

        prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            ("human", human_prompt)
        ])

        formatted_prompt = await prompt.aformat_prompt(question=question)
        structured_llm = llm.with_structured_output(SmartQueryPlan)
        smart_plan: SmartQueryPlan = await structured_llm.ainvoke(formatted_prompt)

        logger.info(f"[SMART PLAN]: {smart_plan.reasoning}")
        logger.info(f"[SMART PLAN] Decompose: {smart_plan.needs_decomposition}")

        if smart_plan.needs_decomposition and smart_plan.subqueries:
            for i, (subquery, search_k) in enumerate(zip(smart_plan.subqueries, smart_plan.search_k_per_subquery)):
                logger.info(f"[SMART PLAN] Subquery {i+1} [search_k={search_k}]: '{subquery}'")

            # Store subqueries in the format expected by existing code
            return {
                "query_plan": {
                    "reasoning": smart_plan.reasoning,
                    "needs_decomposition": smart_plan.needs_decomposition,
                },
                "subqueries": {
                    "reasoning": smart_plan.reasoning,
                    "subqueries": smart_plan.subqueries,
                    "search_k_per_subquery": smart_plan.search_k_per_subquery
                }
            }
        else:
            # No decomposition needed
            return {
                "query_plan": {
                    "reasoning": smart_plan.reasoning,
                    "needs_decomposition": smart_plan.needs_decomposition,
                }
            }

    return smart_query_planning


def subquery_execution_fn(retriever: BaseRetriever, answers_retriever: BaseRetriever = None):
    """Create a function that executes subqueries and retrieves documents."""

    def deduplicate_docs(all_docs, use_id=True):
        """Helper function to deduplicate documents.

        Args:
            all_docs: List of documents to deduplicate
            use_id: If True, deduplicate by document ID, otherwise by content

        Returns:
            List of unique documents
        """
        unique_docs = []
        seen = set()

        for doc in all_docs:
            target_data_check = doc.metadata['_id'] if use_id else doc.page_content
            if target_data_check not in seen:
                seen.add(target_data_check)
                unique_docs.append(doc)

        logger.info(f"Total unique documents retrieved: {len(unique_docs)}")
        return unique_docs

    async def execute_retrieval_tasks(queries, search_k_values=None, add_subquery_metadata=False):
        """Execute retrieval tasks for one or more queries.

        Args:
            queries: List of queries or a single query string
            search_k_values: List of search_k values (one per query) or None to use defaults
            add_subquery_metadata: Whether to add subquery metadata to documents

        Returns:
            List of retrieved documents
        """
        # Normalize inputs to lists
        if isinstance(queries, str):
            queries = [queries]
            search_k_values = [None] * len(queries)
        elif search_k_values is None:
            search_k_values = [None] * len(queries)

        # Prepare all retrieval tasks
        all_tasks = []
        task_metadata = []  # Store metadata about each task

        # Add tasks for all queries
        for i, (query, search_k) in enumerate(zip(queries, search_k_values)):
            # Add answers retriever task if available
            if answers_retriever:
                answers_ret = answers_retriever
                if search_k is not None and hasattr(answers_ret, "search_kwargs"):
                    answers_ret = answers_ret.model_copy()
                    answers_ret.search_kwargs["k"] = search_k
                all_tasks.append(answers_ret.ainvoke(query))
                task_metadata.append({"retriever": "answers", "query_index": i})
                # logger.info(f"Prepared task for query {i + 1} with answers retriever: '{query}' with search_k={search_k or 'default'}")

            # Add main retriever task
            main_retriever = retriever
            if search_k is not None and hasattr(main_retriever, "search_kwargs"):
                main_retriever = main_retriever.model_copy()
                main_retriever.search_kwargs["k"] = search_k
            all_tasks.append(main_retriever.ainvoke(query))
            task_metadata.append({"retriever": "main", "query_index": i})
            # logger.info(f"Prepared task for query {i+1} with main retriever: '{query}' with search_k={search_k or 'default'}")

        all_results = await asyncio.gather(*all_tasks)

        # Process all results
        all_docs = []
        for docs, metadata in zip(all_results, task_metadata):
            i = metadata["query_index"]
            # retriever_type = metadata["retriever"]
            # logger.info(f"{retriever_type.capitalize()} retriever - Query {i+1} returned {len(docs)} documents")

            # Add metadata if requested
            if add_subquery_metadata:
                for doc in docs:
                    doc.metadata["subquery"] = queries[i]

            all_docs.extend(docs)

        return all_docs

    async def subquery_execution(state: AgentState):
        logger.info("---SUBQUERY EXECUTION---")

        # Check if we're using subqueries or the original query
        if state.get("query_plan", {}).get("needs_decomposition", False) and state.get("subqueries"):
            # Get subqueries and their search_k values
            subqueries = state["subqueries"]["subqueries"]
            search_k_values = state["subqueries"]["search_k_per_subquery"]
            # logger.info(f"Executing {len(subqueries)} subqueries")

            # Execute retrieval for all subqueries
            all_docs = await execute_retrieval_tasks(
                queries=subqueries,
                search_k_values=search_k_values,
                add_subquery_metadata=True
            )

            # Deduplicate documents by ID
            unique_docs = deduplicate_docs(all_docs, use_id=True)
        else:
            # Execute retrieval for the original query
            rephrased_query = state['generated_question']

            # Get the smart search_k from query plan
            query_plan = state.get("query_plan", {})
            search_k = query_plan.get("search_k")

            if search_k:
                logger.info(f"Executing single query with smart search_k={search_k}: '{rephrased_query}'")
            else:
                logger.info(f"Executing single query with default search_k: '{rephrased_query}'")

            # Execute retrieval for the single query with smart search_k
            all_docs = await execute_retrieval_tasks(rephrased_query, search_k_values=[search_k] if search_k else None)

            # Deduplicate documents by content
            unique_docs = deduplicate_docs(all_docs, use_id=True)

        # Return the unique documents
        return {
            "initial_documents": unique_docs,
            "source_documents": unique_docs,
        }

    return subquery_execution


def subquery_grading_fn(llm: BaseChatModel, config: dict):
    """Create a function that grades documents from each subquery."""
    async def subquery_grading(state: AgentState):
        logger.info("---SUBQUERY GRADING---")

        # Get the documents from subquery execution
        documents = state["initial_documents"]
        original_question = state["question"]
        gen_question = state.get("generated_question", original_question)

        if not documents:
            logger.info("No documents to grade")
            return {
                "source_documents": [],
                "not_answer_reason": "no_documents",
            }

        # Choose between batch grading and individual grading based on config
        use_batch_grading = config.get('batch_grading_enabled', True)
        if use_batch_grading:
            document_grader = agents.batch_grade_documents_fn(llm, config)
        else:
            document_grader = agents.grade_each_fn(llm, config)

        # Check if we're using subqueries
        if state.get("query_plan", {}).get("needs_decomposition", False) and state.get("subqueries"):
            # Group documents by subquery
            subquery_docs = {}
            for doc in documents:
                subquery = doc.metadata.get("subquery", "unknown")
                if subquery not in subquery_docs:
                    subquery_docs[subquery] = []
                subquery_docs[subquery].append(doc)

            all_graded_docs = []
            tasks = []
            subqueries = []

            for subquery, docs in subquery_docs.items():
                logger.info(f"Preparing to grade {len(docs)} documents for subquery: '{subquery}'")
                subqueries.append(subquery)

                # Create a temporary state with the subquery as the question
                temp_state = {
                    "question": subquery,  # Use the subquery instead of the original question
                    "generated_question": subquery,
                    "initial_documents": docs,
                }

                # Add the grading task to our list
                tasks.append(document_grader(temp_state))

            grading_results = await asyncio.gather(*tasks)

            # Process the results
            for i, (subquery, result) in enumerate(zip(subqueries, grading_results)):
                subquery_graded_docs = result.get("source_documents", [])
                logger.info(f"Subquery '{subquery}' has {len(subquery_graded_docs)} relevant documents after grading")
                all_graded_docs.extend(subquery_graded_docs)

            logger.info(f"Total documents after grading: {len(all_graded_docs)} out of {len(documents)}")

            return {
                "source_documents": all_graded_docs,
                "not_answer_reason": "no_relevant_docs" if not all_graded_docs else None,
            }
        else:
            # For non-decomposed queries, use the original grading approach
            # Create a temporary state with just the documents we need to grade
            temp_state = {
                "question": original_question,
                "generated_question": gen_question,
                "initial_documents": documents,
            }

            # Grade the documents
            grading_result = await document_grader(temp_state)
            graded_docs = grading_result.get("source_documents", [])

            logger.info(f"Total documents after grading: {len(graded_docs)} out of {len(documents)}")

            return {
                "source_documents": graded_docs,
                "not_answer_reason": grading_result.get("not_answer_reason", "no_relevant_docs"),
            }

    return subquery_grading


def results_combination_fn(llm: BaseChatModel):
    """Create a function that combines results from subqueries into a coherent summary."""
    async def results_combination(state: AgentState):
        logger.info("---RESULTS COMBINATION---")

        # Only perform combination if we used subqueries and have documents that passed grading
        if state.get("query_plan", {}).get("needs_decomposition", False) and state.get("subqueries") and state.get("source_documents"):
            question = state["question"]
            documents = state["source_documents"]

            # If no documents passed grading, return early
            if not documents:
                logger.info("No documents passed grading, skipping combination")
                return {}

            # Group documents by subquery
            subquery_docs = {}
            for doc in documents:
                subquery = doc.metadata.get("subquery", "unknown")
                if subquery not in subquery_docs:
                    subquery_docs[subquery] = []
                subquery_docs[subquery].append(doc)

            system_prompt = (
                "You are an expert at synthesizing information from multiple sources. "
                "You have documents retrieved from multiple subqueries related to the original query. "
                "Your task is to analyze these documents and create a comprehensive summary that "
                "addresses all aspects of the original query."
            )

            human_prompt = (
                "Original query: {question}\n\n"
                "Documents retrieved from subqueries:\n\n{documents_by_subquery}\n\n"
                "Create a comprehensive summary that addresses the original query based on these documents."
            )

            # Format documents by subquery
            documents_by_subquery = ""
            for subquery, docs in subquery_docs.items():
                documents_by_subquery += f"Subquery: {subquery}\n"
                for i, doc in enumerate(docs):
                    documents_by_subquery += f"Document {i+1}:\n{doc.page_content}\n\n"
                documents_by_subquery += "---\n\n"

            prompt = ChatPromptTemplate.from_messages([
                ("system", system_prompt),
                ("human", human_prompt)
            ])

            formatted_prompt = await prompt.aformat_prompt(
                question=question,
                documents_by_subquery=documents_by_subquery
            )

            response = await llm.ainvoke(formatted_prompt)

            return {
                "combined_summary": response.content,
            }
        else:
            # No combination needed if we didn't use subqueries or have no documents
            return {}

    return results_combination


def decide_after_combination(state: AgentState) -> str:
    """Decide whether to go to generate or no_answer after grading."""
    # If we have documents, go to generate
    if state.get("source_documents"):
        return "generate_or_output_check"
    # Otherwise, go to no_answer
    else:
        return agents.PATH_NO_ANSWER


def setup_subquery_processing(
    workflow: StateGraph,
    retriever: BaseRetriever,
    cheap_fast_llm: BaseChatModel,
    streaming_llm: BaseChatModel,
    config: dict = None
):
    """Setup the subquery processing nodes and edges in the workflow.

    This version uses smart query planning that combines planning and subquery generation
    into a single LLM call, saving 1 LLM call per query compared to the original implementation.
    """
    # Get answers_retriever from config if available
    answers_retriever = config.get('answers_retriever')

    # Add nodes for optimized subquery processing
    # Use smart query planning instead of separate planning and generation steps
    workflow.add_node(PATH_SMART_QUERY_PLANNING, smart_query_planning_fn(cheap_fast_llm, config))
    workflow.add_node(PATH_SUBQUERY_EXECUTION, subquery_execution_fn(retriever, answers_retriever))
    workflow.add_node(PATH_SUBQUERY_GRADING, subquery_grading_fn(cheap_fast_llm, config))
    workflow.add_node(agents.PATH_RERANK_DOCS, agents.rerank_docs_fn(config))
    workflow.add_node(PATH_RESULTS_COMBINATION, results_combination_fn(cheap_fast_llm))

    # Add PATH_CLARIFY node for handling unclear questions
    workflow.add_node(agents.PATH_CLARIFY, agents.clarify_question_fn(streaming_llm, config))
    workflow.add_edge(agents.PATH_CLARIFY, agents.END)

    # Add a passthrough node for routing after results combination
    async def passthrough_fn(state: AgentState):
        # This is just a passthrough function that doesn't modify the state
        return {}

    # Add the generate_or_output_check node to the workflow
    workflow.add_node("generate_or_output_check", passthrough_fn)

    # Add edges for optimized subquery processing
    # Smart planning goes directly to execution (subqueries are generated in the same call)
    workflow.add_edge(PATH_SMART_QUERY_PLANNING, PATH_SUBQUERY_EXECUTION)

    # Connect execution directly to grading
    workflow.add_edge(PATH_SUBQUERY_EXECUTION, PATH_SUBQUERY_GRADING)
    workflow.add_edge(PATH_SUBQUERY_GRADING, agents.PATH_RERANK_DOCS)

    # Skip results combination and connect grading directly to generate_or_output_check or no_answer
    workflow.add_conditional_edges(
        agents.PATH_RERANK_DOCS,
        decide_after_combination,
        {
            "generate_or_output_check": "generate_or_output_check",
            agents.PATH_NO_ANSWER: agents.PATH_NO_ANSWER
        }
    )

    # Note: We're keeping the results_combination node in the workflow but not connecting it
    # This allows us to easily re-enable it in the future if needed

    return workflow


def get_subquery_graph(
    retriever,
    llm: BaseChatModel,
    chat_prompt: ChatPromptTemplate,
    streaming_llm: BaseChatModel | None = None,
    config: dict = None,
):
    """Create a workflow graph for the subquery agent.

    This extends the standard agent graph with optimized subquery processing capabilities.
    Uses smart query planning that combines planning and subquery generation into a single LLM call,
    saving 1 LLM call per query compared to the original implementation.

    Args:
        retriever: The retriever to use for document retrieval
        llm: The language model to use
        chat_prompt: The prompt template for chat generation
        streaming_llm: Optional streaming language model
        config: Configuration dictionary

    Returns:
        A compiled workflow graph
    """
    workflow = StateGraph(AgentState)
    main_llm = streaming_llm or llm

    # Setup LLM models
    cheap_fast_llm, tool_llm = agents.setup_llm_models(llm, config)

    # Setup input guardrails
    input_rail_enabled = agents.setup_input_guardrails(workflow, cheap_fast_llm, main_llm, config)

    # Add nodes for document processing
    workflow.add_node(agents.PATH_GENERATE, agents.generate_fn(main_llm, chat_prompt))

    # Setup optimized subquery processing (this replaces the standard vectorstore retrieval)
    setup_subquery_processing(workflow, retriever, cheap_fast_llm, main_llm, config)

    # Setup output processing
    output_check_enabled = config.get('output_check_enabled', False)
    output_rail_enabled = config.get('output_rail_enabled', False)

    start_node = agents.setup_caching(workflow, config)

    # Setup advanced routing with PATH_SMART_QUERY_PLANNING as the default next node
    agents.setup_advanced_routing(
        workflow, tool_llm, config,
        default_start_node=start_node,
        default_next_node=PATH_SMART_QUERY_PLANNING,
        input_rail_enabled=input_rail_enabled
    )

    # Always connect generate_or_output_check to generate first
    workflow.add_edge("generate_or_output_check", agents.PATH_GENERATE)

    # Setup output processing if needed
    if streaming_llm is None and (output_check_enabled or output_rail_enabled):
        # If output processing is enabled, set up the nodes
        # This will connect PATH_GENERATE to output_check if output_check_enabled is True
        last_node = agents.setup_output_processing(workflow, cheap_fast_llm, llm, agents.PATH_GENERATE, config)
    else:
        # If no output processing, generate is the last node
        last_node = agents.PATH_GENERATE

    return agents.compile_graph(workflow, last_node)
