import uuid
from itertools import islice
from typing import Iterable, Optional, List, Sequence, Any, Generator

from langchain_qdrant import QdrantVectorStore
from qdrant_client.http.models import models

from ira_chat.utils import llm_utils


class QdrantVectorStoreEmbedMetadata(QdrantVectorStore):
    """
    QdrantVectorStore that embeds metadata for vectors but does not store it in payload.

    Details:
    This class extends QdrantVectorStore to include document metadata in the text that gets embedded,
    which allows the vector search to consider both the document content and its metadata when
    calculating similarity. This can improve search relevance by making metadata fields part of
    the semantic search space.

    The class prepends metadata key-value pairs to the document text before embedding, but
    excludes certain metadata keys (defined in exclude_metadata_keys_in_embedding) that shouldn't
    influence the embedding.

    When adding documents to the vector store:
    1. For each document, relevant metadata fields are extracted (excluding those in exclude_metadata_keys_in_embedding)
    2. These metadata fields are formatted as "key: value" pairs and prepended to the document text
    3. The combined text (metadata + content) is embedded, creating vectors that represent both
       content and metadata semantics
    4. The original metadata is still stored separately in the payload for retrieval

    This approach allows for semantic search that considers metadata context without modifying
    how metadata is stored or retrieved.
    """

    def _generate_batches(
        self,
        texts: Iterable[str],
        metadatas: Optional[List[dict]] = None,
        ids: Optional[Sequence[str | int]] = None,
        batch_size: int = 64,
    ) -> Generator[tuple[list[str | int], list[models.PointStruct]], Any, None]:
        texts_iterator = iter(texts)
        metadatas_iterator = iter(metadatas or [])
        ids_iterator = iter(ids or [uuid.uuid4().hex for _ in iter(texts)])

        while batch_texts := list(islice(texts_iterator, batch_size)):
            batch_metadatas = list(islice(metadatas_iterator, batch_size)) or None
            batch_ids = list(islice(ids_iterator, batch_size))

            # Include metadata in batch texts
            batch_for_embedding = batch_texts.copy()
            for i, metadata in enumerate(batch_metadatas):
                batch_for_embedding[i] = format_doc(batch_for_embedding[i], metadata)
            # End include metadata in batch texts

            points = [
                models.PointStruct(
                    id=point_id,
                    vector=vector,
                    payload=payload,
                )
                for point_id, vector, payload in zip(
                    batch_ids,
                    self._build_vectors(batch_for_embedding),
                    # self._build_vectors(batch_texts),
                    self._build_payloads(
                        batch_texts,
                        batch_metadatas,
                        self.content_payload_key,
                        self.metadata_payload_key,
                    ),
                )
            ]

            yield batch_ids, points


def format_doc(text: str, metadata: dict, include_only: list = None) -> str:
    """Format document to be added to vectorstore."""
    if not metadata:
        return text
    meta_copy = metadata.copy()
    for k in llm_utils.exclude_metadata_keys_in_embedding:
        meta_copy.pop(k, None)

    source = meta_copy.pop('source', None)
    formatted_metadata = f'source: {source}\n' if source else ''

    for k, v in meta_copy.items():
        if include_only and k not in include_only:
            continue
        if isinstance(v, list):
            meta_copy[k] = ', '.join(v)
        if isinstance(v, dict):
            meta_copy[k] = '\n' + '\n'.join([f'  {s}' for s in format_doc('', v).split('\n')])

    formatted_metadata += "\n".join([f"{k}: {v}" for k, v in meta_copy.items() if v not in [None, '']])
    if not text:
        return formatted_metadata
    return f"{formatted_metadata}\n\n{text}"
