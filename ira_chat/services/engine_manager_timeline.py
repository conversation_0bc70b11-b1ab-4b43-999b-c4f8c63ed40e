import asyncio
import json
import logging

from ira_chat.db import api as db_api
from ira_chat.db import models
from ira_chat.db import base
from ira_chat.services import engine_manager
from ira_chat.utils import json_utils, utils

logger = logging.getLogger(__name__)


class TimelineEngineManager(engine_manager.EngineManager):

    async def process_point_result(
        self,
        extractors: list[models.Extractor],
        extractor_results_in_progress: list[models.ExtractorResult],
        points: list[models.TimelinePoint],
        point: models.TimelinePoint
    ):
        logger.info(f"[TIMELINE] Start processing timeline point [id={point.id}, name={point.title}]")
        # Run results watcher: watch for extractor results, propagate errors to timeline result
        extractors_ok = await self.wait_extractor_results(self.ws, extractor_results_in_progress, point)
        if not extractors_ok:
            return

        extractor_map = {e.id: e for e in extractors}

        prev_result = None
        async with base.session_context():
            for point in points:
                prev_result = await self.process_single_point_result(point, extractor_map[point.extractor_id], prev_result)

        final_result = prev_result
        logger.info(f"[TIMELINE] Final output point [id={point.id}, name={point.title}]: {final_result}")

        tl_update = {'status': utils.build_status(models.STATUS_SUCCESS)}
        points = await db_api.list_timeline_points(timeline_id=point.timeline_id)

        no_updates = all(p.status['status'] == models.STATUS_SUCCESS for p in points)
        still_processing = any(p.status['status'] == models.STATUS_PROCESSING for p in points)
        tl_update['has_updates'] = not no_updates
        if still_processing:
            tl_update['status'] = utils.build_status(models.STATUS_PROCESSING)

        await db_api.update_timeline(models.Timeline(id=point.timeline_id), tl_update)

    async def process_single_point_result(self, point: models.TimelinePoint, extractor: models.Extractor,
                                          prev_result: dict | None) -> dict:
        logger.info(f"[POINT] Processing timeline point [id={point.id}, name={point.title}]")

        extractor_result = await db_api.get_last_extractor_result(point.extractor_id)
        if not prev_result and not extractor_result:
            # Get schema with all null values
            result = json_utils.generate_dummy_json(extractor.to_dict()['data_schema'])
        elif not prev_result:
            output = extractor_result.output
            result = json.loads(output) if output else json_utils.generate_dummy_json(extractor.to_dict()['data_schema'])
        elif not extractor_result:
            result = prev_result
        else:
            result = json_utils.merge_jsons([prev_result, json.loads(extractor_result.output)])

        # Apply all changes in order
        changes = await db_api.list_timeline_point_changes(timeline_id=point.timeline_id, point_id=point.id)
        for change in changes:
            if not change.diff:
                # Re-compute diff where needed
                change.diff = json.dumps(json_utils.diff_json(result, json.loads(change.data)))
                await db_api.update_timeline_point_change(change, {'diff': change.diff})

            result = json_utils.patch_json(result, json.loads(change.diff))

        await db_api.update_timeline_point(
            point,
            {'output': json.dumps(result), 'status': utils.build_status(models.STATUS_SUCCESS)}
        )
        return result

    async def wait_extractor_results(self, ws: models.Workspace, extractor_results: list, point: models.TimelinePoint):
        success = True
        if extractor_results:
            # wait for extractor result completion
            end = False
            overall_status = None
            while not end:
                extractor_results = await db_api.list_extractor_results(workspace_id=ws.id,
                                                                        ids=[r.id for r in extractor_results])
                statuses = [r.to_dict()['status'] for r in extractor_results]
                if any([s['status'] == models.STATUS_ERROR for s in statuses]):
                    # propagate error; end
                    for s in statuses:
                        if s['status'] == models.STATUS_ERROR:
                            overall_status = s
                            overall_status['message'] = overall_status['message']

                    logger.error(f"[TIMELINE] Error while processing timeline point: {overall_status}")
                    break
                if all([s['status'] == models.STATUS_SUCCESS for s in statuses]):
                    logger.info(f"[TIMELINE] All needed timeline points completed, status: {models.STATUS_SUCCESS}")
                    break

                await asyncio.sleep(2)

            if overall_status and overall_status['status'] == models.STATUS_ERROR:
                logger.error(f"[TIMELINE] Abort further processing.")
                update = {'status': overall_status}
                await db_api.update_timeline_point(models.TimelinePoint(id=point.id), update)
                await db_api.update_timeline(models.Timeline(id=point.timeline_id), update)
                success = False

        return success
