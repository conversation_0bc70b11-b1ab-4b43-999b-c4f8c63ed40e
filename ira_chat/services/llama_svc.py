import copy
import logging
import os
from typing import Optional, List, Tuple

import llama_index
from llama_index import OpenAIEmbedding, QueryBundle
# from llama_index import SummaryIndex
from llama_index import ServiceContext
from llama_index import VectorStoreIndex
from llama_index import chat_engine
from llama_index.callbacks import trace_method
from llama_index.chat_engine import condense_question
from llama_index.chat_engine.types import Chat<PERSON>ode, BaseChatEngine, AgentChatResponse
from llama_index.indices.base import BaseIndex
from llama_index.llms import OpenAI, ChatMessage, MessageRole
from llama_index.schema import Document, NodeWithScore, MetadataMode
from llama_index.tools import ToolOutput
from llama_index.vector_stores import DocArrayInMemoryVectorStore, PineconeVectorStore
from llama_index.vector_stores.types import VectorStore

from ira_chat.db import models
from ira_chat.services import file_formats, llm as ira_llm
from ira_chat.utils import utils

logger = logging.getLogger(__name__)


def get_vectorstore(text_chunks):
    return DocArrayInMemoryVectorStore()


def get_service_context(embedding_model, chat_config=None):
    params = {
        'timeout': 60,
        'temperature': 0.0,
        'model_name': utils.llm_model_name()
    }
    for k in params:
        if chat_config and k in chat_config:
            params[k] = chat_config[k]

    llm = OpenAI(
        temperature=float(params['temperature']), model=params['model_name'],
        max_tokens=completion_limit, additional_kwargs={'timeout': int(params['timeout'])},
    )
    return ServiceContext.from_defaults(
        llm=llm,
        embed_model=embedding_model,
        # prompt_helper=PromptHelper(context_window=max_token_limit, chunk_size_limit=1000),
        # chunk_size=1000,
    ), {'max_tokens': completion_limit, 'messages_limit': messages_limit}


# noinspection PyProtectedMember
def copy_vectorstore(store: VectorStore, namespace: str):
    if isinstance(store, PineconeVectorStore):
        idx = store._pinecone_index
        store._pinecone_index = None
        copied = copy.deepcopy(store)
        copied._pinecone_index = idx
        store._pinecone_index = idx
        store.namespace = namespace
        return copied
    return copy.deepcopy(store)


def get_chat_mode(chat_config=None):
    return ira_llm.get_config_property(chat_config, 'mode', 'context', lower=True)


def get_default_prompts(chat_config=None) -> dict:
    mode = get_chat_mode(chat_config)
    if mode == 'context':
        from llama_index.chat_engine import context
        return {
            'system_prompt': '',
            'context_template': context.DEFAULT_CONTEXT_TEMPLATE,
        }
    else:
        # Condense question
        return {
            'system_prompt': '',
            'condense_question_prompt': condense_question.DEFAULT_TEMPLATE,
        }


def get_chat_engine(service_context, index: BaseIndex,
                    search_kwargs=None, ws_name='', chat_config=None, token_limits=None) -> BaseChatEngine:
    # llm = HuggingFaceHub(repo_id="google/flan-t5-xxl", model_kwargs={"temperature":0.5, "max_length":512})

    # chat_engine = OpenAIAgent.from_tools(
    #     tools=[query_engine_tool],
    #     llm=service_context.llm,
    # )
    mode = get_chat_mode(chat_config)
    params = ira_llm.get_prompt_params(get_default_prompts(chat_config), chat_config)

    if mode == 'context':
        from llama_index.chat_engine import ContextChatEngine

        # pass all the params as kwargs
        chat_engine = IraContextChatEngine.from_defaults(
            retriever=index.as_retriever(service_context=service_context, similarity_top_k=search_kwargs.get('k', 2)),
            service_context=service_context,
            **params,
        )
        if token_limits:
            chat_engine.force_token_limit = token_limits['max_tokens']
    else:
        # pass condense_question_prompt as PromptTemplate
        if 'condense_question_prompt' in params:
            params['condense_question_prompt'] = llama_index.PromptTemplate(params['condense_question_prompt'])

        chat_engine = index.as_chat_engine(
            chat_mode=ChatMode.CONDENSE_PLUS_CONTEXT,
            service_context=service_context,
            **params,
            verbose=os.environ.get('DEBUG_PROMPT', 'false') == 'true',

        )

    # chat_engine = index.as_chat_engine(chat_mode=ChatMode.BEST, verbose=True)

    return chat_engine


def convert_to_history(messages: list[models.ChatMessage], max_limit=1000):
    history = []
    remains = max_limit
    for message in messages[::-1]:
        role = ''
        if message.role == models.ROLE_USER:
            role = MessageRole.USER
        elif message.role == models.ROLE_AI:
            role = MessageRole.ASSISTANT

        content = message.content
        if remains <= 0:
            history.append(ChatMessage(role=role, content=''))
            break
        new_remains = remains - len(content)
        if new_remains <= 0:
            content = content[:remains]
            history.append(ChatMessage(role=role, content=content))
        else:
            remains -= len(content)
            history.append(ChatMessage(role=role, content=content))

    return history[::-1]


# def trulens_wrapper(query_engine, random_id: str) -> TruLlama:
#     oai_feedback = feedback.OpenAI()
#     hug = feedback.Huggingface()
#
#     feedbacks = [
#         Feedback(oai_feedback.relevance, name=models.METRIC_RELEVANCE).on_input_output(),
#         Feedback(oai_feedback.correctness, name=models.METRIC_CORRECTNESS).on_output(),
#         # Feedback(oai_feedback.coherence, name=models.METRIC_COHERENCE).on_output(),
#         # Feedback(oai_feedback.helpfulness, name=models.METRIC_HELPFULNESS).on_output(),
#         Feedback(hug.positive_sentiment, name=models.METRIC_SENTIMENT).on_input(),
#         Feedback(hug.language_match, name=models.METRIC_LANGUAGE_MATCH).on_input_output(),
#     ]
#     tru_llama = TruLlamaWrapper(
#         query_engine,
#         app_id=random_id,
#         feedbacks=feedbacks,
#         tags="prototype",
#         # db=trulens_db.IraChatDB(),
#         tru=trulens_db.LocalTru(),
#         feedback_mode=FeedbackMode.WITH_APP_THREAD,
#     )
#     # tru.get_records_and_feedback()
#     return tru_llama


# class TruLlamaWrapper(TruLlama):
#     def main_input(
#         self, func: Callable, sig: Signature, bindings: BoundArguments
#     ) -> str:
#         """
#         Determine the main input string for the given function `func` with
#         signature `sig` if it is to be called with the given bindings
#         `bindings`.
#         """
#
#         if 'str_or_query_bundle' in bindings.arguments:
#             # llama_index specific
#             return bindings.arguments['str_or_query_bundle']
#
#         elif 'message' in bindings.arguments:
#             # llama_index specific
#             return bindings.arguments['message']
#
#         else:
#             if hasattr(bindings, 'kwargs') and bindings.kwargs:
#                 if 'message' in bindings.kwargs:
#                     return bindings.kwargs['message']
#                 if 'str_or_query_bundle' in bindings.kwargs:
#                     return bindings.kwargs['str_or_query_bundle']
#
#             if 'args' in bindings.arguments:
#                 if isinstance(bindings.arguments['args'], tuple):
#                     if isinstance(bindings.arguments['args'][0], str):
#                         return bindings.arguments['args'][0]
#
#             return App.main_input(self, func, sig, bindings)


class IraContextChatEngine(chat_engine.ContextChatEngine):
    force_token_limit: Optional[int]

    @trace_method("chat")
    def chat(
        self, message: str, chat_history: Optional[List[ChatMessage]] = None
    ) -> AgentChatResponse:
        if chat_history is not None:
            self._memory.set(chat_history)
        self._memory.put(ChatMessage(content=message, role="user"))

        context_str_template, nodes = self._generate_context(message)
        prefix_messages = self._get_prefix_messages_with_context(context_str_template)
        # New section
        # initial_token_count = len(
        #     self._memory.tokenizer_fn(
        #         " ".join([(m.content or "") for m in prefix_messages])
        #     )
        # )
        all_messages = prefix_messages + self._memory.get()

        all_token_count = sum([len(m.content or "") for m in all_messages])

        # for i, m in enumerate(all_messages):
        #     print(f'{i} MESSAGE. {m.content}')
        if self.force_token_limit and all_token_count > self.force_token_limit:
            all_messages = ira_llm.optimize_messages_to_limit(all_messages, self.force_token_limit)
        # for i, m in enumerate(all_messages):
        #     print(f'{i} MESSAGE. {m.content}')
        # New section end

        chat_response = self._llm.chat(all_messages)
        ai_message = chat_response.message
        self._memory.put(ai_message)

        return AgentChatResponse(
            response=str(chat_response.message.content),
            sources=[
                ToolOutput(
                    tool_name="retriever",
                    content=str(prefix_messages[0]),
                    raw_input={"message": message},
                    raw_output=prefix_messages[0],
                )
            ],
            source_nodes=nodes,
        )

    def _generate_context(self, message: str) -> Tuple[str, List[NodeWithScore]]:
        """Generate context information from a message."""
        nodes = self._retriever.retrieve(message)
        for postprocessor in self._node_postprocessors:
            nodes = postprocessor.postprocess_nodes(
                nodes, query_bundle=QueryBundle(message)
            )

        context_str = "\n\n".join(
            [n.node.get_content(metadata_mode=MetadataMode.LLM).strip() for n in nodes]
        )

        return self._context_template.format(context_str=context_str), nodes
