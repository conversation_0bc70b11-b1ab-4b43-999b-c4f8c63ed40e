from typing import Union, Optional
import os
import asyncio
import base64
import hmac
import json
import hashlib
import httpx
import logging
import time

from pydantic import BaseModel

from ira_chat.db import models
from ira_chat.db import api as db_api

kibernetika_header = 'X-Kibernetika-Hmac-Sha256'
logger = logging.getLogger(__name__)

# Configuration for HTTP client
WEBHOOK_TIMEOUT = int(os.environ.get('WEBHOOK_TIMEOUT', 30))  # 30 seconds default
WEBHOOK_CONNECT_TIMEOUT = int(os.environ.get('WEBHOOK_CONNECT_TIMEOUT', 10))  # 10 seconds default


def create_http_client() -> httpx.AsyncClient:
    """Create a new HTTP client configured for webhook requests."""
    timeout = httpx.Timeout(
        connect=WEBHOOK_CONNECT_TIMEOUT,
        read=WEBHOOK_TIMEOUT,
        write=WEBHOOK_TIMEOUT,
        pool=5.0  # 5 seconds to get connection from pool
    )

    return httpx.AsyncClient(
        timeout=timeout,
        # follow_redirects=False,  # Don't follow redirects for security
        # verify=True,  # Verify SSL certificates
    )


async def cleanup_webhook_client():
    """Public function for compatibility - no longer needed with per-request clients."""
    # This function is kept for compatibility but does nothing since we no longer use global clients
    pass

WEBHOOK_MESSAGE = 'chat_message'
WEBHOOK_FILE = 'workspace_file'
_hook_structure = {
    "type": WEBHOOK_MESSAGE,
    "status": models.STATUS_SUCCESS,
    "data": {}
}


class WebhookOrgConfig(BaseModel):
    all: Optional[bool] = True


class WebhookWorkspaceConfig(BaseModel):
    all: Optional[bool] = True
    ids: Optional[list[int]] = None
    application_types: Optional[list[str]] = None


class WebhookDatasetConfig(BaseModel):
    all: Optional[bool] = True


class WebhookConfig(BaseModel):
    organization: Optional[WebhookOrgConfig] = WebhookOrgConfig(all=True)
    workspace: Optional[WebhookWorkspaceConfig] = WebhookWorkspaceConfig(all=True)
    dataset: Optional[WebhookDatasetConfig] = WebhookDatasetConfig(all=True)


def check_webhook_sender(
    webhook: models.OrganizationWebhook,
    sender: Union[models.Workspace, models.Organization],
) -> bool:
    config = WebhookConfig(**webhook.config)

    if isinstance(sender, models.Organization):
        return config.organization.all
    elif isinstance(sender, models.Workspace):
        if config.workspace.all:
            return True
        else:
            return config.workspace.ids and sender.id in config.workspace.ids
    else:
        return False


async def send_webhook(
    webhook: models.OrganizationWebhook,
    sender: Union[models.Workspace, models.Organization],
    data: Union[dict, list]
):
    t1 = time.time()
    to_send = check_webhook_sender(webhook, sender)
    if not to_send:
        return
    logger.info(f'[WEBHOOK] Sending webhook [url={webhook.url}, description={webhook.description}]; checked in {(time.time() - t1) * 1000:.2f}ms.')
    t = time.time()

    headers, data_str, status, resp_headers, content, err = await _retry_send_webhook(
        webhook,
        data,
        delay=1,
        jitter=1,
        tries=3,  # Reduced from 7 to 3 to prevent long hangs
    )
    if not err:
        logger.info(f'[WEBHOOK] Sent webhook [url={webhook.url}, description={webhook.description}] status={status} in {(time.time() - t) * 1000:.2f}ms.')
    else:
        logger.error(f'[WEBHOOK] Failed send webhook [url={webhook.url}, description={webhook.description}]: {err}')

    await db_api.create_org_webhook_item({
            'webhook_id': webhook.id,
            'headers': json.dumps(headers) if headers else "{}",
            'payload': data_str,
            'response': content or "",
            'status_code': status,
            'response_headers': json.dumps(resp_headers) if resp_headers else "{}",
            'error': err,
        })
    await db_api.keep_n_webhook_items(webhook.id, 10)


async def _retry_send_webhook(webhook, data, delay=0, tries=1, backoff=2, jitter=0, max_delay=60):
    """Retry webhook sending with exponential backoff and proper error handling."""
    _tries, _delay = tries, delay
    err = None
    headers, data_str, status = {}, "", 0
    resp_headers, content = {}, ""

    while _tries > 0:
        try:
            headers, data_str, status, resp_headers, content, err = await _send_webhook(webhook, data)
            if not err:
                return headers, data_str, status, resp_headers, content, err
            else:
                logger.warning(f'[WEBHOOK] Retry {tries - _tries + 1}/{tries} failed for {webhook.url}: {str(err)}')
                _tries -= 1

                if _tries > 0:  # Don't sleep on the last attempt
                    await asyncio.sleep(_delay)
                    _delay = min(_delay * backoff + jitter, max_delay)
        except Exception as e:
            err = f'Retry exception: {e.__class__.__name__}: {str(e)}'
            logger.error(f'[WEBHOOK] Retry exception for {webhook.url}: {err}')
            _tries -= 1
            if _tries > 0:
                await asyncio.sleep(_delay)
                _delay = min(_delay * backoff + jitter, max_delay)

    return headers, data_str, status, resp_headers, content, err


async def _send_webhook(webhook: models.OrganizationWebhook, data: Union[dict, list]):
    """Send a single webhook request with per-request client."""
    headers = webhook.get_headers() or {}

    try:
        data_str = json.dumps(data)
    except (TypeError, ValueError) as e:
        return headers, "", 0, {}, "", f"JSON serialization error: {str(e)}"

    headers[kibernetika_header] = hmac_sha256(webhook.key, data_str)
    headers['Content-Type'] = 'application/json'
    headers['User-Agent'] = 'IRA-Chat-Webhook/1.0'

    resp = None
    err = None

    async with create_http_client() as client:
        try:
            # Send the request with proper headers
            resp = await client.post(
                webhook.url,
                content=data_str,
                headers=httpx.Headers(headers, encoding='utf-8')
            )

            # Check for HTTP errors
            if resp.status_code >= 400:
                # Limit response content to prevent memory issues
                content_preview = resp.content.decode('utf-8', errors='ignore')[:1000]
                err = f'{resp.status_code}: {content_preview}'

        except httpx.TimeoutException:
            err = f'Timeout after {WEBHOOK_TIMEOUT} seconds'
        except httpx.ConnectError as e:
            err = f'Connection error: {str(e)}'
        except httpx.HTTPError as e:
            err = f'HTTP error: {str(e)}'
        except Exception as e:
            err = f'{e.__class__.__name__}: {str(e)}'

    status = resp.status_code if resp else 0
    content = ""
    resp_headers = {}

    if resp:
        try:
            # Limit content size to prevent memory issues
            content = resp.content.decode('utf-8', errors='ignore')[:5000]
            resp_headers = dict(resp.headers)
        except Exception as e:
            logger.warning(f"Error extracting response data: {e}")
            content = ""
            resp_headers = {}

    return headers, data_str, status, resp_headers, content, err


def hmac_sha256(key, message):
    return base64.standard_b64encode(
        hmac.new(
            key.encode(),
            message.encode(),
            hashlib.sha256
        ).digest()
    ).decode()
