import logging
from datetime import datetime
from typing import Optional, List, Tuple, Sequence, Union

import pandas as pd
from pydantic import Field
from trulens_eval import tru
from trulens_eval.db import DB
from trulens_eval.schema import RecordID, FeedbackResultID, FeedbackDefinitionID, FeedbackResultStatus, FeedbackResult, \
    FeedbackDefinition, AppDefinition, AppID, Record
from trulens_eval.utils.serial import JSON

logger = logging.getLogger(__name__)


# noinspection PyMissingConstructor
class LocalTru(tru.Tru):
    def __init__(
        self,
        database_url: Optional[str] = None,
        database_file: Optional[str] = None
    ):
        """
        TruLens instrumentation, logging, and feedback functions for apps.

        Args:
           database_url: SQLAlchemy database URL. Defaults to a local
                                SQLite database file at 'default.sqlite'
                                See [this article](https://docs.sqlalchemy.org/en/20/core/engines.html#database-urls)
                                on SQLAlchemy database URLs.
           database_file: (Deprecated) Path to a local SQLite database file
        """
        if hasattr(self, "db"):
            if database_url is not None or database_file is not None:
                logger.warning(
                    f"Tru was already initialized. Cannot change database_url={database_url} or database_file={database_file} ."
                )

            # Already initialized by SingletonByName mechanism.
            return

        assert None in (database_url, database_file), \
            "Please specify at most one of `database_url` and `database_file`"

        self.db: IraChatDB = IraChatDB()


class IraChatDB(DB):
    feedbacks: dict = Field(default_factory=dict)
    app_to_record: dict = Field(default_factory=dict)
    record_to_app: dict = Field(default_factory=dict)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.feedbacks = {}
        self.app_to_record = {}
        self.record_to_app = {}

    def reset_database(self, app_id=None):
        record_id = self.app_to_record[app_id]

        del self.feedbacks[record_id]
        del self.app_to_record[app_id]
        del self.record_to_app[record_id]

    def insert_record(self, record: Record) -> RecordID:
        self.app_to_record[record.app_id] = record.record_id
        self.record_to_app[record.record_id] = record.app_id
        return record.record_id

    def insert_app(self, app: AppDefinition) -> AppID:
        # self.feedbacks[app.app_id] = []
        return app.app_id

    def insert_feedback_definition(self, feedback_definition: FeedbackDefinition) -> FeedbackDefinitionID:
        return '1'

    def get_feedback_defs(self, feedback_definition_id: Optional[str] = None) -> pd.DataFrame:
        pass

    def insert_feedback(self, feedback_result: FeedbackResult) -> FeedbackResultID:
        if feedback_result.record_id in self.feedbacks:
            self.feedbacks[feedback_result.record_id].append(feedback_result)
        else:
            self.feedbacks[feedback_result.record_id] = [feedback_result]

        return feedback_result.feedback_result_id

    def get_feedback(self, record_id: Optional[RecordID] = None, feedback_result_id: Optional[FeedbackResultID] = None,
                     feedback_definition_id: Optional[FeedbackDefinitionID] = None,
                     status: Optional[Union[FeedbackResultStatus,
                                            Sequence[FeedbackResultStatus]]] = None,
                     last_ts_before: Optional[datetime] = None) -> pd.DataFrame:
        pass

    def get_app(self, app_id: str) -> JSON:
        return {'app_id': app_id}

    def get_records_and_feedback(self, app_ids: Optional[List[str]] = None, ):
        record_id = self.app_to_record[app_ids[0]]
        return self.feedbacks.get(record_id), None
