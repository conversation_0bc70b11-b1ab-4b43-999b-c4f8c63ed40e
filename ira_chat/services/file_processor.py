import asyncio
import inspect
import logging
import os
from typing import Callable, Coroutine, Any, Dict

import tiktoken
from langchain.text_splitter import TokenTextSplitter
from langchain_core.documents import Document
from langchain_core.embeddings import Embeddings
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import HumanMessage
from langchain_core.messages import SystemMessage

from ira_chat.db import models
from ira_chat.services import file_formats
from ira_chat.services import llm as ira_llm
from ira_chat.utils import llm_utils
from ira_chat.utils.json_utils import extract_json

logger = logging.getLogger(__name__)


METADATA_PROMPT = """
Extract comprehensive metadata from the document.
Fields to extract:
- Date: Document date, which can be a signature, seal, or issue date (format: YYYY-MM-DD, e.g., 2023-10-25).
- Type: Category or type of the document.
- Name: Full title or name of the document.
- Author: Name of the document's author, if available.
- Keywords: A list of relevant keywords.
- Summary: A brief summary highlighting the document's key points.

Output must be in valid JSON format enclosed within triple backticks:
```
{
  "date": "YYYY-MM-DD",
  "type": "document type",
  "name": "document name",
  "author": "author name",
  "keywords": ["keyword1", "keyword2"],
  "summary": "brief summary"
}
```
"""

MEDICAL_METADATA_PROMPT = """
Extract comprehensive metadata from the document.
Fields to extract:
- Name: Full title or name of the document.
- Keywords: A list of relevant keywords.
- Summary: A brief summary highlighting the document's key points.
- Medication name (full product name)
- Active ingredients and quantities
- Pharmaceutical form
- ATC (Anatomical Therapeutic Chemical) code and authorization numbers (CIP, CIS, EU etc.)
- Special populations identifiers (pediatric, elderly, renal/hepatic impairment)
- Standard dosage information and contraindications
- Pharmaceutical information (excipients, shelf life, storage conditions)

Output must be in valid JSON format enclosed within triple backticks:
```json
{
  "name": "document name",
  "keywords": ["keyword1", "keyword2"],
  "summary": "brief summary",
  "medication_name": "medication name",
  "active_ingredients": ["ingredient1 20mg", "ingredient2 500mg"],
  "pharmaceutical_form": "form",
  "atc_code": "ATC code",
  "authorization_numbers": ["number1", "number2"],
  "special_populations": ["pediatric", "elderly"],
  "dosage_information": "dosage info",
  "contraindications": "contraindications",
  "pharmaceutical_information": "pharmaceutical info"
}
```
"""

CHUNK_METADATA_PROMPT = """
Extract specific metadata from this document chunk (a small section of a larger document).
Fields to extract:
- Section: The specific section or topic this chunk covers.
- Key_entities: Important people, organizations, or concepts mentioned in this chunk.
- Context: How this chunk relates to the broader document or what it's part of.
- Main_points: The key information or arguments presented in this chunk.
- References: Any citations, figures, tables, or cross-references mentioned in this chunk.

Output must be in valid JSON format enclosed within triple backticks:
```json
{
  "section": "section name",
  "key_entities": ["entity1", "entity2"],
  "context": "how this chunk fits into the document",
  "main_points": ["point1", "point2"],
  "references": ["reference1", "reference2"],
}
```
"""


class FileProcessor:
    def __init__(
        self,
        llm: BaseChatModel = None,
        embeddings: Embeddings = None,
        config: dict = None,
        doc_metadata_hook: str = None,
        chunk_metadata_hook: str = None,
        set_metadata_hook: Callable[[dict], None] | Callable[[dict], Coroutine[Any, Any, None]] = lambda _: _,
        update_status_hook: Callable[[str], None] | Callable[[str], Coroutine[Any, Any, None]] = lambda _: _,
    ):
        self.llm = llm
        self.embeddings = embeddings
        self.update_status_hook = update_status_hook
        self.doc_metadata_hook = doc_metadata_hook
        self.chunk_metadata_hook = chunk_metadata_hook
        self.set_metadata_hook = set_metadata_hook
        self.config = config or {}

        # self.default_splitter = file_formats.get_text_splitter(
        #     chunk_size=self.config.get('chunk_size'),
        #     chunk_overlap=self.config.get('chunk_overlap'),
        # )

    async def process(
        self, data, filename: str, system_metadata=None
    ) -> tuple[list[Document], list[Document]]:
        """
        Process a file and return the original and split documents.

        Stages:
            1. Load the file
            2. Extract doc metadata
            3. Split the file into chunks
            4. Extract chunks metadata
            5. Return the original and split documents
        """
        # splitter = self.default_splitter
        doc_metadata_hook = self.doc_metadata_hook
        chunk_metadata_hook = self.chunk_metadata_hook
        loop = asyncio.get_event_loop()
        all_docs = await loop.run_in_executor(None, file_formats.load_file, data, filename, system_metadata)

        if system_metadata.get('source_type') == models.SourceType.CORRECTED_TXT:
            splitter = file_formats.get_corrected_answer_chunker()
            # Don't need to extract metadata from question and answer data
            doc_metadata_hook = None
            chunk_metadata_hook = None
        else:
            splitter = file_formats.get_text_splitter(
                chunk_size=self.config.get('chunk_size'),
                chunk_overlap=self.config.get('chunk_overlap'),
                file_ext=os.path.splitext(filename)[1],
            )

        if doc_metadata_hook:
            await self._update_status(models.STATUS_PROCESSING_META)

            all_docs = await doc_metadata_hooks[doc_metadata_hook](
                all_docs, self.llm, system_metadata, self.config, 'metadata_prompt',
            )
            if len(all_docs) > 0:
                # TODO: what to do if there are multiple docs?
                meta = all_docs[0].metadata.copy()
                if system_metadata:
                    [meta.pop(k, None) for k in system_metadata.keys()]
                await self.set_metadata(meta)

        splitted_docs = await loop.run_in_executor(None, splitter.split_documents, all_docs)

        if chunk_metadata_hook:
            await self._update_status(models.STATUS_PROCESSING_CHUNK_META)

            splitted_docs = await chunk_metadata_hooks[chunk_metadata_hook](
                splitted_docs, self.llm, system_metadata, self.config, 'chunk_metadata_prompt',
            )

        return all_docs, splitted_docs

    async def set_metadata(self, metadata: dict):
        """Set metadata for the file."""
        if not self.set_metadata_hook:
            return
        if inspect.iscoroutinefunction(self.set_metadata_hook):
            await self.set_metadata_hook(metadata)
        else:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self.set_metadata_hook, metadata)

    async def _update_status(self, status: str):
        """Update the status of the file."""
        if not self.update_status_hook:
            return
        if inspect.iscoroutinefunction(self.update_status_hook):
            await self.update_status_hook(status)
        else:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self.update_status_hook, status)


def count_tokens(text: str, model_name: str = "gpt-4o"):
    """Count the number of tokens in a text string."""
    try:
        encoding = tiktoken.encoding_for_model(model_name)
    except KeyError:
        encoding = tiktoken.get_encoding("o200k_base")
    return len(encoding.encode(text))


def get_model_context_limit(model_name: str):
    """Get the context limit for the specified model."""
    context_limit, _ = ira_llm.all_token_limits.get(model_name) or (16384, 1000)
    return context_limit


def split_document(doc: Document, max_tokens: int, model_name: str):
    """Split a document into chunks if it exceeds the token limit."""
    try:
        encoding = tiktoken.encoding_for_model(model_name)
        encoding_name = encoding.name
    except KeyError:
        encoding_name = "o200k_base"

    splitter = TokenTextSplitter(
        encoding_name=encoding_name,
        chunk_size=max_tokens,
        chunk_overlap=100  # Some overlap to maintain context
    )

    chunks = splitter.split_text(doc.page_content)
    return [Document(page_content=chunk, metadata=doc.metadata.copy()) for chunk in chunks]


def merge_metadata(existing_metadata: Dict, new_metadata: Dict) -> Dict:
    """Intelligently merge metadata from different chunks."""
    result = existing_metadata.copy()

    for key, value in new_metadata.items():
        if key in result:
            # For lists, combine them
            if isinstance(value, list) and isinstance(result[key], list):
                result[key].extend(value)
            # For strings, concatenate with a space
            elif isinstance(value, str) and isinstance(result[key], str):
                result[key] += " " + value
            # For other types, prefer the first non-empty value
            else:
                pass  # Keep the first value
        else:
            result[key] = value

    return result


async def process_single_chunk(chunk_doc: Document, llm: BaseChatModel, prompt: str, chunk_index: int = None, total_chunks: int = None):
    """Process a single document chunk and extract metadata."""
    if chunk_index is not None and total_chunks is not None:
        logger.info(f"Processing chunk {chunk_index+1}/{total_chunks}")

    messages = [
        SystemMessage(content=prompt),
        HumanMessage(content=chunk_doc.page_content),
    ]

    try:
        response = await llm.ainvoke(messages)
        response_json = extract_json(response.content)
        return response_json
    except Exception as e:
        if chunk_index is not None:
            logger.error(f"Error processing chunk {chunk_index+1}: {str(e)}")
        else:
            logger.error(f"Error processing document: {str(e)}")
            logger.error(f"Document: {chunk_doc.page_content}")
            logger.error(f"Prompt: {prompt}")
        raise


async def split_and_merge(doc: Document, max_content_tokens: int, model_name: str, llm: BaseChatModel, prompt: str):
    """Split a large document, process each chunk, and merge the metadata."""
    logger.info(f"Document too large, splitting into chunks")

    # Split the document into chunks
    chunk_docs = split_document(doc, max_content_tokens, model_name)

    # Process each chunk and collect metadata
    all_metadata = {}
    for i, chunk_doc in enumerate(chunk_docs):
        chunk_metadata = await process_single_chunk(chunk_doc, llm, prompt, i, len(chunk_docs))
        if chunk_metadata:
            all_metadata = merge_metadata(all_metadata, chunk_metadata)

    return all_metadata


async def process_doc(
    doc: Document,
    llm: BaseChatModel,
    prompt: str,
    additional_meta: dict = None,
    semaphore: asyncio.Semaphore = None
):
    """Process a document to extract metadata, handling large documents by splitting if necessary."""
    async with semaphore:
        # Get the model's context limit
        model_name = llm_utils.get_model_name(llm) or "gpt-4o"
        context_limit = get_model_context_limit(model_name)

        # Count tokens in the document and prompt
        doc_tokens = count_tokens(doc.page_content, model_name)
        prompt_tokens = count_tokens(prompt, model_name)

        # Reserve some tokens for the response and system message overhead
        max_content_tokens = context_limit - prompt_tokens - 1000  # Reserve 1000 tokens for response

        # Process based on document size
        if doc_tokens > max_content_tokens:
            # Handle large document by splitting and merging
            all_metadata = await split_and_merge(doc, max_content_tokens, model_name, llm, prompt)
            if all_metadata:
                doc.metadata.update(all_metadata)
        else:
            # Process normally if document is not too large
            response_json = await process_single_chunk(doc, llm, prompt)
            if response_json:
                doc.metadata.update(response_json)

        # Add any additional metadata
        if additional_meta:
            doc.metadata.update(additional_meta)

        return doc


async def llm_metadata_hook(
    docs: list[Document],
    llm: BaseChatModel,
    additional_meta: dict = None,
    config: dict = None,
    prompt_config_key: str = 'metadata_prompt',
) -> list[Document]:
    prompt = None
    if config:
        prompt = config.get('prompt_config', {}).get(prompt_config_key)

    # Set default prompt
    if prompt_config_key == 'metadata_prompt':
        prompt = prompt or METADATA_PROMPT
    else:
        prompt = prompt or CHUNK_METADATA_PROMPT

    # logger.info(f"Using prompt: {prompt}")

    # Get the number of workers from config or use a default value
    max_workers = int(config.get('max_concurrent_llm_calls') or 8)
    semaphore = asyncio.Semaphore(max_workers)

    # Process all documents concurrently with semaphore limiting
    tasks = [process_doc(doc, llm, prompt, additional_meta, semaphore) for doc in docs]
    processed_docs = await asyncio.gather(*tasks)

    return processed_docs


async def noop_hook(
    docs: list[Document],
    llm: BaseChatModel,
    additional_meta: dict = None,
    config: dict = None,
    prompt_config_key: str = 'metadata_prompt'
):
    return docs


doc_metadata_hooks: dict[
    str,
    Callable[[list[Document], BaseChatModel, dict, dict, str], Coroutine[Any, Any, list[Document]]]
] = {
    'llm_hook': llm_metadata_hook,
    'noop_hook': noop_hook,
}

chunk_metadata_hooks: dict[
    str,
    Callable[[list[Document], BaseChatModel, dict, dict, str], Coroutine[Any, Any, list[Document]]]
] = {
    'llm_hook': llm_metadata_hook,
    'noop_hook': noop_hook,
}
