import copy
import logging
from typing import Union, Dict, Any

from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain.prompts import chat as chat_prompts
from langchain_community.embeddings import OllamaEmbeddings
from langchain_openai import OpenAIEmbeddings
# from langchain_community.vectorstores.pinecone import <PERSON>cone
from langchain_core.embeddings import Embeddings
from langchain_core.language_models import BaseChatModel
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder, BasePromptTemplate
from langchain_core.retrievers import BaseRetriever, RetrieverLike, RetrieverOutputLike, RetrieverOutput
from langchain_core.runnables import RunnableBranch, RunnablePassthrough, Runnable
from langchain_core.vectorstores import VectorStore
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_mistralai import MistralAIEmbeddings
from langchain_qdrant import QdrantVectorStore
from langchain_voyageai.embeddings import VoyageAIEmbeddings
from qdrant_client.conversions.common_types import VectorParams
from qdrant_client.http.models import Distance, HnswConfigDiff

from ira_chat.db import models
from ira_chat.services import agents, subquery_agent
from ira_chat.utils import llm_utils, env, utils

logger = logging.getLogger(__name__)
system_template = """
You are a highly capable and helpful AI assistant.
Your primary goal is to assist users with accurate, informative, and relevant answers on their questions in a polite and approachable tone.
You are empathetic, friendly, and engaging, but you maintain a professional and focused demeanor to ensure clarity and precision.

The answer should be directly addressed to the user.
Please do not use any placeholders or generic sign-offs such as 'your name' or similar. All responses must be fully self-contained and should not include any templated text for names or signatures.
If the documentation is insufficient or unclear, indicate that and ask for clarification.
Use the following documentation to answer the user question.
---
{context}
---
"""
# "Additional rule: Avoid introduction words, start with the answer itself.\n"
contextualize_q_system_prompt = (
    "Given a chat history and the latest user question "
    "which might reference context in the chat history, "
    "formulate a standalone question in its original language which can be understood "
    "without the chat history. Do NOT answer the question, just "
    "reformulate it if needed and otherwise return it as is."
)


def copy_prepare_vectorstore(store: VectorStore, namespace: str, only_if_exists=False):
    vs = copy_vectorstore(store, namespace)
    vs = prepare_vectorstore(vs, namespace, only_if_exists=only_if_exists)
    return vs


def copy_vectorstore(store: VectorStore, namespace: str):
    # if isinstance(store, Pinecone):
    #     e, idx = store._embedding, store._index
    #     store._embedding = None
    #     store._index = None
    #
    #     copied = copy.deepcopy(store)
    #     copied._embedding = e
    #     copied._index = idx
    #     # Like a namespace
    #     copied._namespace = namespace
    #
    #     store._embedding = e
    #     store._index = idx
    #
    #     return copied
    if isinstance(store, QdrantVectorStore):
        e, ef, client = store._embeddings, store._sparse_embeddings, store._client
        store._embeddings = None
        store._embeddings_function = None
        store._client = None

        copied = copy.deepcopy(store)
        copied._embeddings = e
        copied._sparse_embeddings = ef
        copied._client = client
        # Like a namespace
        copied.collection_name = namespace

        store._client = client
        store._embeddings = e

        return copied
    return copy.deepcopy(store)


def _embeddings_size(embeddings: Embeddings) -> int:
    # TODO try and get embedding to get the embedding size here, for now use 1536/4096/768 length
    size = 1536
    if isinstance(embeddings, OpenAIEmbeddings):
        size = 1536
        if embeddings.model == 'text-embedding-3-small':
            size = 1536
        elif embeddings.model == 'text-embedding-3-large':
            size = 3072
        elif embeddings.model == 'text-embedding-ada-002':
            size = 1536
    if isinstance(embeddings, OllamaEmbeddings):
        size = 4096
    elif isinstance(embeddings, GoogleGenerativeAIEmbeddings):
        size = 768
    elif isinstance(embeddings, MistralAIEmbeddings):
        size = 1024
    elif isinstance(embeddings, VoyageAIEmbeddings):
        # May be 1024 in case of voyage-3 model
        if embeddings.model == 'voyage-3-lite':
            size = 512
        else:
            size = 1024

    return size


def prepare_vectorstore(store: VectorStore, namespace: str, index_recreate=False, only_if_exists=False):
    size = _embeddings_size(store.embeddings)

    if isinstance(store, QdrantVectorStore):
        def create_collection(store: QdrantVectorStore, size):
            try:
                ok = store.client.create_collection(
                    collection_name,
                    vectors_config=VectorParams(
                        size=size,
                        distance=Distance.COSINE,
                        on_disk=env.vectorstore_on_disk(),
                        quantization_config={
                            'scalar': {
                                "type": "int8",
                                "always_ram": True
                            }
                        },
                    ),
                    hnsw_config=HnswConfigDiff(on_disk=False)
                )
                if not ok:
                    raise ValueError('Qdrant collection not created.')
            except Exception as e:
                if 'already exists' in str(e):
                    # Skip if in parallel a collection was already created
                    pass
                else:
                    raise
        collection_name = namespace
        try:
            collection_exists = store.client.collection_exists(collection_name)
            if not collection_exists:
                if only_if_exists:
                    return None
                raise ValueError('to create collection')
            if index_recreate:
                collection = store.client.get_collection(collection_name)
                vector_size = collection.config.params.vectors.size
                if vector_size != size:
                    store.client.delete_collection(collection_name)
                    create_collection(store, size)
        except:
            create_collection(store, size)
    return store


def clear_vectorstore(prepared_store: VectorStore):
    if isinstance(prepared_store, QdrantVectorStore):
        items, _ = prepared_store.client.scroll(prepared_store.collection_name, limit=100)
        while len(items) > 0:
            prepared_store.client.delete(prepared_store.collection_name, [item.id for item in items])
            items, _ = prepared_store.client.scroll(prepared_store.collection_name, limit=100)
    else:
        raise TypeError(f'Unrecognized vectorstore type: {type(prepared_store)}')


def delete_vectorstore(prepared_store: VectorStore):
    if isinstance(prepared_store, QdrantVectorStore):
        prepared_store.client.delete_collection(prepared_store.collection_name)
    else:
        raise TypeError(f'Unrecognized vectorstore type: {type(prepared_store)}')


def get_vectorstore_size_vector_count(prepared_store: VectorStore) -> tuple[int, int]:
    if isinstance(prepared_store, QdrantVectorStore):
        try:
            collection = prepared_store.client.get_collection(prepared_store.collection_name)
        except Exception as e:
            if 'not found' in str(e).lower():
                return 0, 0
            raise
        size = int(collection.config.params.vectors.size * collection.points_count * 4 * 1.5)
        vector_count = collection.points_count
        return size, vector_count
    else:
        raise TypeError(f'Unrecognized vectorstore type: {type(prepared_store)}')


def get_default_prompts(chat_config=None) -> dict:
    return {
        'condense_question_prompt': contextualize_q_system_prompt,
        'system_prompt': system_template,
    }


def get_chat_prompt_template(prompt_params: dict, params: dict):
    language = params.get('language', None)
    if language is not None:
        prompt_params['system_prompt'] += (
            f'---\nAdditional rule: Answer must be in {language.capitalize()} language.\n---\n'
        )

    system_support = llm_utils.support_system_message(params)
    if not system_support:
        chat_prompt = chat_prompts.HumanMessagePromptTemplate.from_template(prompt_params['system_prompt'])
    else:
        chat_prompt = chat_prompts.SystemMessagePromptTemplate.from_template(prompt_params['system_prompt'])

    messages = [
        chat_prompt,
        # chat_prompts.SystemMessagePromptTemplate.from_template(prompt_params['system_prompt']),
        chat_prompts.HumanMessagePromptTemplate.from_template("{question}"),
    ]
    chat_prompt = chat_prompts.ChatPromptTemplate.from_messages(messages)
    return chat_prompt


def get_agent_chain(llm: BaseChatModel, streaming_llm: BaseChatModel | None, retriever: BaseRetriever, prompt_params: dict, params):
    chat_prompt = get_chat_prompt_template(prompt_params, params)

    # Check if we should use the subquery agent
    if params.get('use_subquery_agent', False):
        return subquery_agent.get_subquery_graph(retriever, llm, chat_prompt, streaming_llm=streaming_llm, config=params)
    else:
        return agents.get_graph(retriever, llm, chat_prompt, streaming_llm=streaming_llm, config=params)


def get_chain(llm: BaseChatModel, streaming_llm: BaseChatModel | None, retriever: BaseRetriever, prompt_params: dict, params):
    # Contextualize question
    contextualize_q_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", prompt_params['condense_question_prompt']),
            MessagesPlaceholder("chat_history"),
            ("human", "{question}"),
        ]
    )
    history_aware_retriever_obj = create_history_aware_retriever(llm, retriever, contextualize_q_prompt, params)

    chat_prompt = get_chat_prompt_template(prompt_params, params)
    chat_prompt.messages.insert(-2, MessagesPlaceholder("chat_history"))

    question_answer_chain = create_stuff_documents_chain(streaming_llm or llm, chat_prompt)
    chain = create_retrieval_chain(llm, history_aware_retriever_obj, question_answer_chain, params)

    return chain


def create_history_aware_retriever(
    llm: BaseChatModel,
    retriever: RetrieverLike,
    prompt: BasePromptTemplate,
    config: dict = None,
) -> RetrieverOutputLike:
    """Returns dict {
      "question": "",
      "generated_question": "",
      "source_documents": [Document],
      "chat_history": [("user", "message)],
    }
    """
    if "question" not in prompt.input_variables:
        raise ValueError(
            "Expected `question` to be a prompt variable, "
            f"but got {prompt.input_variables}"
        )

    def log_generated_question(x):
        q = x["generated_question"]
        logger.info(f"[CHAIN] Generated question: {q}")
        return q

    fast_llm = llm_utils.set_cheapest_model(llm)

    async def run_input_rail(input) -> bool:
        rails_fn = agents.input_guardrails_fn(fast_llm)
        return (await rails_fn(input))['input_ok']

    enable_input_rail = config.get('input_rail_enabled', False)

    retrieve_documents: RetrieverOutputLike = RunnableBranch(
        (
            # Both empty string and empty list evaluate to False
            lambda x: not x.get("chat_history", False),
            # If no chat history, then we just pass input to retriever
            RunnablePassthrough.assign(
                generated_question=(lambda x: x["question"]), chat_history=lambda x: []
            ).assign(source_documents=(lambda x: x["generated_question"]) | retriever),
        ),
        # If chat history, then we pass inputs to LLM chain, then to retriever
        RunnablePassthrough.assign(
            generated_question=prompt | llm | StrOutputParser()
        ).assign(source_documents=log_generated_question | retriever)
    ).with_config(run_name="chat_retriever_chain")

    if enable_input_rail:
        return RunnableBranch(
            (
                run_input_rail, RunnablePassthrough.assign(input_ok=lambda x: True) | retrieve_documents,
            ),
            RunnablePassthrough.assign(
                input_ok=lambda x: False,
                generated_question=(lambda x: "Generate refusal due to violating policies."),
                chat_history=lambda x: [],
                source_documents=(lambda x: []),
            )
        ).with_config(run_name='rail_chain')
    else:
        return retrieve_documents


def create_retrieval_chain(
    llm: BaseChatModel,
    retriever: Union[BaseRetriever, Runnable[dict, RetrieverOutput]],
    combine_docs_chain: Runnable[Dict[str, Any], str],
    config: dict = None,
) -> Runnable:
    if not isinstance(retriever, BaseRetriever):
        retrieval_docs: Runnable[dict, RetrieverOutput] = retriever
    else:
        retrieval_docs = (lambda x: x["generated_question"]) | retriever

    # Retrieval docs gives a dict
    def change_inputs(inputs):
        return {
            "context": inputs["source_documents"],
            "question": inputs["question"],
            "generated_question": inputs["generated_question"],
            "chat_history": inputs["chat_history"],
        }

    enable_output_rail = config.get('output_rail_enabled', False) and not config.get('stream', False)
    fast_llm = llm_utils.set_cheapest_model(llm)

    # Logic: qa_chain ─> output_rail ────────> regenerate
    #                 └─> END (if disabled) └──> END (if OK)

    async def run_output_rail(input_dict) -> bool:
        to_rails = {
            'messages': input_dict['chat_history'] + [(models.ROLE_USER, input_dict['question'])] + [(models.ROLE_AI, input_dict['answer'])],
            'question': input_dict['question'],
        }
        rails_fn = agents.output_guardrails_fn(fast_llm)
        return (await rails_fn(to_rails))['output_ok']

    qa_chain = (
        retrieval_docs | RunnablePassthrough.assign(answer=change_inputs | combine_docs_chain)
    ).with_config(run_name="retrieval_chain")

    if enable_output_rail:
        async def regenerate(input_dict) -> dict:
            history = []
            for msg in input_dict['chat_history']:
                if msg[0] == models.ROLE_AI:
                    history.append(('assistant', msg[1]))
                else:
                    history.append(('user', msg[1]))
            to_rails = {
                'messages': input_dict['chat_history'] + [(models.ROLE_USER, input_dict['question'])] + [(models.ROLE_AI, input_dict['answer'])],
                'question': input_dict['question'],
                'source_documents': [],
            }
            regenerate_func = agents.regenerate_fn(fast_llm, config)
            return (await regenerate_func(to_rails))['answer']

        return qa_chain | RunnableBranch(
            (
                run_output_rail, lambda x: x,  # no-op if ok
            ),
            # Trigger regenerate
            RunnablePassthrough.assign(answer=regenerate)
        ).with_config(run_name='rail_chain')
    else:
        return qa_chain


# def trulens_wrapper(chain, random_id: str) -> TruChain:
#     oai_feedback = OpenAI()
#     hug = Huggingface()
#
#     feedbacks = [
#         Feedback(oai_feedback.relevance, name=models.METRIC_RELEVANCE).on_input_output(),
#         Feedback(oai_feedback.correctness, name=models.METRIC_CORRECTNESS).on_output(),
#         # Feedback(oai_feedback.coherence, name=models.METRIC_COHERENCE).on_output(),
#         # Feedback(oai_feedback.helpfulness, name=models.METRIC_HELPFULNESS).on_output(),
#         Feedback(hug.positive_sentiment, name=models.METRIC_SENTIMENT).on_input(),
#         Feedback(hug.language_match, name=models.METRIC_LANGUAGE_MATCH).on_input_output(),
#     ]
#     tru_chain = TruChain(
#         chain,
#         app_id=random_id,
#         feedbacks=feedbacks,
#         tags="prototype",
#         # db=trulens_db.IraChatDB(),
#         tru=trulens_db.LocalTru(),
#         feedback_mode=FeedbackMode.WITH_APP_THREAD,
#     )
#     # tru.get_records_and_feedback()
#     return tru_chain
