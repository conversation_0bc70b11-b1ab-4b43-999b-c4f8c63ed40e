import asyncio
import collections
import datetime
import logging
from typing import Annotated, Sequence, TypedDict, List
from typing import Literal

from langchain.tools.retriever import create_retriever_tool
from langchain_core.documents import Document
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import BaseMessage, ToolMessage, AIMessage
from langchain_core.messages import HumanMessage
from langchain_core.prompts import PromptTemplate, ChatPromptTemplate
from langchain_core.retrievers import BaseRetriever
from langchain_core.tools import Tool
from langchain_qdrant import QdrantVectorStore
from langgraph.graph import END, StateGraph, START
from langgraph.graph.message import add_messages
from pydantic import BaseModel, Field

from ira_chat.db import api as db_api, base
from ira_chat.services import rails, qdrant_vs, rrf
from ira_chat.utils import utils, llm_utils, json_utils
from ira_chat.utils.llm_utils import set_cheapest_model

logger = logging.getLogger(__name__)

PATH_NO_ANSWER = 'no_answer'
PATH_NEXT = 'next'
PATH_REWRITE_QUERY = 'rewrite_query'
PATH_EXCLUDED_TOPICS = 'excluded_topics_check'
PATH_CACHE_CHECK = 'cache_check'
PATH_CACHE_CHECKED = 'cache_checked'
PATH_VECTORSTORE = 'vectorstore'
PATH_GRADE_DOCS = 'grade_each'
PATH_RERANK_DOCS = 'rerank_docs'
PATH_ANSWERS_VECTORSTORE = 'answers_vectorstore'
PATH_REFUSAL = 'refusal'
PATH_GENERATE = 'generate'
PATH_REGENERATE = 'regenerate'
PATH_INPUT_RAIL = 'input_rail'
PATH_SUBQUERY = 'subquery'
PATH_TOPIC_ROUTER = 'topic_router'
PATH_CLARIFY = 'clarify'


def merge_documents(left: List[Document], right: List[Document]) -> List[Document]:
    left = left or []
    right = right or []
    return left + right


def select_one_string(left: str, right: str) -> str:
    return left or right


class AgentState(TypedDict):
    # The add_messages function defines how an update should be processed
    # Default is to replace. add_messages says "append"
    org_id: int
    workspace_id: int
    dataset_id: int
    messages: Annotated[Sequence[BaseMessage], add_messages]
    question: str
    generated_question: str
    answer: str
    initial_documents: Annotated[List[Document], merge_documents]
    source_documents: List[Document]
    rewritten: bool
    input_ok: bool
    output_ok: bool
    output_check_score: int
    not_answer: bool
    not_answer_reason: str
    # Fields for subquery processing
    query_plan: dict
    subqueries: dict
    combined_summary: str
    # topic router fields
    path: str
    topic_group: str
    topic: str
    topic_confidence: float
    cache_hit: bool


class RouteQuery(BaseModel):
    """Route a user query to the most relevant path."""

    path: Literal["vectorstore", "general"] = Field(
        # default='vectorstore',
        description="Route path, general or a vectorstore.",

    )
    query: str = Field(description='Query to look up in retriever')


class RewriteQuery(BaseModel):
    """Rewrite a user query for optimal vectorstore search."""
    query: str = Field(description='Query to look up in retriever')


class TopicGroupRouter(BaseModel):
    """Router for mapping user query to a predefined topic group."""
    reasoning: str = Field(description="Brief reasoning for the routing decision")
    matched_group: str = Field(description="The name of the matched topic group")
    matched_topic: str = Field(description="The specific topic within the group that was matched")
    confidence: float = Field(description="Confidence score for the match (0.0 to 1.0)")
    route_to: str = Field(description="Where to route the query next (node name from the configured routes)")


class Grade(BaseModel):
    """Score for relevance check."""
    reasoning: str = Field(description="Reasoning behind the score, one sentence.")
    score: int = Field(description="Relevance score from 0 to 10")


class DocumentGrade(BaseModel):
    """Grade for a single document with document identifier."""
    document_index: int = Field(description="Index of the document in the batch (0-based)")
    reasoning: str = Field(description="Reasoning behind the score, one sentence.")
    score: int = Field(description="Relevance score from 0 to 10")


class BatchGrade(BaseModel):
    """Batch grading response for multiple documents."""
    document_grades: List[DocumentGrade] = Field(description="List of grades for each document in the batch")


class AnswerGrade(BaseModel):
    reasoning: str = Field(description="Reasoning behind the score, one sentence.")
    """Score for relevance check."""
    score: int = Field(description="Answer score from 0 to 10")


def get_retriever_tool(retriever, chat_topics: list[str] = None) -> Tool:
    if chat_topics:
        desc = "Searches and returns information in document index only about:\n - "
        desc += "\n - ".join(chat_topics)
    else:
        desc = "Searches and returns information about various data in document index."

    retriever_tool = create_retriever_tool(
        retriever,
        "retrieve_documents_data",
        desc,
        document_prompt=PromptTemplate.from_template("{page_content}"),
        document_separator='\n\n',
    )
    return retriever_tool


def get_tools(retriever):
    ret_tool = get_retriever_tool(retriever)
    return [ret_tool]


def rewrite_query_fn(llm: BaseChatModel, config: dict = None):
    async def route_rewrite_query(state: AgentState):
        logger.info("---REWRITE QUESTION---")

        # chat_topics = config.get('chat_topics')
        # routing_enabled = config.get('routing_enabled', False)
        # if chat_topics:
        #     desc = "The vectorstore contains documents about:\n - "
        #     desc += "\n - ".join(chat_topics)
        # else:
        #     desc = "The vectorstore contains documents about various data in document index.\n"

        rewrite_part = (
            "Re-write an input question/message to a better version that is optimized "
            "for vectorstore search. Look at the input and try to reason about the "
            "underlying semantic intent / meaning.\n"
            "Look at the history of the chat and address the last message.\n"
            "Keep the key words as they are without reconsidering them.\n"
        )
        # system = (
        #     "You are an expert at routing a user question to a vectorstore or a general path.\n"
        #     f'{desc}'
        #     "Use the vectorstore for questions on these topics. Otherwise, use general.\n"
        #     f"For the vectorstore, {rewrite_part}"
        # )
        system = rewrite_part

        formatted_messages = [
            (m.type, m.content)
            for m in state['messages']
        ]
        support_system = llm_utils.support_system_message(config)
        prompt_messages = [
            ("system" if support_system else "human", system),
            *formatted_messages,
        ]

        structured_llm_router = llm.with_structured_output(RewriteQuery)
        # structured_llm_router = llm.with_structured_output(RouteQuery, method=llm_utils.structured_output_method(llm))

        # noinspection PyTypeChecker
        query_result: RewriteQuery = await structured_llm_router.ainvoke(prompt_messages)

        logger.info(f"---GOT QUERY: {query_result.query}---")
        return {
            'generated_question': query_result.query,
            # 'generated_question': state['question'],
            'initial_documents': state["initial_documents"] if state.get('initial_documents') else [],
            'source_documents': state["source_documents"] if state.get('source_documents') else [],
        }

    return route_rewrite_query


def exclude_topics_node_fn(llm: BaseChatModel, config: dict = None):
    async def exclude_topic_route_node(state: AgentState):
        loop = asyncio.get_event_loop()
        logger.info("---EXCLUDE TOPICS CHECK---")

        not_chat_topics = config.get('not_chat_topics')
        if not not_chat_topics:
            return {'not_answer': False}

        question = state['question']

        not_topics_vs: QdrantVectorStore = await loop.run_in_executor(None, config['init_not_topics_vectorstore'])

        # question_sentences = question.split('.')
        threshold = float(config.get('not_chat_topics_score_threshold') or 0.8)
        search_k = int(config.get('not_chat_topics_search_k') or 4)
        not_topics_docs_and_scores = await not_topics_vs.asimilarity_search_with_score(
            question, score_threshold=0.6, k=search_k,
        )
        (not_topics_docs, scores) = zip(*not_topics_docs_and_scores) if not_topics_docs_and_scores else ([], [])
        logger.info(f"GOT NOT TOPICS: {[d.page_content for d in not_topics_docs]}, scores={scores}")

        if any(s > threshold for s in scores):
            logger.info(f"---ADVANCED ROUTE: TRIGGER NO ANSWER (score > {threshold})---")
            return {'not_answer': True, 'not_answer_reason': 'not_chat_topics'}
        else:
            return {'not_answer': False}

    return exclude_topic_route_node


def decide_no_answer(state: AgentState) -> str:
    if state.get('not_answer', False):
        return PATH_NO_ANSWER
    else:
        return PATH_NEXT


def decide_path(state: AgentState):
    return state['path']


def input_guardrails_fn(llm: BaseChatModel):
    async def inner(state: AgentState):
        # noinspection PyTypeChecker
        rail = rails.LLMRails(rails.NEMO_RAILS_CONFIG, llm)
        logger.info("---CALL INPUT RAIL---")
        question = state["question"]
        response = await rail.generate_async(
            question,
            options={
                "output_vars": ["input_check_score", "bot_message"],
                # "output_vars": True,
                'rails': {'input': True, 'output': False, 'retrieval': False, 'dialog': False}
            },
        )
        return {'input_ok': response.output_data['input_check_score']}
    return inner


def determine_callback(llm: BaseChatModel, config: dict = None):
    async def inner(state: AgentState):
        prompt = """
Your task is to analyze user question intention and semantic meaning.
Rules:
  - Analyze the user question and tell if a user's intention is to be called back or if there is something we can do.

Output should be in JSON format:
{
  "reasoning": "",
  "user_callback": 0.5
}

The user question:

Bonjour, j'ai réussi à obtenir l'adresse url pour paramétrer sanofi en pharma ml, vous pouvez me rappeler

merci
"""
        all_messages = [('system' if llm_utils.support_system_message(config) else 'human', prompt)]
        response = await llm.ainvoke(all_messages)
        logger.info(f"---CALLBACK ANALYSIS = {response.content}---")
        return {
            'not_answer': True,
            'not_answer_reason': 'callback',
            'source_documents': [],
        }
    return inner


def output_check_fn(llm: BaseChatModel, config: dict = None):
    async def inner(state: AgentState):
        # noinspection PyTypeChecker
        # messages = state["messages"]
        answer = state["answer"]
        prompt = f"""
Task: Evaluate if the response is a "non-answer" by determining how much useful, specific information it contains.

Non-Answer Detection Guidelines:
- A non-answer fails to provide specific, relevant information that directly addresses the question
- Examples include: "I don't know", "I can't provide that information", "I'm not sure", "I don't have access to that data"
- Responses that redirect, deflect, or provide only generic information without addressing the specific question

Scoring Criteria (0-10):

0: Complete non-answer - Explicitly states inability to answer with no additional information ("I don't know")
1: Explicit non-answer with minimal context - Acknowledges inability to answer with brief explanation
2-3: Mostly non-answer - Provides very little relevant information, mostly explains inability to answer
4-5: Partial answer - Contains some relevant information but significant gaps remain, more explanation of limitations than actual content
6-7: Adequate answer - Provides useful information that partially addresses the question with some specificity
8-9: Strong answer - Offers specific, detailed information that addresses most aspects of the question
10: Complete answer - Provides comprehensive, specific information that fully addresses all aspects of the question

Important Instructions:
1. Focus solely on information content and relevance - not tone, style or length
2. Evaluate based on what information is present, not what might be implied
3. Output an integer score together with reasoning behind the score from 0-10 in JSON format

Answer to evaluate: {answer}
"""
        all_messages = [('human', prompt)]
        response = await llm.with_structured_output(AnswerGrade).ainvoke(all_messages)
        logger.info(f"---OUTPUT CHECK SCORE = {response.score}---")
        return {
            'output_check_score': response.score,
        }
    return inner


def output_guardrails_fn(llm: BaseChatModel, config: dict = None):
    async def inner(state: AgentState):
        # noinspection PyTypeChecker
        rail = rails.LLMRails(rails.NEMO_RAILS_CONFIG, llm)
        logger.info("---CALL OUTPUT RAIL---")
        question = state["question"]
        messages = state["messages"]

        all_messages = rails.convert_to_nemorails(messages)

        response = await rail.generate_async(
            messages=all_messages,
            options={
                "output_vars": ["output_check_score", "bot_message"],
                # "output_vars": True,
                'rails': {'input': False, 'output': True, 'retrieval': False, 'dialog': False}
            },
        )
        return {'output_ok': response.output_data['output_check_score']}
    return inner


def refusal_fn(llm: BaseChatModel):
    async def inner(state: AgentState):
        # noinspection PyTypeChecker
        logger.info("---CALL REFUSAL---")
        # messages = state["messages"]
        question = state["question"]
        prompt = f"""
Your task is to generate polite refusal to the user message because he/she violated policies.

User message: {question}

Refusal:
"""
        all_messages = [('user', prompt)]
        response = await llm.ainvoke(all_messages)
        return {
            "messages": [response],
            'generated_question': question,
            'answer': response.content,
            'not_answer': True,
            'not_answer_reason': 'refusal',
            'source_documents': []
        }
    return inner


def select_generate_or_no_answer(state: AgentState):
    if len(state['source_documents']) > 0:
        return PATH_GENERATE
    else:
        logger.info("---NO DOCUMENTS FOUND---")
        return PATH_NO_ANSWER


def select_output_check_fn(config: dict = None):
    threshold = config.get('output_check_threshold', 1)

    def select_output_check_next_or_end(state: AgentState):
        if state['output_check_score'] > threshold:
            return PATH_NEXT
        else:
            logger.info("---OUTPUT CHECK SCORE BELOW THRESHOLD---")
            logger.info(f"Answer: {state['answer']}")
            return PATH_NO_ANSWER

    return select_output_check_next_or_end


def clarify_question_fn(llm: BaseChatModel, config: dict = None, retriever: BaseRetriever = None):
    async def inner(state: AgentState):
        logger.info("---CALL CLARIFY QUESTION---")
        # messages = state["messages"]
        question = state["question"]
        gen_question = state["generated_question"]
        language = config.get('language')
        lang_part = f" in {language.capitalize()}" if language else ""

        prompt = f"""
        As a helpful AI assistant, your task is to create a clarifying response{lang_part} that:
        1. Briefly acknowledges the user's question (in 1 short sentence).
        2. Asks 1-2 specific clarifying questions to better understand what the user is asking for.
        3. Keep the professional tone and polite style.

        """
        # IMPORTANT: Do not explain why the question is unclear or provide lengthy explanations. Focus only on acknowledging and asking clarifying questions.
        if retriever:
            # Get quick docs
            copy_retriever = retriever.model_copy(update={'search_kwargs': {'k': 4}})
            docs = await copy_retriever.ainvoke(gen_question)
            prompt += f"\n\nPossible relevant documentation:\n\n {format_docs(docs)}"

        user_part = f"""
        - User question to clarify: {question}
        - Rephrased question: {gen_question}
        """
        all_messages = [
            ('system' if llm_utils.support_system_message(config) else 'human', prompt),
            ('human', user_part),
        ]
        response = await llm.ainvoke(all_messages)

        return {
            "messages": [response],
            'answer': response.content,
            'not_answer': True,
            'not_answer_reason': 'clarify_question',
        }

    return inner


def no_answer_template_fn(llm: BaseChatModel, config: dict = None):
    async def inner(state: AgentState):
        logger.info("---CALL NO ANSWER---")
        messages = state["messages"]
        question = state["question"]
        gen_question = state["generated_question"]
        tpl = config.get('no_answer_template')
        is_strict = utils.boolean_string(config.get('no_answer_strict'))
        language = config.get('language')
        lang_part = f" in {language.capitalize()}" if language else ""
        # Base prompt that's common to both cases
        base_part = (
            f"As a helpful AI assistant, your task is to create a direct, polite response {lang_part} that:\n"
            f"1. Acknowledges the user's question in a friendly manner\n"
            f"2. Clearly states that the specific information requested is not available in our documentation\n"
            f"3. Suggests a possible clarification or alternative question the user might want to ask\n"
            f"4. Offers to help with related information if they have follow-up questions\n\n"
            f"IMPORTANT: Your response MUST be written in final form ready to be shown directly to the user.\n"
            f"Do NOT include placeholders, variables, or template language. Write as if you are speaking directly to the user.\n"
        )

        # Context part that's common to both cases
        context_part = (
            f"Context:\n"
            f"- User's question: {question}\n"
            f"- User's query (processed): {gen_question}\n"
            f"Craft a helpful, conversational response that encourages further dialogue."
        )

        if tpl:
            prompt = f"{base_part}\n{context_part}\n\nResponse template: {tpl}\n"
        else:
            prompt = f"{base_part}\n{context_part}"

        all_messages = [('user', prompt)]
        if not is_strict:
            response = await llm.ainvoke(all_messages)
        else:
            if config.get('stream'):
                # Handle streaming
                config['stream_queue'].put_nowait({'event_status': 'OK', 'event_type': 'chunk', 'content': tpl})
                config['stream_event'].set()
            response = AIMessage(content=tpl)
        return {
            "messages": [response],
            'answer': response.content,
            'not_answer': True,
        }

    return inner


def regenerate_fn(llm: BaseChatModel, config: dict = None):
    async def inner(state: AgentState):
        # noinspection PyTypeChecker
        logger.info("---CALL REGENERATE---")
        messages = state["messages"]
        prompt = f"""
Your task is to generate a new polite message instead of the last message because of violated policies.
The message should not contain any of these blocked terms: {config.get("output_rail_blocked_terms", [])}

New message:
"""
        all_messages = rails.convert_to_nemorails(messages) + [('assistant', prompt)]
        response = await llm.ainvoke(all_messages)
        return {
            "messages": [response],
            'answer': response.content,
        }
    return inner


def evaluate_input_rail(state: AgentState):
    # noinspection PyTypeChecker
    ok = state['input_ok']
    if ok:
        logger.info(f'---INPUT OK, PROCEEDING NEXT---')
    else:
        logger.info(f'---INPUT NOT OK, GENERATING REFUSAL---')

    return PATH_REWRITE_QUERY if ok else PATH_REFUSAL


def evaluate_output_rail(state: AgentState):
    # noinspection PyTypeChecker
    ok = state['output_ok']
    if ok:
        logger.info(f'---OUTPUT OK, PROCEEDING NEXT---')
    else:
        logger.info(f'---OUTPUT NOT OK, REGENERATING---')

    return END if ok else 'regenerate'


def agent_fn(retriever_tool: Tool, llm: BaseChatModel):
    async def agent(state: AgentState):
        """
        Invokes the agent model to generate a response based on the current state. Given
        the question, it will decide to retrieve using the retriever tool, or simply end.
        Args:
            state (messages): The current state
        Returns:
            dict: The updated state with the agent response appended to messages
        """
        logger.info("---CALL AGENT---")
        messages = state["messages"]
        question = state["question"]
        agent_llm = llm.bind_tools([retriever_tool])
        all_messages = list(messages)
        response = await agent_llm.ainvoke(all_messages)
        return {
            "messages": [response],
            "initial_documents": state["initial_documents"] if "initial_documents" in state else [],
            'generated_question': question,
            'answer': response.content
        }
    return agent


def retrieve_fn(retriever: BaseRetriever, override_search_k=None, concat_by=None):
    if override_search_k:
        search_kwargs = retriever.search_kwargs
        search_kwargs['k'] = override_search_k
        retriever = retriever.model_copy(update={'search_kwargs': search_kwargs})

    def concat_docs(docs: List[Document], concat_by: int, field='source') -> List[Document]:
        docs_by_field = collections.defaultdict(list)
        for doc in docs:
            docs_by_field[doc.metadata[field]].append(doc)

        result = []
        for field_value, field_docs in docs_by_field.items():
            # Split documents into chunks of size concat_by
            for i in range(0, len(field_docs), concat_by):
                chunk_docs = field_docs[i:i + concat_by]

                if len(chunk_docs) == 1:
                    # If only one document in chunk, keep it as is
                    result.append(chunk_docs[0])
                else:
                    # Combine multiple documents in the chunk
                    combined_doc = Document(
                        page_content='\n\n'.join([d.page_content for d in chunk_docs]),
                        metadata=chunk_docs[0].metadata.copy()
                    )
                    # Update metadata to reflect that this is a combined document
                    # combined_doc.metadata['combined_from_count'] = len(chunk_docs)
                    result.append(combined_doc)

        return result

    async def retrieve(state: AgentState):
        """Retrieve documents
        Args:
            state (messages): The current state
        Returns:
             dict: The updated state with documents
        """
        last_message = state["messages"][-1]
        is_from_tool = hasattr(last_message, 'tool_calls')
        if is_from_tool:
            tool_calls = last_message.tool_calls
            tool_call = tool_calls[0]
            query = tool_call['args']['query']
            # logger.info(f"[RETRIEVE] GOT TOOL call query='{query}'")
        else:
            query = state['generated_question']
            # logger.info(f"[RETRIEVE] GOT call query='{query}'")

        docs = await retriever.ainvoke(query)

        for doc in docs:
            logger.info(f'[DOCS] FOUND {doc.metadata["source"]} score={doc.metadata["score"]}')

        if concat_by:
            docs = concat_docs(docs, concat_by)

        # Make it appendable / repeatable
        old_initial_docs = state.get('initial_documents', [])
        result = {
            'initial_documents': old_initial_docs + list(docs),
        }

        if is_from_tool:
            formatted_docs = format_docs(docs)
            response_message = ToolMessage(
                content=formatted_docs,
                tool_call_id=tool_call['id'],
                name=tool_call['name']
            )
            result['messages'] = [response_message]

        return result

    return retrieve


def generate_fn(llm: BaseChatModel, chat_prompt: ChatPromptTemplate):
    async def generate(state: AgentState):
        """Generate answer
        Args:
            state (messages): The current state
        Returns:
             dict: The updated state with answer
        """
        logger.info("---GENERATE---")

        question = state['question']
        gen_question = state.get('generated_question')
        docs = format_docs(state['source_documents'])

        # Check if we have a combined summary from subquery processing
        combined_summary = state.get('combined_summary')
        if combined_summary:
            logger.info("Using combined summary from subquery processing")
            # Add the combined summary to the context
            docs = f"Combined Summary:\n{combined_summary}\n\nRetrieved Documents:\n{docs}"

        if question and gen_question and question != gen_question:
            question = (
                f"Original user question: {question}\n---\n"
                f"Rephrased user question: {gen_question}"
            )
        # TODO take history ??

        prompt = await chat_prompt.aformat_prompt(context=docs, question=question)
        # rag_chain = prompt | llm
        # response = await rag_chain.ainvoke({"context": docs, "question": question})
        response = await llm.ainvoke(prompt)

        return {"messages": [response], 'answer': response.content}

    return generate


def grade_each_fn(llm: BaseChatModel, config: dict):
    async def document_evaluation(state: AgentState):
        """
        Determines whether the retrieved documents are relevant to the question.
        Uses Reciprocal Rank Fusion (RRF) to combine initial document ranking with graded ranking.

        Args:
            state (dict): The current graph state

        Returns:
            state (dict): Updates documents key with filtered and reranked relevant documents
        """
        if not config.get('document_evaluation_enabled', True):
            return {"source_documents": state["initial_documents"]}

        question = state["question"]
        gen_question = state["generated_question"]
        documents = state["initial_documents"]

        # "You are a grader assessing usefulness of a retrieved document content to a user question. \n"
        # "If the document contains keyword(s), semantic meaning related to the question or "
        # "the document contains information for answering a question, grade it as useful.\n"
        # "The goal is to filter out wrong retrievals.\n"
        """
Relevance Score (0 to 10, in increments of 1):
0 = Completely Irrelevant: The block has no connection or relation to the query.
1 = Virtually Irrelevant: Only a very slight or vague connection to the query.
2 = Very Slightly Relevant: Contains an extremely minimal or tangential connection.
3 = Slightly Relevant: Addresses a very small aspect of the query but lacks substantive detail.
4 = Somewhat Relevant: Contains partial information that is somewhat related but not comprehensive.
5 = Moderately Relevant: Addresses the query but with limited or partial relevance.
6 = Fairly Relevant: Provides relevant information, though lacking depth or specificity.
7 = Relevant: Clearly relates to the query, offering substantive but not fully comprehensive information.
8 = Very Relevant: Strongly relates to the query and provides significant information.
9 = Highly Relevant: Almost completely answers the query with detailed and specific information.
10 = Perfectly Relevant: Directly and comprehensively answers the query with all the necessary specific information.
"""

        system = """
You are a relevance‑grader. Example you receive will have two clearly‑labeled parts:

User Question:
Original: The single, canonical question the user asked.
(Optional) Rephrased: The rephrased version of the original question based on the context.

Retrieved Document:
[metadata key: value]
[The document’s content] (which may itself include internal “Question client” headings).

Your task:
Assign a Relevance Score from 0 (Completely Irrelevant) to 10 (Perfectly Relevant), in whole numbers,
based only on whether the Retrieved Document contains information that would directly
help you construct an answer to the User Question.

Important:
 - Do not treat any references to "Question client" or similar headings inside the document as the User Question—they belong to the document’s internal Q&A and should be ignored.
 - Focus on whether the content supplies facts, procedures, or details that you would actually integrate into an answer to the User Question.

Usefulness Score Guidelines:
0 = Not useful: The content offers no information relevant to answering the question.
1 = Almost no utility: Only a trivial or very indirect connection.
2 = Very little utility: Barely any applicable info—only tangential.
3 = Limited utility: Mentions something related but lacks substantive answer content.
4 = Some utility: Provides a small detail that could partially inform an answer, but insufficient.
5 = Moderate utility: Contains at least one fact or instruction relevant to answering the question, but incomplete.
6 = Fair utility: Offers useful information but missing important details or context.
7 = Good utility: Clearly supplies substantive information useful in an answer, yet not fully comprehensive.
8 = Strong utility: Contains significant, detailed information that would largely satisfy the answer.
9 = Very high utility: Almost provides a complete answer with specific details.
10 = Perfect utility: Directly and comprehensively provides all necessary information to fully answer the question.
"""
        human_msg = "User question:\nOriginal: {question}"
        if question != gen_question:
            human_msg += "\nRephrased: {generated_question}"

        human_msg += "\n\nRetrieved document:\n\n{document}\n\n"

        support_system = llm_utils.support_system_message(config)
        grade_prompt = ChatPromptTemplate.from_messages(
            [
                ("system" if support_system else "human", system),
                ("human", human_msg),
            ]
        )

        # retrieval_grader2 = grade_prompt | llm
        retrieval_grader = grade_prompt | llm.with_structured_output(Grade)

        # Score each doc
        source_documents = []
        async_tasks = []
        metadata_keys = []
        for d in documents:
            invoke_dict = {"question": question, "document": format_doc(d, include_only=metadata_keys)}
            if question != gen_question:
                invoke_dict['generated_question'] = gen_question

            async_tasks.append(retrieval_grader.ainvoke(invoke_dict))
            # score = await retrieval_grader.ainvoke(invoke_dict)

        scores = await asyncio.gather(*async_tasks)
        score_threshold = int(config.get('doc_score_threshold', 5))

        # Create a list of documents that meet the threshold
        graded_docs = []
        for score, doc in zip(scores, documents):
            if score.score > score_threshold:
                logger.info(
                    f"---GRADE: DOC RELEVANT [name={doc.metadata['source']} "
                    f"page={doc.metadata.get('page')} answer={score.dict()}]---"
                )
                # Store the grade score in the document metadata
                doc.metadata['grade_score'] = score.score
                graded_docs.append(doc)
            else:
                logger.info(
                    f"---GRADE: DOC NOT RELEVANT [name={doc.metadata['source']} "
                    f"page={doc.metadata.get('page')} answer={score.dict()}]---"
                )

        # If no documents meet the threshold, return empty list
        if not graded_docs:
            return {
                "source_documents": [],
                "not_answer_reason": "no_relevant_docs",
            }

        # Return the graded documents for the reranking node
        return {
            "source_documents": graded_docs,
            "not_answer_reason": None,
        }

    return document_evaluation


def batch_grade_documents_fn(llm: BaseChatModel, config: dict):
    """Create a function that grades documents in batches for improved efficiency."""
    async def batch_document_evaluation(state: AgentState):
        """
        Determines whether the retrieved documents are relevant to the question using batch grading.
        Processes multiple documents in batches using async tasks and asyncio.gather for optimal performance.

        This follows the same async pattern as grade_each_fn but processes documents in batches
        to reduce the total number of LLM calls while maintaining concurrent execution.

        Args:
            state (dict): The current graph state

        Returns:
            state (dict): Updates documents key with filtered and reranked relevant documents
        """
        if not config.get('document_evaluation_enabled', True):
            return {"source_documents": state["initial_documents"]}

        question = state["question"]
        gen_question = state["generated_question"]
        documents = state["initial_documents"]

        if not documents:
            logger.info("No documents to grade")
            return {
                "source_documents": [],
                "not_answer_reason": "no_relevant_docs",
            }

        # Get batch size from config (default 3 for optimal balance)
        batch_size = int(config.get('batch_grading_size') or 3)

        logger.info(f"---BATCH DOCUMENT GRADING: Processing {len(documents)} documents in batches of {batch_size}---")

        # Create the grading prompt system message
        system = """
You are a relevance‑grader. You will receive a user question and a batch of retrieved documents.

Your task:
For each document in the batch, assign a Relevance Score from 0 (Completely Irrelevant) to 10 (Perfectly Relevant), in whole numbers,
based only on whether the Retrieved Document contains information that would directly
help you construct an answer to the User Question.

Important:
 - Do not treat any references to "Question client" or similar headings inside the document as the User Question—they belong to the document’s internal Q&A and should be ignored.
 - Focus on whether the content supplies facts, procedures, or details that you would actually integrate into an answer to the User Question.

Usefulness Score Guidelines:
0 = Not useful: The content offers no information relevant to answering the question.
1 = Almost no utility: Only a trivial or very indirect connection.
2 = Very little utility: Barely any applicable info—only tangential.
3 = Limited utility: Mentions something related but lacks substantive answer content.
4 = Some utility: Provides a small detail that could partially inform an answer, but insufficient.
5 = Moderate utility: Contains at least one fact or instruction relevant to answering the question, but incomplete.
6 = Fair utility: Offers useful information but missing important details or context.
7 = Good utility: Clearly supplies substantive information useful in an answer, yet not fully comprehensive.
8 = Strong utility: Contains significant, detailed information that would largely satisfy the answer.
9 = Very high utility: Almost provides a complete answer with specific details.
10 = Perfect utility: Directly and comprehensively provides all necessary information to fully answer the question.
"""

        # Prepare human message template
        human_msg_template = "User question:\nOriginal: {question}"
        if question != gen_question:
            human_msg_template += "\nRephrased: {generated_question}"

        human_msg_template += "\n\nDocuments to grade:\n\n{documents_to_grade}"

        support_system = llm_utils.support_system_message(config)

        # Create prompt template
        grade_prompt = ChatPromptTemplate.from_messages([
            ("system" if support_system else "human", system),
            ("human", human_msg_template),
        ])

        # Create the retrieval grader
        retrieval_grader = grade_prompt | llm.with_structured_output(BatchGrade)

        # Prepare batches and async tasks (following grade_each_fn pattern)
        async_tasks = []
        batch_metadata = []
        metadata_keys = []

        for batch_start in range(0, len(documents), batch_size):
            batch_end = min(batch_start + batch_size, len(documents))
            batch_docs = documents[batch_start:batch_end]

            # Build the batch documents text
            batch_docs_text = ""
            for i, doc in enumerate(batch_docs):
                doc_text = format_doc(doc, include_only=metadata_keys)
                batch_docs_text += f"Document {i}:\n{doc_text}\n\n"

            # Create invoke dict for this batch
            invoke_dict = {
                "question": question,
                "documents_to_grade": batch_docs_text
            }
            if question != gen_question:
                invoke_dict['generated_question'] = gen_question

            # Add the grading task to our list
            async_tasks.append(retrieval_grader.ainvoke(invoke_dict))
            batch_metadata.append({
                'batch_start': batch_start,
                'batch_end': batch_end,
                'batch_docs': batch_docs
            })

        # Execute all batch grading tasks concurrently using asyncio.gather
        batch_results = await asyncio.gather(*async_tasks)

        # Process all batch results
        all_graded_docs = []
        score_threshold = int(config.get('doc_score_threshold', 5))

        for batch_result, batch_meta in zip(batch_results, batch_metadata):
            batch_docs = batch_meta['batch_docs']

            for doc_grade in batch_result.document_grades:
                if doc_grade.document_index < len(batch_docs):
                    doc = batch_docs[doc_grade.document_index]

                    if doc_grade.score > score_threshold:
                        logger.info(
                            f"---BATCH GRADE: DOC RELEVANT [name={doc.metadata['source']} "
                            f"page={doc.metadata.get('page')} score={doc_grade.score} reasoning={doc_grade.reasoning}]---"
                        )
                        # Store the grade score in the document metadata
                        doc.metadata['grade_score'] = doc_grade.score
                        all_graded_docs.append(doc)
                    else:
                        logger.info(
                            f"---BATCH GRADE: DOC NOT RELEVANT [name={doc.metadata['source']} "
                            f"page={doc.metadata.get('page')} score={doc_grade.score} reasoning={doc_grade.reasoning}]---"
                        )
                else:
                    logger.warning(f"Invalid document index {doc_grade.document_index} in batch of size {len(batch_docs)}")

        logger.info(f"---BATCH GRADING COMPLETE: {len(all_graded_docs)} relevant documents out of {len(documents)} total---")

        # If no documents meet the threshold, return empty list
        if not all_graded_docs:
            return {
                "source_documents": [],
                "not_answer_reason": "no_relevant_docs",
            }

        # Return the graded documents for the reranking node
        return {
            "source_documents": all_graded_docs,
            "not_answer_reason": None,
        }

    return batch_document_evaluation


def rerank_docs_fn(config: dict = None):
    """
    Creates a function that reranks documents using Reciprocal Rank Fusion (RRF).
    """
    async def rerank_documents(state: AgentState):
        """
        Reranks documents using Reciprocal Rank Fusion (RRF).

        Args:
            state: The current state with graded documents

        Returns:
            Updated state with reranked documents
        """
        graded_docs = state["source_documents"]

        # If no documents to rerank, return the state as is
        if not graded_docs:
            return state

        # Get the RRF k constant from config
        rrf_k = int(config.get('rrf_k', 10))  # Using k=10 for more distinguishable scores

        # Use our RRF implementation to rerank the documents
        reranked_docs = rrf.apply_rrf_reranking(
            documents=graded_docs,
            rrf_k=rrf_k
        )

        logger.info(f"---RRF RERANKING: Reordered {len(reranked_docs)} documents---")

        return {
            "source_documents": reranked_docs,
        }

    return rerank_documents


def rewrite_fn(llm: BaseChatModel):
    def rewrite(state: AgentState):
        """Transform the query to produce a better question.
        Args:
            state (messages): The current state
        Returns:
            dict: The updated state with re-phrased question
        """

        question = state["question"]

        msg = [
            HumanMessage(
                content=(
                    "\n"
                    "Look at the input and try to reason about the underlying semantic intent / meaning. \n"
                    "Here is the initial question:"
                    "\n ------- \n"
                    f"{question}"
                    "\n ------- \n"
                    "An improved question: "
                )
            )
        ]

        # Grader
        response = llm.invoke(msg)
        logger.info(f"[REWRITE] Response: {response.content}")
        return {"messages": [response], "rewritten": True}

    return rewrite


def setup_llm_models(llm: BaseChatModel, config: dict = None):
    """Initialize different LLM models based on configuration."""
    # For agent and generation, use the specified LLM
    # For other else - use the cheapest/fastest possible LLM
    fast_tool_llm = set_cheapest_model(llm, custom_model=config.get('utility_model'))
    tool_model = config.get('tool_model') or llm_utils.get_tool_model(llm)
    tool_llm = set_cheapest_model(llm, custom_model=tool_model)
    return fast_tool_llm, tool_llm


def get_default_topic_groups():
    """Get the default topic groups configuration.

    Returns:
        A dictionary with default topic groups configuration
    """
    return {
        "call_me_back": {
            "path": PATH_NO_ANSWER,
            "topics": ["direct callback request only", "just call me back", "only need contact", "callback with no other questions"]
        },
        "unclear_question": {
            "path": PATH_CLARIFY,
            "topics": ["unclear question", "not understandable", "ambiguous request", "vague question"]
        },
    }


def get_resolved_topic_groups(config: dict):
    """Get the resolved topic groups configuration.

    Args:
        config: Configuration dictionary

    Returns:
        A dictionary with resolved topic groups configuration
    """
    default_topic_groups = get_default_topic_groups()
    topic_groups = config.get('topic_router_groups')
    if not topic_groups:
        return default_topic_groups
    return json_utils.dict_merge(default_topic_groups, topic_groups)


def get_topic_router_routes(workflow: StateGraph, config: dict, default_next_node: str):
    """Create a route mapping for the topic router.

    Args:
        workflow: The workflow graph
        config: Configuration dictionary
        default_next_node: The default node to route to when path is 'next'

    Returns:
        A dictionary mapping path values to target nodes
    """
    topic_groups = get_resolved_topic_groups(config)

    route_mapping = {PATH_NEXT: default_next_node}

    # Extract paths from the topic groups
    for group_name, group_data in topic_groups.items():
        path = group_data.get('path')
        if path and path not in route_mapping:
            # Only add routes that point to valid nodes
            if path in workflow.nodes:
                route_mapping[path] = path
            else:
                logger.warning(f"Invalid route '{path}' in topic group '{group_name}'")
                raise ValueError(f"Invalid route '{path}' in topic group '{group_name}'")

    return route_mapping


def setup_caching(workflow: StateGraph, config: dict = None) -> str:
    """Setup caching for the workflow.

    Args:
        workflow: The workflow graph
        config: Configuration dictionary
    """
    cache_enabled = config.get('cache_enabled', False)
    cache_ttl_days = int(config.get('cache_ttl_days') or 30)

    if not cache_enabled:
        return START

    async def check_cache(state: AgentState):
        logger.info("---CHECK CACHE---")
        user_question = state['question']
        cached_message_key = utils.hash_sha256(user_question)
        cached_message = await db_api.get_message_cached_by_hash(
            hash=cached_message_key, dataset_id=state['dataset_id'], org_id=state['org_id']
        )
        if cached_message:
            logger.info(f"---CACHE HIT: {cached_message.content}---")
            # Update expiry time
            await db_api.update_message_cached(
                cached_message, {'expires_at': base.now() + datetime.timedelta(days=cache_ttl_days)}
            )
            context = cached_message.context or {}
            context.pop('cache_hit', None)
            source_documents = context.pop('source_documents', [])

            await db_api.delete_messages_cached_expired()

            return {
                'cache_hit': True,
                'generated_question': cached_message.content,
                'answer': cached_message.output,
                'source_documents': [Document.model_validate(d) for d in source_documents],
            } | context

        await db_api.delete_messages_cached_expired()
        return {'cache_hit': False}

    async def cache_checked(state: AgentState):
        return {}

    workflow.add_node(PATH_CACHE_CHECK, check_cache)
    workflow.add_node(PATH_CACHE_CHECKED, cache_checked)
    workflow.add_edge(START, PATH_CACHE_CHECK)
    workflow.add_conditional_edges(
        PATH_CACHE_CHECK,
        lambda state: state['cache_hit'],
        {True: END, False: PATH_CACHE_CHECKED}
    )
    return PATH_CACHE_CHECKED


def setup_advanced_routing(
    workflow: StateGraph,
    tool_llm: BaseChatModel,
    config: dict = None,
    default_next_node=None,
    default_start_node=START,
    input_rail_enabled=False
):
    """Setup the advanced routing nodes and edges with parallel preprocessing.

    This function sets up parallel execution of excluded topics check and query rewriting
    for optimized performance. Both operations run concurrently and LangGraph automatically
    merges their results before proceeding to the topic router.

    Args:
        workflow: The workflow graph
        tool_llm: The language model to use for routing
        config: Configuration dictionary
        default_next_node: The default node to route to when path is 'next'. If None, uses
                          PATH_VECTORSTORE or PATH_ANSWERS_VECTORSTORE based on config.
        default_start_node: The default start node. START by default.
        input_rail_enabled: Whether input guardrails are enabled
    """
    # Add both nodes that will run in parallel
    workflow.add_node(PATH_EXCLUDED_TOPICS, exclude_topics_node_fn(tool_llm, config))
    workflow.add_node(PATH_REWRITE_QUERY, rewrite_query_fn(tool_llm, config))

    # Add topic router node
    workflow.add_node(PATH_TOPIC_ROUTER, topic_router_node_fn(tool_llm, config))

    # Create a pass-through node to handle conditional logic after parallel execution
    async def check_no_answer_after_parallel(state: AgentState):
        # This node just passes through the state after parallel execution
        # LangGraph automatically merges state from both parallel nodes
        return {}

    workflow.add_node("check_no_answer", check_no_answer_after_parallel)

    # Setup entry point based on input rail configuration
    if input_rail_enabled:
        # START → INPUT_RAIL → (if OK, trigger parallel preprocessing) → CHECK → TOPIC_ROUTER
        workflow.add_edge(default_start_node, PATH_INPUT_RAIL)

        # Create a dispatcher node that triggers parallel execution
        async def dispatch_to_parallel(state: AgentState):
            # This node just passes through the state to trigger parallel execution
            return {}

        workflow.add_node("dispatch_parallel", dispatch_to_parallel)
        workflow.add_conditional_edges(
            PATH_INPUT_RAIL,
            evaluate_input_rail,
            {PATH_NEXT: "dispatch_parallel", PATH_REFUSAL: PATH_REFUSAL},
        )

        # From dispatcher, go to both parallel nodes
        workflow.add_edge("dispatch_parallel", PATH_EXCLUDED_TOPICS)
        workflow.add_edge("dispatch_parallel", PATH_REWRITE_QUERY)
    else:
        # START → (parallel preprocessing) → CHECK → TOPIC_ROUTER
        workflow.add_edge(default_start_node, PATH_EXCLUDED_TOPICS)
        workflow.add_edge(default_start_node, PATH_REWRITE_QUERY)

    # Both parallel nodes connect to the decision point (common for both cases)
    workflow.add_edge(PATH_EXCLUDED_TOPICS, "check_no_answer")
    workflow.add_edge(PATH_REWRITE_QUERY, "check_no_answer")

    # From the check node, either proceed to topic router or go to no_answer
    workflow.add_conditional_edges(
        "check_no_answer",
        decide_no_answer,
        {PATH_NEXT: PATH_TOPIC_ROUTER, PATH_NO_ANSWER: PATH_NO_ANSWER},
    )

    # Determine the default next node based on configuration if not provided
    if default_next_node is None:
        default_next_node = "dispatch_retrieval"

    # Get route mapping for the topic router
    route_mapping = get_topic_router_routes(workflow, config, default_next_node)

    # Add conditional edges from topic router to next nodes based on router's decision
    workflow.add_conditional_edges(
        PATH_TOPIC_ROUTER,
        lambda state: state['path'],  # Use path from topic router result
        route_mapping
    )


def setup_input_guardrails(
    workflow: StateGraph, cheap_fast_llm: BaseChatModel,
    main_llm: BaseChatModel, config: dict = None
):
    """Setup input guardrails if enabled."""
    input_rail_enabled = config.get('input_rail_enabled', False)

    if input_rail_enabled:
        # Add input rail and refusal nodes
        workflow.add_node(PATH_INPUT_RAIL, input_guardrails_fn(cheap_fast_llm))
        workflow.add_node(PATH_REFUSAL, refusal_fn(main_llm))
        workflow.add_edge(PATH_REFUSAL, END)

        # Input rail will be connected to the parallel preprocessing in setup_advanced_routing
        # This is handled there to avoid circular dependencies

    # Make sure PATH_NO_ANSWER has a node definition before it's used
    if PATH_NO_ANSWER not in workflow.nodes:
        workflow.add_node(PATH_NO_ANSWER, no_answer_template_fn(main_llm, config))
        workflow.add_edge(PATH_NO_ANSWER, END)

    return input_rail_enabled


def setup_vectorstore_retrieval(
    workflow: StateGraph,
    retriever: BaseRetriever,
    config: dict,
):
    """Setup the vectorstore retrieval nodes and edges."""
    answers_retriever = config.get('answers_retriever')

    async def merge_passthrough(state: AgentState):
        return {}

    workflow.add_node("merge_retrieval", merge_passthrough)
    workflow.add_node("dispatch_retrieval", merge_passthrough)

    if answers_retriever:
        search_k = answers_retriever.search_kwargs.get('k')
        workflow.add_node(PATH_ANSWERS_VECTORSTORE, retrieve_fn(answers_retriever, override_search_k=search_k * 2, concat_by=2))
        workflow.add_edge("dispatch_retrieval", PATH_ANSWERS_VECTORSTORE)
        workflow.add_edge(PATH_ANSWERS_VECTORSTORE, "merge_retrieval")

    # NO tool node here for better output/state/documents control
    workflow.add_node(PATH_VECTORSTORE, retrieve_fn(retriever))
    workflow.add_edge("dispatch_retrieval", PATH_VECTORSTORE)
    workflow.add_edge(PATH_VECTORSTORE, "merge_retrieval")

    return answers_retriever, "merge_retrieval"


def setup_document_processing(
    workflow: StateGraph,
    cheap_fast_llm: BaseChatModel,
    main_llm: BaseChatModel,
    chat_prompt: ChatPromptTemplate,
    retrieval_end_node: str,
    config: dict = None
):
    """Setup document processing, grading, reranking, and generation nodes."""
    workflow.add_node(PATH_GENERATE, generate_fn(main_llm, chat_prompt))

    # Choose between batch grading and individual grading based on config
    use_batch_grading = config.get('batch_grading_enabled', True)
    if use_batch_grading:
        workflow.add_node(PATH_GRADE_DOCS, batch_grade_documents_fn(cheap_fast_llm, config))
    else:
        workflow.add_node(PATH_GRADE_DOCS, grade_each_fn(cheap_fast_llm, config))

    workflow.add_node(PATH_RERANK_DOCS, rerank_docs_fn(config))
    # workflow.add_node(PATH_NO_ANSWER, no_answer_template_fn(main_llm, config))
    workflow.add_node(PATH_CLARIFY, clarify_question_fn(main_llm, config))
    workflow.add_edge(PATH_CLARIFY, END)

    workflow.add_edge(retrieval_end_node, PATH_GRADE_DOCS)

    # Add edge from grading to reranking
    workflow.add_edge(PATH_GRADE_DOCS, PATH_RERANK_DOCS)

    # Add conditional edges from reranking to generate or no_answer
    workflow.add_conditional_edges(
        PATH_RERANK_DOCS,
        select_generate_or_no_answer,
        {PATH_GENERATE: PATH_GENERATE, PATH_NO_ANSWER: PATH_NO_ANSWER}
    )
    workflow.add_edge(PATH_NO_ANSWER, END)

    return PATH_GENERATE


def setup_output_processing(
    workflow: StateGraph, cheap_fast_llm: BaseChatModel,
    llm: BaseChatModel, last_node: str, config: dict = None
):
    """Setup output checking and guardrails if enabled."""
    output_check_enabled = config.get('output_check_enabled', False)
    output_rail_enabled = config.get('output_rail_enabled', False)

    if output_check_enabled:
        workflow.add_node("output_check", output_check_fn(cheap_fast_llm, config))
        # Goes 'generate' -> 'output_check' -> pre_output_node
        workflow.add_edge(last_node, "output_check")
        last_node = 'output_check'
        workflow.add_conditional_edges(
            "output_check",
            select_output_check_fn(config),
            {PATH_NEXT: 'output_rail' if output_rail_enabled else END, PATH_NO_ANSWER: PATH_NO_ANSWER}
        )

    if output_rail_enabled:
        workflow.add_node("output_rail", output_guardrails_fn(cheap_fast_llm, config))
        # Goes to 'output_rail' -> END or 'regenerate' -> END
        if not output_check_enabled:
            workflow.add_edge(last_node, "output_rail")
        workflow.add_node("regenerate", regenerate_fn(llm, config))
        workflow.add_conditional_edges(
            "output_rail",
            evaluate_output_rail,
            {END: END, 'regenerate': 'regenerate'}
        )
        workflow.add_edge("regenerate", END)
        last_node = 'output_rail'

    return last_node


def compile_graph(workflow: StateGraph, last_node: str):
    """Compile the final graph."""
    # Goes to END
    workflow.add_edge(last_node, END)

    graph = workflow.compile(
        checkpointer=None,
        interrupt_before=None,
        interrupt_after=None,
    )
    # draw mermaid graph
    # graph.get_graph().draw_png('graph.png')
    return graph


def get_graph(
    retriever,
    llm: BaseChatModel,
    chat_prompt: ChatPromptTemplate,
    streaming_llm: BaseChatModel | None = None,
    config: dict = None,
):
    """Create a workflow graph for the chat agent.

    Args:
        retriever: The retriever to use for document retrieval
        llm: The language model to use
        chat_prompt: The prompt template for chat generation
        streaming_llm: Optional streaming language model
        config: Configuration dictionary

    Returns:
        A compiled workflow graph
    """
    workflow = StateGraph(AgentState)
    main_llm = streaming_llm or llm

    # Setup LLM models
    fast_tool_llm, tool_llm = setup_llm_models(llm, config)

    # Setup input guardrails
    input_rail_enabled = setup_input_guardrails(workflow, fast_tool_llm, main_llm, config)

    # Setup vectorstore retrieval
    answers_retriever, retrieval_end_node = setup_vectorstore_retrieval(workflow, retriever, config)

    # Setup document processing
    last_node = setup_document_processing(
        workflow, fast_tool_llm, main_llm, chat_prompt, retrieval_end_node, config
    )

    # Setup caching
    start_node = setup_caching(workflow, config)

    # Setup advanced routing
    setup_advanced_routing(
        workflow, tool_llm, config,
        input_rail_enabled=input_rail_enabled,
        default_start_node=start_node,
        default_next_node="dispatch_retrieval"
    )

    # Setup output processing
    # Only enable output processing if not streaming
    if streaming_llm is None:
        last_node = setup_output_processing(workflow, fast_tool_llm, llm, last_node, config)

    # Compile and return graph
    graph = compile_graph(workflow, last_node)

    return graph


def format_docs(docs: list[Document]):
    return "\n\n".join(format_doc(d) for d in docs)


def format_doc(doc: Document, include_only: list = None) -> str:
    return qdrant_vs.format_doc(doc.page_content, doc.metadata, include_only=include_only)


def topic_router_node_fn(llm: BaseChatModel, config: dict = None):
    """
    Creates a node that routes user queries based on configured topic groups.

    The router analyzes the query and determines if it belongs to any of the
    configured topic groups (like "call back", "unclear question", etc.).
    Based on the match, it routes to the appropriate next node.

    Args:
        llm: The language model to use for routing
        config: Configuration dictionary containing topic groups
              - topic_groups: Dict mapping group names to objects with 'path' and 'topics' keys
                Example:
                {
                    "call_back": {
                        "path": "no_answer",
                        "topics": ["callback request", "call me back"]
                    }
                }

    Returns:
        A function that performs topic routing
    """
    async def topic_router_node(state: AgentState):
        enabled = config.get('topic_router_enabled', False)
        if not enabled:
            return {'path': 'next'}
        logger.info("---TOPIC ROUTER---")

        topic_groups = get_resolved_topic_groups(config)

        # Default route for unmatched topics
        default_route = config.get('topic_route_default', "next")

        # Get both original and generated questions
        original_question = state['question']
        generated_question = state.get('generated_question', original_question)

        # System prompt for the router
        system_prompt = (
            "You are an expert at analyzing user queries and categorizing them into predefined topic groups. "
            "Analyze the user's query and determine if it belongs to any of the defined topic groups. "
            "Each topic group contains a list of specific topics. Match the query to the most relevant topic "
            "and provide a confidence score and routing decision.\n\n"
            "IMPORTANT INSTRUCTIONS FOR call_me_back TOPIC GROUP:\n"
            "- Only categorize a query as call_me_back if the MAIN PURPOSE of the message is to request a callback "
            "  with almost nothing else to answer.\n"
            "- If the query contains a callback request BUT ALSO includes a substantive question that can be answered, "
            "  DO NOT categorize it as call_me_back. Instead, categorize it based on the question content.\n"
            "- The call_me_back category should ONLY be used when there is no significant information to provide "
            "  in response other than acknowledging the callback request.\n\n"
            "Topic Groups:\n\n"
        )

        # Create a simplified topic groups dictionary for the LLM prompt
        simplified_topic_groups = {}
        group_routes = {}

        # Extract topics and paths from the new structure
        for group_name, group_data in topic_groups.items():
            topics = group_data.get('topics', [])
            simplified_topic_groups[group_name] = topics

            # Store the path for this group
            if 'path' in group_data:
                group_routes[group_name] = group_data['path']

        # Add each topic group to the prompt
        for group_name, topics in simplified_topic_groups.items():
            system_prompt += f'GROUP "{group_name}":\n- {"\n- ".join(topics)}\n\n'

        # Create routing guidelines based on configured routes
        routing_guidelines = "\nRouting Guidelines:\n"
        for group_name, route in group_routes.items():
            routing_guidelines += f'- If the query is about "{group_name}", route to "{route}"\n'

        # Add the default route for any unmatched topics
        routing_guidelines += f'- For any other query, route to "{default_route}"\n'

        system_prompt += routing_guidelines

        # Add both original and generated questions to the prompt
        user_query = f"Question: {original_question}\n"
        if original_question != generated_question:
            user_query += f"Rephrased question: {generated_question}"

        messages = [
            ("system", system_prompt),
            ("human", user_query),
        ]

        # Use structured output to get routing decision
        structured_llm_router = llm.with_structured_output(TopicGroupRouter)
        # Invoke the LLM to get routing decision
        route_result: TopicGroupRouter = await structured_llm_router.ainvoke(messages)

        # Get the configured route for the matched group
        matched_group = route_result.matched_group

        # Get the path from the group configuration
        group_config = topic_groups.get(matched_group, {})
        if isinstance(group_config, dict) and 'path' in group_config:
            configured_route = group_config['path']
        else:
            # Fallback to the default route if the group or path is not found
            configured_route = default_route

        logger.info(
            f"TOPIC ROUTING RESULT: group={matched_group}, "
            f"topic={route_result.matched_topic}, confidence={route_result.confidence}, "
            f"route_to={route_result.route_to}, configured_route={configured_route}"
        )

        # Use the configured route from the topic group
        # This ensures we're using the routes from the config
        final_route = configured_route

        # Update state with routing information
        return {
            'path': final_route,
            'topic_group': matched_group,
            'topic': route_result.matched_topic,
            'topic_confidence': route_result.confidence,
            'not_answer': final_route == PATH_NO_ANSWER,
            'not_answer_reason': f"topic_group_{matched_group}" if final_route == PATH_NO_ANSWER else None
        }

    return topic_router_node
