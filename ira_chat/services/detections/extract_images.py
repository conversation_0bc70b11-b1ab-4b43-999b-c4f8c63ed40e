import io
import logging
import os
import tempfile

import cv2
import glymur
import numpy as np
import pymupdf
from PIL import Image

logger = logging.getLogger(__name__)

dimlimit = 10  # each image side must be greater than this
relsize = 0.01  # image : pixmap size ratio must be larger than this (5%)
abssize = 2048  # absolute image size limit 2 KB: ignore if smaller


def recoverpix(doc, x, imgdict):
    """Return pixmap for item with an image mask."""
    s = imgdict["smask"]  # xref of its image mask

    try:
        pix0 = pymupdf.Pixmap(imgdict["image"])
        mask_dict = doc.extract_image(s)
        mask = pymupdf.Pixmap(mask_dict["image"])

        # Align mask and image size
        if mask.height != pix0.height or mask.width != pix0.width:
            mask_im = cv2.imdecode(np.frombuffer(mask.tobytes(), dtype=np.uint8), cv2.IMREAD_GRAYSCALE)
            mask_im = cv2.resize(mask_im, [pix0.width, pix0.height])
            _, mask_np = cv2.imencode(f'.{mask_dict["ext"]}', mask_im)
            mask = pymupdf.Pixmap(mask_np.tobytes())

        # Convert CMYK to RGB to avoid CMYK + alpha
        if pix0.colorspace.name == pymupdf.csCMYK.name:
            jpg = pix0.tobytes('jpg')
            buf = io.BytesIO()
            Image.open(io.BytesIO(jpg)).convert('RGB').save(buf, 'JPEG')
            pix0 = pymupdf.Pixmap(buf.getvalue())

        pix = pymupdf.Pixmap(pix0, mask)

        # if pix0.n > 3:
        #     webp = pix.pil_tobytes('webp')
        #     np_arr = np.ndarray([pix.h, pix.w, 4], dtype=np.uint8, buffer=pix.samples_mv)
        #
        #     webp = cv2.imdecode(np.frombuffer(dtype=np.uint8, buffer=webp), cv2.IMREAD_COLOR)
        #     pix = pymupdf.Pixmap(pix, pymupdf.csRGB)
        #     return {"ext": 'png', "colorspace": pix.colorspace, "image": pix.tobytes()}
        # else:
        ext = "png"
        return {"ext": ext, "colorspace": pix.colorspace.n, "image": pix.tobytes(ext)}
    except:
        return None


def extract_images_from_page(doc, imgdir, page, seen_xrefs: set = None):

    fpref = "img"
    img_ocnt = 0
    img_icnt = 0
    len_xref = doc.xref_length()  # PDF object count - do not use entry 0

    # t0 = time.time()  # start the timer

    smasks = set()  # stores xrefs of /SMask objects
    imgs = {}

    if not os.path.exists(imgdir):
        os.mkdir(imgdir)

    # counter = 0
    seen_result = set()
    for xref in range(1, len_xref):  # scan through all PDF objects
        if seen_xrefs and xref in seen_xrefs:
            continue
        if doc.xref_get_key(xref, "Subtype")[1] != "/Image":  # not an image
            seen_result.add(xref)
            continue
        if xref in smasks:  # ignore smask
            seen_result.add(xref)
            continue

        imgrect = page.get_image_rects(xref)
        if not imgrect:
            # Image probably is not on this page; ignore
            continue

        imgdict = doc.extract_image(xref)
        seen_result.add(xref)
        if not imgdict:  # not an image ?
            continue
        img_icnt += 1  # increase read images counter

        smask = imgdict["smask"]
        if smask > 0:  # store /SMask xref
            smasks.add(smask)

        width = imgdict["width"]
        height = imgdict["height"]
        ext = imgdict["ext"]

        if min(width, height) <= dimlimit:  # rectangle edges too small
            continue

        imgdata = imgdict["image"]  # image data
        l_imgdata = len(imgdata)  # length of data
        # if l_imgdata <= abssize:  # image too small to be relevant
        #     continue
        if smask > 0:  # has smask: need use pixmaps
            imgdict_smask = recoverpix(doc, xref, imgdict)  # create pix with mask applied
            if imgdict_smask is None:  # something went wrong
                continue
            ext = "png"
            imgdata = imgdict_smask["image"]
            l_samples = width * height * 3
            l_imgdata = len(imgdata)
        else:
            c_space = max(1, imgdict["colorspace"])  # get the colorspace n
            l_samples = width * height * c_space  # simulated samples size
            # Convert CMYK to RGB to avoid CMYK + alpha
            if c_space == pymupdf.csCMYK.n:
                pix = pymupdf.Pixmap(imgdata)
                jpg = pix.tobytes('jpg')
                buf = io.BytesIO()
                Image.open(io.BytesIO(jpg)).save(buf, 'JPEG')
                imgdata = buf.getvalue()
                ext = 'jpg'

        if l_imgdata / l_samples <= relsize:  # seems to be unicolor image
            logger.info(f'delete xref {xref}')
            continue

        # now we have an image worthwhile dealing with
        img_ocnt += 1

        if ext == 'jpx':
            # Convert jpeg2000 format
            tmp = tempfile.mktemp(suffix='.jpx')
            with open(tmp, 'wb') as f:
                f.write(imgdata)
            jp2k = glymur.Jp2k(tmp)
            img = Image.fromarray(jp2k[:])
            os.remove(tmp)
            saved = io.BytesIO()
            img.save(saved, 'JPEG')
            imgdata = saved.getvalue()
            ext = 'jpg'

        img_file_base = f"{fpref}-{xref}.{ext}"
        img_name = os.path.join(imgdir, img_file_base)
        ofile = open(img_name, "wb")
        ofile.write(imgdata)
        ofile.close()
        imgs[img_name] = (img_name, imgrect, page.number)

    # now delete any /SMask files not filtered out before
    removed = 0
    if len(smasks) > 0:
        imgdir_ls = os.listdir(imgdir)
        for smask in smasks:
            img_file_base = f"{fpref}-{smask}"
            for f in imgdir_ls:
                if f.startswith(img_file_base):
                    img_name = os.path.join(imgdir, f)
                    os.remove(img_name)
                    if img_name in imgs:
                        del imgs[img_name]
                    removed += 1

    # t1 = time.time()

    # logger.info(f"{img_icnt} found images")
    # logger.info(f"{img_ocnt - removed} extracted images")
    # logger.info(f"{len(smasks)} skipped smasks")
    # logger.info(f"{removed} removed smasks")
    # logger.info(f"total time {t1 - t0} sec")

    return list(imgs.values()), seen_result


def _reencode_to_rgb(cmyk, ext):
    decoded = cv2.imdecode(np.frombuffer(cmyk.tobytes(), dtype=np.uint8), cv2.IMREAD_COLOR)
    decoded = cv2.cvtColor(decoded, cv2.COLOR_)