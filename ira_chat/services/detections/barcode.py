import json
import logging
import os
import time
import typing
import zipfile
from typing import BinaryIO, Literal

import cv2
import numpy as np
import pydantic
import pymupdf
from pymupdf import IRect

try:
    from pyzbar import pyzbar, locations
except ImportError:
    pyzbar = None
    locations = None

from ira_chat.services.detections import extract_images


logger = logging.getLogger(__name__)


class Decoded(pydantic.BaseModel):
    data: str
    box: list
    impl: str
    type: str = None
    polygon: list = None


def extract_barcodes(
    pdf_path: str,
    output_file: str | BinaryIO,
    image_dpi: int = 288,
    impl: Literal['opencv', 'pyzbar'] = 'opencv',
    extract_all_images: bool = False,
    page_start: int = None,
    page_end: int = None,
):
    std_dpi = 72
    dpi = image_dpi
    dpi_diff = dpi / std_dpi
    doc = pymupdf.open(pdf_path)
    pages = doc.pages()
    page_start = page_start if page_start is not None else 0
    page_end = page_end if page_end is not None else doc.page_count

    detected_barcodes = []
    extracted_images = []
    seen_xrefs = set()
    for page in pages:
        if page.number < page_start or page.number > page_end:
            continue
        pixmap = page.get_pixmap(dpi=dpi)
        img_raw = np.ndarray([pixmap.h, pixmap.w, 3], dtype=np.uint8, buffer=pixmap.samples_mv)
        img = _reencode_jpg(img_raw)
        # img = img_raw
        decoded = detect_barcodes(img, split_counts=[2, 4, 6])

        logger.info(f'{page.number + 1} / {doc.page_count}; decoded = {len(decoded)}')
        # cv2.imwrite(f'img_{page.number}.jpg', img[:, :, ::-1])
        del img, img_raw, pixmap

        if not decoded and not extract_all_images:
            continue
        # t = time.time()
        page_imgs, seen_xref_new = extract_images.extract_images_from_page(doc, os.path.dirname(pdf_path), page, seen_xrefs)
        seen_xrefs |= seen_xref_new
        # logger.info(f'extract {page.number} t={time.time() - t:.1f}s')
        for barcode in decoded:
            barcode.box = [
                barcode.box[0] / dpi_diff,
                barcode.box[1] / dpi_diff,
                barcode.box[2] / dpi_diff,
                barcode.box[3] / dpi_diff,
            ]
        all_barcode_boxes = [b.box for b in decoded]
        for barcode in decoded:
            default_directions = ['up_left', 'up', 'left', 'up_right', 'right']
            detected_image = detect_images(barcode.box, all_barcode_boxes,  barcode.data, page_imgs, search_directions=default_directions)
            if detected_image:
                detected_barcodes.append(detected_image)

        if extract_all_images:
            for img in page_imgs:
                gray = check_is_gray(cv2.imread(img[0]))
                if not gray:
                    extracted_images.append(img[0])

    # for detected_barcode in detected_barcodes:
    #     shutil.copy(detected_barcode[0], os.path.basename(detected_barcode[0]))

    # Compose zip archive: take all images, md text and original pdf
    basename = os.path.basename(pdf_path)
    answer = {
        'detected_num': len(detected_barcodes),
        'barcodes': [d[1] for d in detected_barcodes]
    }
    answer_str = json.dumps(answer, indent=2)
    files = set()
    with zipfile.ZipFile(output_file, 'w') as z:
        img_set = set()
        for detected_barcode in detected_barcodes:
            img_set.add(detected_barcode[0])
            base, ext = os.path.splitext(detected_barcode[0])
            # print(f'{base} = {detected_barcode[1]}')
            fname = f'{detected_barcode[1]}{ext}'
            i = 1
            while fname in files:
                fname = f'{detected_barcode[1]}_{i}{ext}'
                i += 1
            z.write(detected_barcode[0], fname)
            files.add(fname)

        # Save additional images
        for i, img in enumerate(extracted_images):
            if img in img_set:
                continue
            base, ext = os.path.splitext(img)
            fname = f'image_{i:04d}{ext}'
            z.write(img, fname)
        z.write(pdf_path, basename)
        z.writestr('barcodes.json', answer_str)

    return answer_str


def check_is_gray(img: np.ndarray):
    if len(img.shape) < 3:
        return True
    if img.shape[2] == 1:
        return True

    b, g, r = img[:, :, 0], img[:, :, 1], img[:, :, 2]
    return (b == g).all() and (b == r).all()


def detect_images(text_rect, all_text_rects, detected_text, imgs, search_directions=['up', 'left']):
    def extend_box_for_direction(text_rect, direction, coef):
        x0, y0, x1, y1 = text_rect[:4]
        if direction == 'up':
            y0 = y0 - (y1 - y0) * coef
            y0 = 0 if y0 < 0 else y0
        elif direction == 'left':
            x0 = x0 - (x1 - x0) * coef // 2
            x0 = 0 if x0 < 0 else x0
        elif direction == 'up_left':
            x0 = x0 - (x1 - x0) * coef // 2
            x0 = 0 if x0 < 0 else x0
            y0 = y0 - (y1 - y0) * coef
            y0 = 0 if y0 < 0 else y0
        elif direction == 'up_right':
            y0 = y0 - (y1 - y0) * coef
            y0 = 0 if y0 < 0 else y0
            x1 = x1 + (x1 - x0) * coef // 2
        elif direction == 'down':
            pass
        elif direction == 'right':
            x1 = x1 + (x1 - x0) * coef // 2
        return x0, y0, x1, y1

    coef = 12
    x0, y0, x1, y1 = extend_box_for_direction(text_rect, search_directions[0], coef)
    extended_text_rect = IRect(x0=x0 - (x1-x0) * 0.25, y0=y0, x1=x1 + (x1-x0) * 0.25, y1=y1)

    def intersects_any(rect: IRect, boxes):
        for box in boxes:
            next_rect = IRect(x0=box[0], y0=box[1], x1=box[2], y1=box[3])
            if rect.intersects(next_rect):
                return True
        return False

    detected = []
    for img in imgs:
        img_rects = img[1]
        for img_rect in img_rects:
            im_rect = IRect(x0=int(img_rect.x0), y0=int(img_rect.y0), x1=int(img_rect.x1), y1=int(img_rect.y1))
            intersects = extended_text_rect.intersects(im_rect)
            is_gray = check_is_gray(cv2.imread(img[0]))
            intersects_other_text = intersects_any(im_rect, all_text_rects)
            # center_img_rect = IRect(x0=im_rect.x0, y0=(im_rect.y1 + im_rect.y0) // 2, x1=im_rect.x1, y1=im_rect.y1)
            # inverse_intersects = extended_text_rect.intersects(center_img_rect)
            if intersects and not is_gray and not intersects_other_text:
                # print(f'[img  rect={center_img_rect}] {os.path.basename(img[0])}')
                # shutil.copy(img[0], os.path.basename(img[0]))
                detected.append((detected_text, img[0], im_rect))

    text_center = (x1 + x0) // 2, (y1 + y0) // 2
    min_distance = float('+inf')
    per_distance = {}
    for (text, path, im_rect) in detected:
        img_center = (im_rect.x1 + im_rect.x0) // 2, (im_rect.y1 + im_rect.y0) // 2
        distance = np.linalg.norm(np.array(text_center) - np.array(img_center))
        # print(f'{path} distance = {distance}')
        per_distance[distance] = (path, detected_text)
        if distance < min_distance:
            min_distance = distance

    if min_distance in per_distance:
        final_image = per_distance[min_distance]
    else:
        # search another direction?
        if len(search_directions) > 1:
            new_search_directions = search_directions[1:]
        else:
            logger.warning(f'Image for barcode {detected_text} can not be found. All search directions exceeded')
            return None
        # print(f'was {extended_text_rect}')
        # print(f'search {detected_text} {new_search_directions}')
        final_image = detect_images(text_rect, all_text_rects, detected_text, imgs, new_search_directions)
    # print(f'[img  rect={final_image[1]}] {final_image[0]}')
    # print(f'[text rect={extended_text_rect}] text={detected_text}')
    return final_image


def box_intersection(box_a: typing.List[int], box_b: typing.List[int]):
    xa = max(box_a[0], box_b[0])
    ya = max(box_a[1], box_b[1])
    xb = min(box_a[2], box_b[2])
    yb = min(box_a[3], box_b[3])

    inter_area = max(0, xb - xa) * max(0, yb - ya)

    box_a_area = (box_a[2] - box_a[0]) * (box_a[3] - box_a[1])
    box_b_area = (box_b[2] - box_b[0]) * (box_b[3] - box_b[1])

    d = float(box_a_area + box_b_area - inter_area)
    if d == 0:
        return 0
    iou = inter_area / d
    box_a_frac = inter_area / box_a_area
    box_b_frac = inter_area / box_b_area
    return iou, box_a_frac, box_b_frac


def rect_to_box(rect, offset: tuple[int]) -> list[int]:
    box = IRect(
        x0=rect.left,
        y0=rect.top,
        x1=rect.left + rect.width,
        y1=rect.top + rect.height,
    )
    return [box.x0 + offset[0], box.y0 + offset[1], box.x1 + offset[0], box.y1 + offset[1]]


def polygon_with_offset(poly, offset=(0, 0)) -> list:
    return [locations.Point(x=p.x + offset[0], y=p.y + offset[1]) for p in poly]


def detect_barcodes(frame, split_counts: list[int] = None) -> list[Decoded]:
    # boxes = _detect_faces_split(frame, threshold)
    boxes = []
    if not split_counts:
        split_counts = [4]

    def add_box(b):
        for i, b0 in enumerate(boxes):
            iou, a_frac, b_frac = box_intersection(b0.box, b.box)
            stop = a_frac >= 0.5 or b_frac >= 0.5
            if iou > 0.1 or stop:
                # set the largest proba to existing box
                # boxes[i].box = max(b0[4], b[4])
                return
            if iou > 0.005 and (b0.impl == 'pyzbar' or b.impl == 'pyzbar'):
                return
        boxes.append(b)

    # Add 1 more run with 4 split pyzbar
    split_counts = [(s, 'opencv') for s in split_counts] + [(4, 'pyzbar'), (6, 'pyzbar')]

    for split_count, impl in split_counts:
        size_multiplier = 2. / (split_count + 1)
        xstep = int(frame.shape[1] / (split_count + 1))
        ystep = int(frame.shape[0] / (split_count + 1))

        xlimit = int(np.ceil(frame.shape[1] * (1 - size_multiplier)))
        ylimit = int(np.ceil(frame.shape[0] * (1 - size_multiplier)))
        for x in range(0, xlimit, xstep):
            for y in range(0, ylimit, ystep):
                y_border = min(frame.shape[0], int(np.ceil(y + frame.shape[0] * size_multiplier)))
                x_border = min(frame.shape[1], int(np.ceil(x + frame.shape[1] * size_multiplier)))

                # Enlarge width by some number
                x_new = max(x - int(frame.shape[1] * 0.03), 0)
                x_border_new = min(x_border + int(frame.shape[1] * 0.03), frame.shape[1])

                crop = frame[y:y_border, x_new:x_border_new, :]
                box_candidates = _detect_codes_split(crop, (x_new, y), impl=impl)

                # print(split_count, impl, box_candidates)
                for bb in box_candidates:
                    add_box(bb)
                    # boxes.extend(box_candidates)

    return boxes  # [:, :4].astype(int), boxes[:, 4]


def _detect_codes_split(rgb_frame, offset=(0, 0), impl: str = 'opencv') -> list[Decoded]:
    if impl == 'opencv':
        return _decode_opencv(rgb_frame, offset)
    else:
        if not pyzbar:
            raise ValueError('pyzbar is not installed')
        return _decode_pyzbar(rgb_frame, offset)


def _decode_pyzbar(rgb_frame, offset=(0, 0)):
    decoded_raw = pyzbar.decode(rgb_frame)
    decoded_raw = [d for d in decoded_raw if d.type not in [pyzbar.ZBarSymbol.QRCODE.name, pyzbar.ZBarSymbol.SQCODE.name]]
    decoded = []

    for d in decoded_raw:
        box = _increase_height(rect_to_box(d.rect, offset), coef=0.3)
        decoded.append(Decoded(
            data=d.data.decode(),
            type=d.type,
            box=_set_minimum_width(box, minimum=100),
            impl='pyzbar',
            # polygon=polygon_with_offset(d.polygon, offset),
        ))
    return decoded


def _decode_opencv(rgb_frame, offset=(0, 0)):
    bd = cv2.barcode.BarcodeDetector()
    # bd = cv2.barcode.BarcodeDetector('sr.prototxt', 'sr.caffemodel')
    detected = bd.detectAndDecodeMulti(rgb_frame)
    decoded = []

    if not detected[0]:
        return decoded

    for data, points in zip(detected[1], detected[2]):
        if not data.strip():
            continue
        box = _points_to_box(points, offset)
        # Increase height to not intersect
        decoded.append(Decoded(
            data=data,
            box=_increase_height(box, coef=0.5),
            impl='opencv',
        ))

    return decoded


def _reencode_jpg(img: np.ndarray):
    _, jpg_data = cv2.imencode('.jpg', img[:, :, ::-1])
    decoded = cv2.imdecode(np.frombuffer(jpg_data, dtype=np.uint8), cv2.IMREAD_COLOR)[:, :, ::-1]
    del jpg_data
    return decoded


def _increase_height(box, coef=0.5, min_height=20):
    height = box[3] - box[1]
    increase = int(max(height, min_height) * coef)
    return box[0], max(box[1] - increase, 0), box[2], box[3] + increase


def _set_minimum_width(box, minimum=100):
    width = box[2] - box[0]
    if width < minimum:
        return [box[0] - minimum//2, box[1], box[2] + minimum // 2, box[3]]
    return box


def _points_to_box(points: np.ndarray, offset=(0, 0)):
    x0 = min(points[:, 0]) + offset[0]
    x1 = max(points[:, 0]) + offset[0]
    y0 = min(points[:, 1]) + offset[1]
    y1 = max(points[:, 1]) + offset[1]
    return [x0, y0, x1, y1]
