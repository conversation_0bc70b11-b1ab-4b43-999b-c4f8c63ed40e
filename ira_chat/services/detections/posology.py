import logging
import mimetypes
import os
import re
import shutil
import tempfile
import zipfile
from typing import BinaryIO
from urllib.parse import urljoin

import bs4.element
import markdownify
import pymupdf4llm

from ira_chat.utils.http_utils import download_content_sync

logger = logging.getLogger(__name__)
symbol_map = {
    34: '∀', 36: '∃', 38: '&', 39: '∋', 42: '∗', 45: '−', 60: '<', 62: '>', 64: '≅', 65: 'Α', 66: 'Β',
    67: 'Χ', 68: 'Δ', 69: 'Ε', 70: 'Φ', 71: 'Γ', 72: 'Η', 73: 'Ι', 74: 'ϑ', 75: 'Κ', 76: 'Λ', 77: 'Μ',
    78: 'Ν', 79: 'Ο', 80: 'Π', 81: 'Θ', 82: 'Ρ', 83: 'Σ', 84: 'Τ', 85: 'Υ', 86: 'ς', 87: 'Ω', 88: 'Ξ',
    89: 'Ψ', 90: 'Ζ', 92: '∴', 94: '⊥', 97: 'α', 98: 'β', 99: 'χ', 100: 'δ', 101: 'ε', 102: 'φ', 103: 'γ',
    104: 'η', 105: 'ι', 106: 'ϕ', 107: 'κ', 108: 'λ', 109: 'μ', 110: 'ν', 111: 'ο', 112: 'π', 113: 'θ',
    114: 'ρ', 115: 'σ', 116: 'τ', 117: 'υ', 118: 'ϖ', 119: 'ω', 120: 'ξ', 121: 'ψ', 122: 'ζ', 126: '∼',
    160: '€', 161: 'ϒ', 162: '′', 163: '≤', 164: '⁄', 165: '∞', 166: 'ƒ', 167: '♣', 168: '♦', 169: '♥',
    170: '♠', 171: '↔', 172: '←', 173: '↑', 174: '→', 175: '↓', 176: '°', 177: '±', 178: '″', 179: '≥',
    180: '×', 181: '∝', 182: '∂', 183: '•', 184: '÷', 185: '≠', 186: '≡', 187: '≈', 188: '…', 191: '↵',
    192: 'ℵ', 193: 'ℑ', 194: 'ℜ', 195: '℘', 196: '⊗', 197: '⊕', 198: '∅', 199: '∩', 200: '∪', 201: '⊃',
    202: '⊇', 203: '⊄', 204: '⊂', 205: '⊆', 206: '∈', 207: '∉', 208: '∠', 209: '∇', 210: '®', 211: '©',
    212: '™', 213: '∏', 214: '√', 215: '⋅', 216: '¬', 217: '∧', 218: '∨', 219: '⇔', 220: '⇐', 221: '⇑',
    222: '⇒', 223: '⇓', 224: '◊', 225: '⟨', 229: '∑', 241: '⟩', 242: '∫'
}
webdings_font_map = {
    '\uf020': '\xa0', '\uf021': '🕷', '\uf022': '🕸', '\uf023': '🕲', '\uf024': '🕶', '\uf025': '🏆',
    '\uf026': '🎖', '\uf027': '🖇', '\uf028': '🗨', '\uf029': '🗩', '\uf02a': '🗰', '\uf02b': '🗱',
    '\uf02c': '🌶', '\uf02d': '🎗', '\uf02e': '🙾', '\uf02f': '🙼', '\uf03a': '⏭', '\uf03b': '⏸',
    '\uf03c': '⏹', '\uf03d': '⏺', '\uf03e': '🗚', '\uf03f': '🗳', '\uf040': '🛠', '\uf041': '🏗',
    '\uf042': '🏘', '\uf043': '🏙', '\uf044': '🏚', '\uf045': '🏜', '\uf046': '🏭', '\uf047': '🏛',
    '\uf048': '🏠', '\uf049': '🏖', '\uf04a': '🏝', '\uf04b': '🛣', '\uf04c': '🔍', '\uf04d': '🏔',
    '\uf04e': '👁', '\uf04f': '👂', '\uf050': '🏞', '\uf051': '🏕', '\uf052': '🛤', '\uf053': '🏟',
    '\uf054': '🛳', '\uf055': '🕬', '\uf056': '🕫', '\uf057': '🕨', '\uf058': '🔈', '\uf059': '🎔',
    '\uf05a': '🎕', '\uf05b': '🗬', '\uf05c': '🙽', '\uf05d': '🗭', '\uf05e': '🗪', '\uf05f': '🗫',
    '\uf060': '⮔', '\uf061': '✔', '\uf062': '🚲', '\uf063': '□', '\uf064': '🛡', '\uf065': '📦',
    '\uf066': '🛱', '\uf067': '■', '\uf068': '🚑', '\uf069': '🛈', '\uf06a': '🛩', '\uf06b': '🛰',
    '\uf06c': '🟈', '\uf06d': '🕴', '\uf06e': '⚫', '\uf06f': '🛥', '\uf070': '🚔', '\uf071': '🗘',
    '\uf072': '🗙', '\uf073': '❓', '\uf074': '🛲', '\uf075': '🚇', '\uf076': '🚍', '\uf077': '⛳',
    '\uf078': '🛇', '\uf079': '⊖', '\uf07a': '🚭', '\uf07b': '🗮', '\uf07c': '|', '\uf07d': '🗯',
    '\uf07e': '🗲', '\uf080': '🚹', '\uf081': '🚺', '\uf082': '🛉', '\uf083': '🛊', '\uf084': '🚼',
    '\uf085': '👽', '\uf086': '🏋', '\uf087': '⛷', '\uf088': '🏂', '\uf089': '🏌', '\uf08a': '🏊',
    '\uf08b': '🏄', '\uf08c': '🏍', '\uf08d': '🏎', '\uf08e': '🚘', '\uf08f': '🗠', '\uf090': '🛢',
    '\uf091': '💰', '\uf092': '🏷', '\uf093': '💳', '\uf094': '👪', '\uf095': '🗡', '\uf096': '🗢',
    '\uf097': '🗣', '\uf098': '✯', '\uf099': '🖄', '\uf09a': '🖅', '\uf09b': '🖃', '\uf09c': '🖆',
    '\uf09d': '🖹', '\uf09e': '🖺', '\uf09f': '🖻', '\uf0a0': '🕵', '\uf0a1': '🕰', '\uf0a2': '🖽',
    '\uf0a3': '🖾', '\uf0a4': '📋', '\uf0a5': '🗒', '\uf0a6': '🗓', '\uf0a7': '📖', '\uf0a8': '📚',
    '\uf0a9': '🗞', '\uf0aa': '🗟', '\uf0ab': '🗃', '\uf0ac': '🗂', '\uf0ad': '🖼', '\uf0ae': '🎭',
    '\uf0af': '🎜', '\uf0b0': '🎘', '\uf0b1': '🎙', '\uf0b2': '🎧', '\uf0b3': '💿', '\uf0b4': '🎞',
    '\uf0b5': '📷', '\uf0b6': '🎟', '\uf0b7': '🎬', '\uf0b8': '📽', '\uf0b9': '📹', '\uf0ba': '📾',
    '\uf0bb': '📻', '\uf0bc': '🎚', '\uf0bd': '🎛', '\uf0be': '📺', '\uf0bf': '💻', '\uf0c0': '🖥',
    '\uf0c1': '🖦', '\uf0c2': '🖧', '\uf0c3': '🕹', '\uf0c4': '🎮', '\uf0c5': '🕻', '\uf0c6': '🕼',
    '\uf0c7': '📟', '\uf0c8': '🖁', '\uf0c9': '🖀', '\uf0ca': '🖨', '\uf0cb': '🖩', '\uf0cc': '🖿',
    '\uf0cd': '🖪', '\uf0ce': '🗜', '\uf0cf': '🔒', '\uf0d0': '🔓', '\uf0d1': '🗝', '\uf0d2': '📥',
    '\uf0d3': '📤', '\uf0d4': '🕳', '\uf0d5': '🌣', '\uf0d6': '🌤', '\uf0d7': '🌥', '\uf0d8': '🌦',
    '\uf0d9': '☁', '\uf0da': '🌧', '\uf0db': '🌨', '\uf0dc': '🌩', '\uf0dd': '🌪', '\uf0de': '🌬',
    '\uf0df': '🌫', '\uf0e0': '🌜', '\uf0e1': '🌡', '\uf0e2': '🛋', '\uf0e3': '🛏', '\uf0e4': '🍽',
    '\uf0e5': '🍸', '\uf0e6': '🛎', '\uf0e7': '🛍', '\uf0e8': 'Ⓟ', '\uf0e9': '♿', '\uf0ea': '🛆',
    '\uf0eb': '🖈', '\uf0ec': '🎓', '\uf0ed': '🗤', '\uf0ee': '🗥', '\uf0ef': '🗦', '\uf0f0': '🗧',
    '\uf0f1': '🛪', '\uf0f2': '🐿', '\uf0f3': '🐦', '\uf0f4': '🐟', '\uf0f5': '🐕', '\uf0f6': '🐈',
    '\uf0f7': '🙬', '\uf0f8': '🙮', '\uf0f9': '🙭', '\uf0fa': '🙯', '\uf0fb': '🗺', '\uf0fc': '🌍',
    '\uf0fd': '🌏', '\uf0fe': '🌎', '\uf0ff': '🕊'
}


def postprocess_headers(text: str):
    blocks = []

    for block in text.split('\n\n'):
        lines = []
        found_lines = block.split('\n')
        if len(found_lines) > 1:
            for line in found_lines:
                # import re
                # if re.findall('^[_\*]{1,2}\w+[_\*]{1,2}$', line)
                if line.startswith('_') and line.endswith('_'):
                    line = f'\n{line}\n'
                if line.startswith('*') and line.endswith('*'):
                    line = f'\n{line}\n'

                lines.append(line)
            blocks.append('\n'.join(lines))
        else:
            blocks.append('\n'.join(found_lines))

    return '\n\n'.join(blocks)


def format_table(rows):
    # Split rows into columns
    table_rows = [row.split('|')[1:-1] for row in rows]
    # Strip leading and trailing spaces from each cell
    table_rows = [[cell.strip() for cell in row] for row in table_rows]
    is_header = len(table_rows) > 1 and all([set(col) == {'-'} for col in table_rows[1]])

    # Determine the maximum width for each column
    col_widths = [max(len(cell) for cell in col) for col in zip(*table_rows)]

    if is_header:
        table_rows[1] = ['-' * x for x in col_widths]

    # Replace artificial col names "Col1", "Col2", ... with empty string
    table_rows[0] = [re.sub(r'Col\d+', '', cell) for cell in table_rows[0]]

    # Reformat each row to have columns of equal width
    formatted_rows = []
    for i, row in enumerate(table_rows):
        format_sep = ' | '
        space = ' '
        if is_header and i == 1:
            format_sep = '-|-'
            space = '-'
        formatted_row = format_sep.join(f"{cell:<{col_widths[j]}}" for j, cell in enumerate(row))
        formatted_rows.append(f"|{space}{formatted_row}{space}|")

    return formatted_rows


def postprocess_tables(text: str):
    lines = text.split('\n')
    table_ranges = []
    start, end = -1, -1
    for i, line in enumerate(lines):
        if line.startswith('|'):
            if start == -1:
                start = i
            end = i
        else:
            if end != -1:
                table_ranges.append((start, end + 1))
                start, end = -1, -1

    for table_start, table_end in table_ranges:
        try:
            fixed_webdings = fix_webdings('\n'.join(lines[table_start:table_end]))
            lines[table_start:table_end] = fixed_webdings.split('\n')
            lines[table_start:table_end] = format_table(lines[table_start:table_end])
        except IndexError:
            pass

    return '\n'.join(lines)


def fix_webdings(text: str):
    return ''.join([webdings_font_map.get(ch, ch) for ch in text])


def postprocess_section_links(text: str, local_link: str, section_pattern: str, url: str = None) -> str:
    md_link_pattern = r'\[(.*?)]\((.*?)\)'
    md_link_hash_pattern = r'\[(.*?)]\((#.*?)\)'
    section_match_list = list(re.finditer(section_pattern, text))
    back, forward = 20, 200

    for i in range(len(section_match_list)):
        match = section_match_list[i]
        start = match.start()

        cut = text[start - back:start + forward]
        # if '4.4' in cut:

        matched_links = re.findall(md_link_pattern, cut)
        if matched_links:
            for link_text, link in matched_links:
                if link.startswith('#') and url:
                    new_link = url + link
                    # Replace matched part with new link
                    repl = cut.replace(rf'[{link_text}]({link})', rf'[{link_text}]({new_link})')
                    # repl = re.sub(rf'\[{link_text}]\({link}\)', rf'[{link_text}]({new_link})', cut)
                    cut = repl
                    # repl = re.sub(md_link_pattern, rf'[{link_text}]({new_link})', cut)

            text = text[:start - back] + cut + text[start + forward:]
            # Reset positions
            section_match_list = list(re.finditer(section_pattern, text))
        else:
            matched_text = text[start:start+len(match.group(0))]
            text = text[:start] + f'[{matched_text}]({local_link})' + text[start + len(match.group(0)):]
            # Reset positions
            section_match_list = list(re.finditer(section_pattern, text))

    if url:
        # Process general # links
        text = re.sub(md_link_hash_pattern, rf'[\g<1>]({url}\g<2>)', text)

    # repl = re.sub(section_pattern, rf'[\g<0>]({local_link})', text)
    return text


def postprocess_dots(text: str):
    """
    Splits two sentences with newline in case of existing newline.
    Markdown does not consider a single newline as paragraph so this makes it double.
    """
    repl = re.sub(r'(\.\n)([^\n])', fr'\g<1>\n\g<2>', text)
    return repl


def extract_section_text(text: str, start_text: str, end_text: str) -> str:
    extracted = []
    start_extracting = False
    only_first = True

    source_lines = text.split('\n')
    exceptions = []

    start = -1
    end = -1
    start_match_list = list(re.finditer(start_text, text))
    end_match_list = list(re.finditer(end_text, text))
    for start_match in start_match_list:
        cut = text[start_match.start():start_match.start() + 100]
        no_exceptions = all(e not in cut for e in exceptions)
        if not no_exceptions:
            continue
        start = start_match.start()
        break

    for end_match in end_match_list:
        cut = text[end_match.start():end_match.start() + 100]
        no_exceptions = all(e not in cut for e in exceptions)
        if not no_exceptions:
            continue
        end = end_match.start()
        if only_first:
            break

    full_text = text[start:end] + '\n'
    # for i, line in enumerate(source_lines):
    #     # if not line.strip():
    #     #     continue
    #
    #     test_line = line.replace("*", "").replace("_", "").replace("#", "").strip()
    #     start_condition = (from_line_start and test_line.startswith(start_text)) or (not from_line_start and start_text in test_line)
    #     no_exceptions = all(e not in test_line for e in exceptions)
    #     if start_condition and no_exceptions:
    #         start_extracting = True
    #     if (from_line_start and test_line.startswith(end_text)) or (not from_line_start and end_text in test_line):
    #         start_extracting = False
    #
    #     if start_extracting:
    #         extracted.append(line)
    return full_text


def chomp(text, preserve_spaces=False):
    """
    If the text in an inline tag like b, a, or em contains a leading or trailing
    space, strip the string and return a space as suffix of prefix, if needed.
    This function is used to prevent conversions like
        <b> foo</b> => ** foo**
    """
    prefix = ' ' if text and text[0] == ' ' else ''
    suffix = ' ' if text and text[-1] == ' ' else ''
    if not preserve_spaces:
        text = text.strip()
    return prefix, suffix, text


def abstract_inline_conversion(markup_fn, preserve_spaces=False):
    """
    This abstracts all simple inline tags like b, em, del, ...
    Returns a function that wraps the chomped text in a pair of the string
    that is returned by markup_fn, with '/' inserted in the string used after
    the text if it looks like an HTML tag. markup_fn is necessary to allow for
    references to self.strong_em_symbol etc.
    """

    def implementation(self, el, text, parent_tags):
        markup_prefix = markup_fn(self)
        if markup_prefix.startswith('<') and markup_prefix.endswith('>'):
            markup_suffix = '</' + markup_prefix[1:]
        else:
            markup_suffix = markup_prefix
        if '_noformat' in parent_tags:
            return text
        prefix, suffix, text = chomp(text, preserve_spaces)
        if not text:
            return ''
        return '%s%s%s%s%s' % (prefix, markup_prefix, text, markup_suffix, suffix)

    return implementation


class Html2MdConverter(markdownify.MarkdownConverter):
    def __init__(self, html_path: str, style_map: dict, url: str = None, **options):
        super().__init__(**options)
        self.style_map = style_map
        self.url = url
        self.style_map_fns = self._preprocess_style_map(style_map)
        self.image_counter = 0
        self.html_base, _ = os.path.splitext(os.path.basename(html_path))
        self.image_dir = os.path.dirname(html_path)

    def _header_format(self, num):
        def fn(self, node, result, parent_tags):
            prefix = f'{"#" * num} '
            format_result = re.sub(r'^(\n*)', r'\1' + prefix, result)
            return format_result

        return fn

    def _next_image_name(self, ext):
        self.image_counter += 1
        return f'{self.html_base}-{self.image_counter:03d}{ext}'

    @staticmethod
    def _is_full_url(u):
        return u.startswith('https://') or u.startswith('http://')

    def _preprocess_style_map(self, style_map):
        style_map_fn = {}
        for key, value in style_map.items():
            if value[0] == 'h' and value[1:].isdigit():
                num = int(value[1:])
                value = self._header_format(num)
            else:
                value = self._format_func_fn(value)
            style_map_fn[key] = value

        return style_map_fn

    def _format_func_fn(self, md_variant):
        tags = md_variant.split(',')

        def fn(self, node, result, parent_tags):
            for tag in tags:
                format_fn = markdownify.abstract_inline_conversion(lambda x: f'<{tag}>')
                result = format_fn(self, node, result, parent_tags)
            result = result + '\n'
            return result

        return fn

    def process_tag(self, node: bs4.element.Tag, parent_tags=None):
        # node.name == html tag
        result = super().process_tag(node, parent_tags)

        clazz = node.attrs.get('class')[0] if node.attrs.get('class') else None
        if self.style_map_fns.get(f'.{clazz}'):
            md_format_func = self.style_map_fns.get(f'.{clazz}')
            return md_format_func(self, node, result, parent_tags)

        if node.attrs.get('style'):
            style = node.attrs.get('style')
            if 'font-family' in style and 'Symbol' in style:
                result = ''.join(symbol_map[ord(ch)] if ord(ch) in symbol_map else ch for ch in result)
        return result

    def convert_img(self, el, text, parent_tags):
        alt = el.attrs.get('alt', None) or ''
        src = el.attrs.get('src', None) or ''
        if self.url:
            # Rewrite image url
            src = urljoin(self.url, src)

        if not alt:
            alt = os.path.basename(src)

        if self._is_full_url(src):
            # Download and save it
            try:
                img, mimetype = download_content_sync(src, get_content_type=True)
                ext = mimetypes.guess_extension(mimetype)
                image_name = self._next_image_name(ext)
                with open(os.path.join(self.image_dir, image_name), 'wb') as f:
                    f.write(img)

                alt = image_name
                src = image_name
            except Exception as e:
                logger.warning(f'Failed to download image {src}: {str(e)}')

        title = el.attrs.get('title', None) or ''
        title_part = ' "%s"' % title.replace('"', r'\"') if title else ''
        # if convert_as_inline and el.parent.name not in self.options['keep_inline_images_in']:
        #     return alt

        return f'![{alt}]({src}{title_part})'

    convert_b = abstract_inline_conversion(lambda self: '<b>', preserve_spaces=True)

    convert_u = markdownify.abstract_inline_conversion(lambda self: '<u>')


def extract_section_pymupdf4llm(
    pdf_path: str,
    output_file: str | BinaryIO,
    start_text: str,
    end_text: str,
    from_line_start=True,
    section_pattern: str = r'rubriques?\s+[0-9.,\set]+[0-9.,]',
    image_dpi: int = 100,
    **kwargs,
):
    text = pymupdf4llm.to_markdown(
        pdf_path,
        margins=(0, 15, 0, 15),
        image_margins=(0, 15, 0, 15),
        write_images=True,
        image_path=os.path.dirname(pdf_path),
        image_basename=True,
        include_page_breaks=False,
        dpi=image_dpi,
        table_strategy='lines_strict'
        # show_progress=True,
    )

    # text = pymupdf4llm.to_markdown(pdf_path, margins=(0, 25, 0, 25))
    full_text = extract_section_text(text, start_text, end_text)

    # Postprocessing
    # full_text = '\n'.join(extracted)
    full_text = postprocess_headers(full_text)
    full_text = postprocess_tables(full_text)
    full_text = postprocess_dots(full_text)
    full_text = fix_webdings(full_text)
    full_text = postprocess_section_links(full_text, os.path.basename(pdf_path), section_pattern=section_pattern)
    # Delete page numbers ?
    full_text = re.sub(r'\n[0-9]+\n', '', full_text)

    # Write everything in zip
    basename = os.path.basename(pdf_path)
    basename_no_ext, _ = os.path.splitext(basename)
    dirname = os.path.dirname(pdf_path)
    image_files = re.findall(fr'{basename}-[-.a-zA-Z0-9]+', full_text)
    image_files = set(image_files)

    # Rename image files
    old_image_files = list(image_files)
    image_files = []
    for i, image_file in enumerate(sorted(old_image_files)):
        _, ext = os.path.splitext(image_file)
        new_file = f'{basename_no_ext}-{i}{ext}'
        try:
            shutil.move(os.path.join(dirname, image_file), os.path.join(dirname, new_file))
            full_text = full_text.replace(image_file, new_file)
            image_files.append(new_file)
        except Exception as e:
            logger.error(str(e))
            continue
    # End rename

    # Compose zip archive: take all images, md text and original pdf
    with zipfile.ZipFile(output_file, 'w') as z:
        z.writestr(f'Posology_{basename_no_ext}.md', full_text)
        for img in image_files:
            z.write(os.path.join(dirname, img), img)
        z.write(pdf_path, basename)

    return full_text


def extract_section_html(
    html_path: str,
    output_file: str | BinaryIO,
    start_text: str,
    end_text: str,
    from_line_start=True,
    section_pattern: str = r'rubriques?\s+[0-9.,\set]+[0-9.,]',
    image_dpi: int = 100,
    style_map: dict = None,
    url: str = None,
    **kwargs,
):
    # html = requests.get(url).content
    with open(html_path, 'rb') as f:
        html_raw = f.read()

    encoding = _detect_encoding(html_raw, html_path)
    try:
        html = html_raw.decode('windows-1252' if encoding == 'iso-8859-1' else encoding)
    except:
        html = html_raw.decode(encoding)

    full_text = Html2MdConverter(
        html_path=html_path,
        style_map=style_map,
        url=url,
        escape_misc=False
    ).convert(html)

    full_text = extract_section_text(full_text, start_text, end_text)

    full_text = postprocess_headers(full_text)
    full_text = postprocess_tables(full_text)
    full_text = postprocess_dots(full_text)
    full_text = fix_webdings(full_text)
    full_text = postprocess_section_links(
        full_text, os.path.basename(html_path), section_pattern=section_pattern, url=url,
    )
    # Delete page numbers ?
    # full_text = re.sub(r'\n[0-9]+\n', '', full_text)

    # Write everything in zip
    basename = os.path.basename(html_path)
    basename_no_ext, _ = os.path.splitext(basename)
    dirname = os.path.dirname(html_path)
    image_files = []
    for match in re.finditer(rf'\[({basename_no_ext}-.*?)]\(.*?\)', full_text):
        image_file = match.group(1)

        image_files.append(image_file)
    image_files = list(set(image_files))

    # Compose zip archive: take all images, md text and original pdf
    with zipfile.ZipFile(output_file, 'w') as z:
        z.writestr(f'Posology_{basename_no_ext}.md', full_text)
        for img in image_files:
            z.write(os.path.join(dirname, img), img)
        z.write(html_path, basename)

    return full_text


def _detect_encoding(data: bytes, path: str = None) -> str:
    charset_start = data.find(b'charset=')
    if charset_start:
        is_quotes = data[charset_start + len('charset=')] == ord('"')
        if not is_quotes:
            end = data[charset_start:].find(b'"')
            encoding_raw = data[charset_start + len('charset='):charset_start + end]
        else:
            end = data[charset_start + len('charset="'):].find(b'"')
            encoding_raw = data[charset_start + len('charset="'):charset_start + len('charset="') + end]
        encoding = encoding_raw.decode()
        logger.info(f"Detected charset {encoding} in file {path}")
    else:
        encoding = 'utf-8'

    return encoding


def save_to_tmp_dir(path: str = None, data: bytes = None, name: str = None):
    tempdir = tempfile.mkdtemp()
    if path:
        target_path = os.path.join(tempdir, os.path.basename(path))
        shutil.copy(path, target_path)
    elif data and name:
        target_path = os.path.join(tempdir, name)
        with open(target_path, 'wb') as f:
            f.write(data)
    else:
        raise ValueError('Provide path or data and name')

    return tempdir, target_path
