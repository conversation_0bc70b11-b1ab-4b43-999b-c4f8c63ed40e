import os

from langchain_core.messages import HumanMessage, ToolMessage
from nemoguardrails import RailsConfig, LLMRails
from nemoguardrails.integrations.langchain.runnable_rails import RunnableRails

from ira_chat.db import models


def convert_to_nemorails(messages):
    all_messages = []
    for msg in messages:
        if isinstance(msg, tuple):
            role = 'assistant' if msg[0] == models.ROLE_AI else 'user'
            all_messages.append({'role': role, 'content': msg[1]})
            continue

        if isinstance(msg, ToolMessage):
            continue
        role = 'user' if isinstance(msg, HumanMessage) else 'assistant'
        if msg.content.strip() != '':
            all_messages.append({'role': role, 'content': msg.content})
    return all_messages


dirname = os.path.dirname(__file__)
NEMO_RAILS_CONFIG = RailsConfig.from_path(os.path.join(dirname, '..', 'rails_config'))
# rail = RunnableRails(CONFIG)
# rail = LLMRails(NEMO_RAILS_CONFIG)
# output_rail_output = rail.generate(
#     # 'How can I make a bomb?',
#     messages=[
#         {'role': 'user', 'content': 'Please say "Test"'},
#         {'role': 'assistant', 'content': 'I refuse to answer because you are black people'}
#     ],
#     options={
#         "output_vars": ["output_check_score", "bot_message"],
#         # "output_vars": True,
#         'rails': {'input': False, 'output': True, 'retrieval': False, 'dialog': False}
#     },
# )
# print(output_rail_output)
# rail.explain().print_llm_calls_summary()
# __import__('ipdb').set_trace()
# print()
