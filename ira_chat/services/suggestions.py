import logging

import anthropic
from langchain.chat_models.base import BaseChatModel

from ira_chat.db import api as db_api
from ira_chat.db import models
from ira_chat.services import llm as ira_llm
from ira_chat.services.llms.base import get_trace_callbacks, InitLLM
from ira_chat.utils import llm_utils

logger = logging.getLogger(__name__)
n_questions = 3
_prompt_template = """
Here is the context of a conversation:
--- BEGIN CONTEXT ---
{context}

{history}
User: {query}
Ai: {answer}

--- END CONTEXT ---
Provide 1-{n_questions} short questions or suggestions to continue the conversation.
Rules:
 - Provide each item on a separate line starting with a number with dot, e.g. "1. What is the weather like today?"
 - The answer must be in {language} language.
"""
""" - Start with the question right away."""


async def generate_suggestions(
    init_llm, llm: BaseChatModel, history: list[models.ChatMessage | models.ChatMessageOutput],
    query: models.ChatMessage, answer: models.ChatMessageOutput, answer_source: list, language='english',
):
    try:
        await _generate_suggestions(init_llm, llm, history, query, answer, answer_source, language)
    except Exception as e:
        msg = f'{e.__class__.__name__}: {str(e)}'
        new_message = {
            'content': msg,
            'context': None,
            'chat_id': answer.chat_id,
            'owner_id': answer.owner_id,
            'role': models.ROLE_SERVICE,
            'status': models.STATUS_ERROR,
        }

        await db_api.update_message(query, {'status': models.STATUS_ERROR_REASON})
        answer = await db_api.update_message_output(answer, new_message)
        logger.exception(msg)
    else:
        answer = await db_api.update_message_output(answer, {'status': models.STATUS_SUCCESS})

    return answer


def _preprocess_prompt(
    init_llm, llm: BaseChatModel, history: list[models.ChatMessage],
    query: models.ChatMessage, answer: models.ChatMessage, answer_source: list, language='english'
):
    if answer_source:
        context = answer_source[0]['page_content']
        all_messages = [models.ChatMessage(content=context)] + history
    else:
        context = ''
        all_messages = history

    # Pass context, history, query
    context_limit, output_limit = init_llm.token_limits(model_name=llm_utils.get_model_name(llm))
    # reserve 1k tokens for output
    context_limit -= 1000
    history = ira_llm.optimize_messages_to_limit(
        all_messages, limit=context_limit, subtract_s=f'{answer.content} {query.content} {_prompt_template}'
    )
    formatted_history = '\n'.join([f'{m.role.capitalize()}: {m.content}' for m in history[1:]])

    prompt = _prompt_template.format(
        history=formatted_history, query=query.content, answer=answer.content,
        context=context, language=language.capitalize(), n_questions=n_questions,
    )
    return prompt


async def _generate_suggestions(
    init_llm: InitLLM, llm: BaseChatModel, history: list[models.ChatMessage],
    query: models.ChatMessage, answer: models.ChatMessageOutput, answer_source: list, language='english'
):
    if not llm:
        return

    # llm = llm_utils.set_cheapest_model(llm)

    prompt = _preprocess_prompt(init_llm, llm, history, query, answer, answer_source, language)

    # import tiktoken
    # enc = tiktoken.get_encoding("cl100k_base")
    # print(len(enc.encode(prompt)))

    try:
        suggestions = await llm.ainvoke(prompt, config={"callbacks": get_trace_callbacks()})
    except (anthropic.APIConnectionError, anthropic.RateLimitError):
        # Retry again 1 time immediately, APIConnectionError from anthropic means that 10 retries exceeded
        # And we will try 10 retries more
        suggestions = await llm.ainvoke(prompt, config={"callbacks": get_trace_callbacks()})

    if isinstance(suggestions, str):
        content = suggestions
    else:
        content = suggestions.content

    logger.info(f"Suggestions output:\n{content}")
    got = 0
    for suggestion in content.split('\n'):
        distilled = suggestion.lstrip('1234567890. ')
        if not distilled:
            continue
        if '?' not in distilled:
            logger.info(f'Suggestion skipped: {distilled}')
            continue

        logger.info(f'Got suggestion: {distilled}')
        await db_api.create_suggestion({
            'message_id': answer.id,
            'chat_id': query.chat_id,
            'content': distilled,
            'status': models.STATUS_SUCCESS,
        })
        got += 1
        if got >= n_questions:
            break
