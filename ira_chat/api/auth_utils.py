import asyncio
import datetime
import os
import random

from fastapi import Request, HTTPException

from ira_chat.api import common_utils
from ira_chat.cache import global_cache
from ira_chat.context import context as ctx
from ira_chat.credentials import credentials
from ira_chat.db import api as db_api, models
from ira_chat.policies import policies
from ira_chat.utils import utils

COOKIE_KEY = 'IRA_SESSION_ID'
AUTH_KEY = 'Authorization'
MAX_ID = int(8e18)
MIN_ID = int(1e17)
# sessions = {}
keys = os.getenv("AUTH_KEY", "")


async def session_handler_cache(request: Request, domain):
    cache_result = None
    processed_session = ctx.current_session()
    sess_id = request.cookies.get(COOKIE_KEY) or (processed_session.id if processed_session else None)
    if sess_id:
        cache_key = common_utils.get_cache_key(sess_id, request, domain=domain)
        cache_result = await global_cache.get_cache().aget(cache_key)
        if cache_result:
            sess, *_ = cache_result
            created = False
            ctx.session.set(sess)
            return created, cache_result
        # Assign session by sess id
        # if sess_id in sessions:
        #     sess = sessions[sess_id]
        # TODO delete expired session
        # else:
        sess, created = await get_or_create_session_for_id(sess_id)

        # sessions[sess_id] = sess
    else:
        # Create session ?
        sess = await db_api.create_session({
            'admin': False,
            'user_id': get_random_user_id(),
            'ttl': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=365),
        })
        created = True
        # sessions[sess_id] = sess
        # raise HTTPException(401, 'Unauthorized')
    # token = get_bearer_token(request)
    # token = await db_api.get_token_by_id(token)
    # if not token:
    #     raise HTTPException(401, f'Unauthorized')
    # else:
    #     sess = models.Session(user_id=token.user_id)

    ctx.session.set(sess)
    return created, cache_result


async def only_admin(request: Request, call_next):
    sess = ctx.current_session()
    admins_exist = credentials.ADMIN_IDS and len(credentials.ADMIN_IDS) > 0
    if (not sess or not sess.admin) and admins_exist:
        raise HTTPException(403, 'Forbidden')

    return await call_next(request)


async def only_admin_or_org_owner(request: Request, call_next):
    sess = ctx.current_session()
    admins_exist = credentials.ADMIN_IDS and len(credentials.ADMIN_IDS) > 0
    not_admin = (not sess or not sess.admin) and admins_exist
    is_owner = policies.is_allowed(ctx.current_org_permissions(), policies.OrgAccessMode.OWNER, policies.OrgAccessMode)
    if not_admin and not is_owner:
        raise HTTPException(403, 'Forbidden')

    return await call_next(request)


async def get_or_create_session_for_id(sess_id: str) -> tuple[models.Session, bool]:
    if len(sess_id) > models.Session.id.type.length:
        raise HTTPException(400, 'Invalid session id')
    sess = await db_api.get_session_by_id(sess_id)
    created = False
    if sess is None:
        # Re-create session
        sess = await db_api.create_session({
            'id': sess_id,
            'admin': False,
            'user_id': get_random_user_id(),
            'ttl': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=365),
        })
        created = True

    return sess, created


def get_random_user_id():
    return random.randint(MIN_ID, MAX_ID)


def ephemeral_user(sess):
    return sess is None or sess.user_id >= MIN_ID


def generate_temp_password():
    charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    length = 8
    return ''.join(random.choice(charset) for _ in range(length))


async def login(login, password):
    sess = ctx.current_session()

    u = None
    if login == 'admin':
        if keys and password == keys:
            sess.user_id = -1
        else:
            raise HTTPException(401, 'Wrong credentials')
    else:
        u = await db_api.get_user_by_login_password(login, password)
        if not ephemeral_user(sess) and u.id != sess.user_id:
            raise HTTPException(400, "Already authenticated as another user, please Log out first")

        sess.user_id = u.id

    sess, created = await login_session(sess)
    return u, sess, created


async def login_session(sess: models.Session):
    if not sess:
        return sess, False
    # sess.id = str(uuid.uuid4())
    admin = sess.user_id in credentials.ADMIN_IDS or sess.user_id == -1
    sess = await db_api.update_session(sess, {
        'ttl': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=365),
        'user_id': sess.user_id,
        'admin': admin,
    })

    _ = asyncio.create_task(
        utils.log_exceptions(db_api.update_user(models.User(id=sess.user_id), {'last_login_at': models.now()}))
    )
    await common_utils.clear_cache_for_session(sess.id)
    # sessions[sess.id] = sess

    return sess, False


async def logout(request: Request):
    sess_id = request.cookies.get(COOKIE_KEY)
    if sess_id:
        # if sess_id in sessions:
        #     del sessions[sess_id]
        sess = await db_api.get_session_by_id(sess_id)
        if sess:
            sess = await make_session_as_guest(sess)
            # sessions[sess.id] = sess

    await common_utils.clear_cache_for_session(sess_id)


async def make_session_as_guest(sess: models.Session):
    return await db_api.update_session(sess, {
        'ttl': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=365),
        'user_id': get_random_user_id(),
        'admin': False,
    })


def extend_ws_permissions_with_org(permissions, org_permissions):
    ws_allowed = False
    if policies.is_allowed(org_permissions, policies.OrgAccessMode.OWNER, policies.OrgAccessMode):
        permissions = policies.AccessMode.OWNER
        ws_allowed = True
    if policies.is_allowed(org_permissions, policies.OrgAccessMode.ALL_WS_VIEW, policies.OrgAccessMode):
        permissions = permissions | policies.AccessMode.ALL_CHATS_VIEW
        ws_allowed = True
    if policies.is_allowed(org_permissions, policies.OrgAccessMode.WORKSPACE_DELETE, policies.OrgAccessMode):
        permissions = permissions | policies.AccessMode.ALL_CHATS_MANAGE
    if policies.is_allowed(org_permissions, policies.OrgAccessMode.ORG_MANAGE, policies.OrgAccessMode):
        permissions = permissions | policies.AccessMode.METRICS_MANAGE

    return permissions, ws_allowed


def get_bearer_token(request: Request) -> tuple[str, str]:
    res = ""
    auth_header = request.headers.get(AUTH_KEY)
    if auth_header:
        split = auth_header.split()
        if len(split) > 1:
            slash_split = split[1].split('/')
            if len(slash_split) > 1:
                return slash_split[0], slash_split[1]
            return split[1], ""
    return res, ""
