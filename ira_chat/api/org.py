import datetime
import logging
import uuid
import json
from collections import defaultdict
from typing import Optional

from fastapi import APIRouter, HTTPException
from fastapi.responses import ORJSONResponse
from pydantic import BaseModel
from starlette.responses import Response

from ira_chat.api import common_utils, org_webhooks
from ira_chat.context import context as ctx
from ira_chat.db import api as db_api, models, base
from ira_chat.e_mail import send
from ira_chat.policies import policies
from ira_chat.utils import utils

logger = logging.getLogger(__name__)


class WorkspaceConfig(BaseModel):
    config: Optional[dict] = None


class OrgModel(BaseModel):
    # name: Optional[str]
    display_name: Optional[str] = None
    description: Optional[str] = None
    config: Optional[dict] = None


class OrgModelWithDomains(BaseModel):
    name: str
    display_name: Optional[str] = None
    description: Optional[str] = None
    domains: list
    config: Optional[dict] = None


class OrgDomains(BaseModel):
    domains: list


def get_routers():
    router = APIRouter(prefix='/organizations')
    cur_router = APIRouter(prefix='/organization')
    cur_router.add_api_route("/info", current_org, methods=['GET'], name='Current organization')
    org_prefix = '/{org_id}'

    router.add_api_route("", list_org, methods=['GET'], name='Available organization list')
    router.add_api_route("", create_org, methods=['POST'], name='Create a new organization')
    router.add_api_route("/{org_id}", get_org, methods=['GET'], name='Organization by id')
    router.add_api_route("/{org_id}", edit_org, methods=['PUT'], name='Edit organization')
    router.add_api_route("/{org_id}", delete_org, methods=['DELETE'], name='DELETE organization')
    router.add_api_route("/{org_id}/domains", edit_org_domains, methods=['PUT'], name='Edit organization domains')
    router.add_api_route("/{org_id}/roles", list_org_roles, methods=['GET'], name='List of available roles')
    router.add_api_route("/{org_id}/users", list_org_users, methods=['GET'], name='List of organization users')
    router.add_api_route("/{org_id}/users", create_org_user, methods=['POST'], name='Add a user to an organization (invite)')
    router.add_api_route(
        "/{org_id}/users/{org_user_id}", edit_org_user_permissions,
        methods=['PUT'], name='Edit organization user permission'
    )
    router.add_api_route(
        "/{org_id}/users/{org_user_id}", delete_org_user,
        methods=['DELETE'], name='Delete organization user'
    )
    router.add_api_route(
        "/{org_id}/users/{org_user_id}/system", delete_org_user_account,
        methods=['DELETE'], name='Delete user account'
    )
    # router.add_api_route("/{org_id}", delete_org, methods=['GET'], name='Organization by id')
    router.include_router(org_webhooks.get_router(prefix='/'), prefix=org_prefix, tags=['Webhooks'])

    return [router, cur_router]


class OrgUserPermissions(BaseModel):
    # permissions: list
    role_id: int


class NewOrgUser(BaseModel):
    login: str
    role_id: Optional[int] = None
    # permissions: Optional[list]
    org_domain: Optional[str] = None


async def list_org():
    is_admin = ctx.current_session().admin

    if not is_admin:
        access_ws_ids = await db_api.list_groups_id_by_user_id(ctx.current_session().user_id)
        ws_ids = [ws.workspace_id for ws in access_ws_ids]
        workspaces = await db_api.list_workspaces(ids=ws_ids)
        org_ids = [w.org_id for w in workspaces]
        orgs = await db_api.list_orgs(ids=org_ids)
    else:
        orgs = await db_api.list_orgs_all()
        org_ids = [o.id for o in orgs]

    org_domains = await db_api.list_orgs_domains(org_ids=org_ids)

    # Join org <-> domains
    orgs = [o.to_dict() for o in orgs]
    _ = [o.pop('config') for o in orgs]
    domain_map = defaultdict(list)
    for domain in org_domains:
        domain_map[domain.org_id].append(domain.domain)

    for org in orgs:
        org['domains'] = domain_map[org['id']]

    return ORJSONResponse(content={'organizations': orgs}, status_code=200)


async def create_org(org_data: OrgModelWithDomains):
    is_admin = ctx.current_session().admin

    if not is_admin:
        raise HTTPException(403, 'Forbidden')

    existing = await db_api.get_org(org_data.name)
    if existing:
        raise HTTPException(409, f'Org with name "{org_data.name}" already exists')

    org_dict = org_data.dict(exclude_unset=True)
    domains = org_dict.pop('domains', [])
    for domain in domains:
        org = await db_api.get_org_by_domain(domain)
        if org:
            raise HTTPException(409, f'Domain "{domain}" already taken')

    org_dict['config'] = json.dumps(org_dict.get('config', {}))
    org = await db_api.create_org(org_dict)
    for domain in domains:
        await db_api.create_org_domain({'org_id': org.id, 'domain': domain})

    org_dict = org.to_dict()
    org_dict['domains'] = domains

    await db_api.create_org_user({
        'org_id': org.id,
        'user_id': ctx.current_user().id,
        'role_id': db_api.get_role_by_name('owner')['id'],
        'confirmed': True,
    })

    return ORJSONResponse(content=org_dict, status_code=200)


async def edit_org(org_id: int, org_data: OrgModel):
    is_admin = ctx.current_session().admin

    if not is_admin:
        org_user = await db_api.get_org_user(org_id, ctx.current_session().user_id)
        current_role = db_api.get_role_by_id(org_user.role_id)
        if not is_admin and not policies.is_allowed(
            current_role['permissions'], policies.OrgAccessMode.ORG_MANAGE, policies.OrgAccessMode
        ):
            raise HTTPException(403, 'Forbidden')

    org = await db_api.get_org_by_id(org_id)
    org_dict = org_data.dict(exclude_unset=True)
    if 'config' in org_dict and org_dict['config'] is not None:
        org_dict['config'] = json.dumps(org_dict['config'])
    org = await db_api.update_org(org, org_dict)
    await common_utils.clear_cache_for_session(ctx.current_session().id)

    return ORJSONResponse(content=org.to_dict(), status_code=200)


async def delete_org(org_id: int):
    is_admin = ctx.current_session().admin

    if not is_admin:
        org_user = await db_api.get_org_user(org_id, ctx.current_session().user_id)
        current_role = db_api.get_role_by_id(org_user.role_id)
        if not is_admin and not policies.is_allowed(
            current_role['permissions'], policies.OrgAccessMode.ORG_MANAGE, policies.OrgAccessMode
        ):
            raise HTTPException(403, 'Forbidden')

    org = await db_api.get_org_by_id(org_id)
    if not org:
        raise HTTPException(404, 'Organization not found')
    await db_api.base.mark_as_delete(models.Organization, org.id)
    # TODO Delete workspaces and all their resources: chats/detections/files
    await db_api.delete_org_domains(org.id)

    return Response(status_code=204)


async def edit_org_domains(org_id: int, org_domains: OrgDomains):
    is_admin = ctx.current_session().admin
    ses = ctx.current_session()

    if not is_admin:
        org_user = await db_api.get_org_user(org_id, ctx.current_session().user_id)
        if not org_user:
            raise HTTPException(403, 'Forbidden')
        current_role = db_api.get_role_by_id(org_user.role_id)
        if not ses.admin and not policies.is_allowed(
            current_role['permissions'], policies.OrgAccessMode.ORG_MANAGE, policies.OrgAccessMode
        ):
            raise HTTPException(403, 'Forbidden')

    org = await db_api.get_org_by_id(org_id)
    if not org:
        raise HTTPException(404, 'Org not found')

    old_domains = await db_api.list_org_domains(org.id)
    old_domains = [d.domain for d in old_domains]
    new_domains = set(org_domains.domains) - set(old_domains)
    for domain in new_domains:
        org_for_domain = await db_api.get_org_by_domain(domain)
        if org_for_domain:
            raise HTTPException(409, f'Domain "{domain}" already taken')

    await db_api.delete_org_domains(org.id)
    for domain in org_domains.domains:
        await db_api.create_org_domain({'org_id': org.id, 'domain': domain})

    org_dict = org.to_dict()
    org_dict['domains'] = org_domains.domains
    return ORJSONResponse(content=org_dict, status_code=200)


async def get_org(org_id: int):
    ses = ctx.current_session()
    is_admin = ses.admin

    org_user = await db_api.get_org_user(ctx.current_org().id, ctx.current_user().id)
    if not is_admin:
        if not org_user:
            raise HTTPException(403, 'Not the organization member')

    org = await db_api.get_org_by_id(org_id)
    domains = await db_api.list_org_domains(org_id)
    org_dict = org.to_dict()
    org_dict['domains'] = [d.domain for d in domains]

    current_role = await common_utils.get_current_role(org_user, ses)
    if not is_admin and not policies.is_allowed(
        current_role['permissions'], policies.OrgAccessMode.ORG_MANAGE, policies.OrgAccessMode
    ):
        del org_dict['config']

    return ORJSONResponse(content=org_dict, status_code=200)


async def current_org():
    org = ctx.current_org()
    ses = ctx.current_session()
    is_admin = ses.admin

    org_dict = org.to_dict()
    if not is_admin and not policies.is_allowed(
        ctx.current_org_permissions(), policies.OrgAccessMode.ORG_MANAGE, policies.OrgAccessMode
    ):
        del org_dict['config']

    return ORJSONResponse(content=org_dict, status_code=200)


async def list_org_users(org_id: int):
    user = ctx.current_user()
    ses = ctx.current_session()
    org_user = await db_api.get_org_user(org_id, user.id)
    if not org_user and not ses.admin:
        raise HTTPException(403, 'You are not in the organization')

    current_role = await common_utils.get_current_role(org_user, ses)
    if not ses.admin and not policies.is_allowed(
        current_role['permissions'], policies.OrgAccessMode.ORG_USERS_MANAGE, policies.OrgAccessMode
    ):
        raise HTTPException(403, 'Forbidden')

    org_users = await db_api.list_org_users(org_id=org_id)
    users = await db_api.list_users([o.user_id for o in org_users])
    user_map = {u.id: u for u in users}

    users_with_permissions = []
    for ou in org_users:
        o_user = user_map.get(ou.user_id)
        to_add = {
            'org_user_id': ou.id,
            'role_id': ou.role_id,
            'confirmed': ou.confirmed,
        }
        if not o_user:
            if ou.user_login:
                to_add.update({
                    'id': None,
                    'login': ou.user_login,
                    'name': None,
                    'info': None,
                    'last_seen_at': None,
                    'last_activity_at': None,
                    'last_login_at': None,
                })
        else:
            to_add.update({
                'id': o_user.id,
                'login': o_user.login,
                'name': o_user.name,
                'info': o_user.info,
                'last_seen_at': base.date_to_string(o_user.last_seen_at),
                'last_activity_at': base.date_to_string(o_user.last_activity_at),
                'last_login_at': base.date_to_string(o_user.last_login_at),
            })
        users_with_permissions.append(to_add)

    roles = db_api.get_roles()
    for role in roles:
        role['permissions'] = policies.decode_permissions(role['permissions'], org=True)

    return ORJSONResponse(content={'users': users_with_permissions, 'roles': roles}, status_code=200)


async def list_org_roles(org_id: int):
    user = ctx.current_user()
    ses = ctx.current_session()
    org_user = await db_api.get_org_user(org_id, user.id)
    if not org_user and not ses.admin:
        raise HTTPException(403, 'You are not in the organization')

    roles = db_api.get_roles()
    for role in roles:
        role['permissions'] = policies.decode_permissions(role['permissions'], org=True)

    return ORJSONResponse(content={'roles': roles}, status_code=200)


async def create_org_user(org_id: int, new_org_user: NewOrgUser):
    user = ctx.current_user()
    ses = ctx.current_session()
    org = await db_api.get_org_by_id(org_id)
    if not org:
        raise HTTPException(404, 'Organization not found')

    current_org_user = await db_api.get_org_user(org_id, user.id)
    if not current_org_user and not ses.admin:
        raise HTTPException(403, 'Forbidden')

    current_role = await common_utils.get_current_role(current_org_user, ses)
    if not ses.admin and not policies.is_allowed(
            current_role['permissions'], policies.OrgAccessMode.ORG_USERS_MANAGE, policies.OrgAccessMode
    ):
        raise HTTPException(403, 'Forbidden')

    if not new_org_user.role_id:
        new_role = db_api.get_role_by_name('user')
        new_org_user.role_id = new_role['id']
    else:
        new_role = db_api.get_role_by_id(new_org_user.role_id)
        if not new_role:
            raise HTTPException(404, f'Role not found by id: {new_org_user.role_id}')

    u = await db_api.get_user_by_login(new_org_user.login, notfoundok=True)
    if not u:
        # What to do with user ??
        org_user = await db_api.get_org_user(org_id, user_login=new_org_user.login)
        if org_user:
            raise HTTPException(409, 'User already exists')
        org_user_values = {
            'user_login': new_org_user.login,
            'user_id': None,
            'org_id': org_id,
            'role_id': new_org_user.role_id,
            'confirmed': False,
        }
    else:
        org_user = await db_api.get_org_user(org_id, user_id=u.id)
        if org_user:
            raise HTTPException(409, 'User already exists')
        org_user_values = {
            'user_login': None,
            'user_id': u.id,
            'org_id': org_id,
            'role_id': new_org_user.role_id,
            'confirmed': False,
        }

    new_org_user_db = await db_api.create_org_user(org_user_values)

    invite = await db_api.create_user_invite({
        'id': str(uuid.uuid4()),
        'from_user_id': ses.user_id,
        'to_user_id': u.id if u else 0,
        'to_workspace_id': None,
        'to_group_id': None,
        'to_user_login': new_org_user.login if not u else None,
        'expiry_time': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=1),
    })

    org_base_url = (
        f'{utils.get_global_schema()}'
        f'://{await common_utils.resolve_org_domain(org_id, new_org_user.org_domain)}'
    )
    send.send_invite(new_org_user.login, org_base_url, None, org, invite)

    new_org_user_db_dict = new_org_user_db.to_dict()
    # new_org_user_db_dict['permissions'] = policies.decode_permissions(new_org_user_db_dict['permissions'], org=True)

    return ORJSONResponse(content=new_org_user_db_dict, status_code=200)


async def edit_org_user_permissions(org_id: int, org_user_id: int, permissions: OrgUserPermissions):
    async with base.session_context():
        user = ctx.current_user()
        ses = ctx.current_session()
        current_org_user = await db_api.get_org_user(org_id, user.id)
        if not current_org_user and not ses.admin:
            raise HTTPException(403, 'Forbidden')

        current_role = await common_utils.get_current_role(current_org_user, ses)
        if not ses.admin and not policies.is_allowed(
                current_role['permissions'], policies.OrgAccessMode.ORG_USERS_MANAGE, policies.OrgAccessMode
        ):
            raise HTTPException(403, 'Forbidden')

        org_user = await db_api.get_org_user_by_id(org_user_id)
        if not org_user:
            raise HTTPException(403, 'User is not in the organization')
        if org_user.org_id != org_id:
            raise HTTPException(403, 'User is not in the organization')

        if org_user.user_id == user.id:
            raise HTTPException(403, 'Cannot change permissions for yourself')

        new_role = db_api.get_role_by_id(permissions.role_id)
        if not new_role:
            raise HTTPException(404, f'Role not found by id: {permissions.role_id}')

        new_values = {'role_id': permissions.role_id}
        updated = await db_api.update_org_user(org_user, new_values)
        updated_dict = updated.to_dict()

        sessions = await db_api.get_sessions(user_id=org_user.user_id)
        for session in sessions:
            await common_utils.clear_cache_for_session(session.id)
    # updated_dict['permissions'] = policies.decode_permissions(new_role['permissions'], org=True)

    return ORJSONResponse(content=updated_dict, status_code=200)


async def delete_org_user(org_id: int, org_user_id: int):
    user = ctx.current_user()
    ses = ctx.current_session()
    current_org_user = await db_api.get_org_user(org_id, user.id)
    if not current_org_user and not ses.admin:
        raise HTTPException(403, 'Forbidden')

    current_role = await common_utils.get_current_role(current_org_user, ses)
    if not ses.admin and not policies.is_allowed(
            current_role['permissions'], policies.OrgAccessMode.ORG_USERS_MANAGE, policies.OrgAccessMode
    ):
        raise HTTPException(403, 'Forbidden')

    org_user = await db_api.get_org_user_by_id(org_user_id)
    if not org_user:
        raise HTTPException(403, 'User is not in the organization')

    if user.id == org_user.user_id:
        raise HTTPException(403, 'Can not delete self from the organization')

    if org_user.org_id != org_id:
        raise HTTPException(403, 'User is not in the organization')

    user_to_delete = await db_api.get_user_by_id(org_user.user_id, notfoundok=True)

    workspaces = await db_api.list_workspaces(org_id=org_id)
    for workspace in workspaces:
        await db_api.delete_user_from_workspace(workspace.id, user_id=org_user.user_id)
        if user_to_delete or org_user.user_login:
            login = org_user.user_login or user_to_delete.login
            await db_api.delete_user_from_workspace(workspace.id, user_login=login)

    await db_api.delete_org_user(org_user_id)

    return Response(status_code=204)


async def delete_org_user_account(org_id: int, org_user_id: int):
    ses = ctx.current_session()
    if not ses.admin:
        raise HTTPException(403, 'Only for admins')

    org_user = await db_api.get_org_user_by_id(org_user_id)
    if not org_user:
        raise HTTPException(404, 'Not found in this organization')
    user = await db_api.get_user_by_id(org_user.user_id)

    all_orgs = await db_api.list_orgs_all()
    for org in all_orgs:
        this_org_user = await db_api.get_org_user(org_id=org.id, user_id=user.id)
        if not this_org_user:
            this_org_user = await db_api.get_org_user(org_id=org.id, user_login=user.login)
            if not this_org_user:
                continue
        await delete_org_user(org.id, this_org_user.id)

    await db_api.delete_user(user.id)
    await db_api.delete_sessions_for_user(user.id)

    return Response(status_code=204)
