import asyncio
import collections
import logging
from typing import Optional

import fastapi
from fastapi import APIRouter, HTTPException
from fastapi.responses import ORJSONResponse
from pydantic import BaseModel
from starlette.responses import Response

from ira_chat.api import auth_utils
from ira_chat.api import chats
from ira_chat.api import common_utils
from ira_chat.api import detection
from ira_chat.api import extractor
from ira_chat.api import groups
from ira_chat.api import metrics
from ira_chat.api import timeline
from ira_chat.api import timelines_schemas
from ira_chat.api import workspace_tokens
from ira_chat.cache import global_cache
from ira_chat.context import context as ctx
from ira_chat.db import api as db_api, models, base
from ira_chat.policies import policies
from ira_chat.policies.policies import AccessMode
from ira_chat.policies.workspace import check_user_in_workspace
from ira_chat.services import dataset_utils
from ira_chat.services.detection import DEFAULT_PROMPT_CONFIG
from ira_chat.services import engine_manager, engine_manager_chats
from ira_chat.services import llm as ira_llm
from ira_chat.services.engine_manager import LANGCHAIN_TYPE, LLAMA_TYPE

logger = logging.getLogger(__name__)


class WorkspaceConfig(BaseModel):
    config: Optional[dict] = None


class EditWorkspace(BaseModel):
    # name: Optional[str]
    display_name: Optional[str] = None
    description: Optional[str] = None
    config: Optional[dict] = None
    access_type: Optional[str] = None


class CloneWorkspace(BaseModel):
    name: str
    display_name: Optional[str] = None
    description: Optional[str] = None
    access_type: Optional[str] = None
    config: Optional[dict] = None
    clone_files: bool = True
    clone_members: bool = True


class CreateWorkspace(EditWorkspace):
    name: str


class CheckWorkspace(EditWorkspace):
    name: Optional[str] = None


class AddMember(BaseModel):
    login: str
    group_id: int


class EditMember(BaseModel):
    group_id: int


def get_routers():
    prefix = '/workspaces'
    ws_prefix = '/{workspace_id}'
    router = APIRouter(prefix=prefix)
    config_router = APIRouter(prefix='/workspaces_configs')

    router.add_api_route("", list_workspaces, methods=['GET'], name='Available workspace list')
    router.add_api_route("", create_workspace, methods=['POST'], name='Create a workspace')
    router.add_api_route("/{workspace_id}/config", change_config, methods=['PUT'], name='Change config')
    router.add_api_route("/{workspace_id}/clone", clone_workspace, methods=['POST'], name='Clone workspace')
    router.add_api_route("/{workspace_id}/default_prompt_config", prompt_config, methods=['GET'], name='Prompts config')
    router.add_api_route("/{workspace_id}", get_workspace, methods=['GET'], name='Get workspace')
    router.add_api_route("/{workspace_id}", edit_workspace, methods=['PUT'], name='Edit workspace')
    router.add_api_route("/{workspace_id}", delete_workspace, methods=['DELETE'], name='Delete workspace')

    router.add_api_route(
        "/{workspace_id}/members", get_workspace_members,
        methods=['GET'], name='Get workspace members'
    )
    router.add_api_route(
        "/{workspace_id}/members", add_workspace_member,
        methods=['POST'], name='Add workspace member'
    )
    router.add_api_route(
        "/{workspace_id}/members/{member_id}", edit_workspace_member,
        methods=['PUT'], name='Edit workspace member'
    )
    router.add_api_route(
        "/{workspace_id}/members/{member_id}", delete_workspace_member,
        methods=['DELETE'], name='Delete workspace members'
    )

    config_router.add_api_route("/default", get_prompt_config, methods=['POST'], name='Get prompt config for workspace config')
    config_router.add_api_route("/available_models", get_available_models, methods=['GET'], name='Get list of available models')

    router.include_router(chats.get_router(prefix='/'), prefix=ws_prefix, tags=['Chats'])
    router.include_router(workspace_tokens.get_router(prefix='/'), prefix=ws_prefix, tags=['WS tokens'])
    router.include_router(groups.get_router(prefix='/'), prefix=ws_prefix, tags=['Groups'])
    router.include_router(metrics.get_router(prefix='/'), prefix=ws_prefix, tags=['Metrics (ws)'])
    router.include_router(extractor.get_router(prefix='/'), prefix=ws_prefix, tags=['Extractor'])
    router.include_router(timeline.get_router(prefix='/'), prefix=ws_prefix, tags=['Timelines'])
    router.include_router(timelines_schemas.get_router(prefix='/'), prefix=ws_prefix, tags=['Timelines'])

    # Detection apps
    detections = detection.get_routers(prefix='/')
    for r in detections:
        router.include_router(r, prefix=ws_prefix, tags=['Detections'])

    return [router, config_router]


async def change_config(workspace_id: int, ws_config: WorkspaceConfig):
    if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.WORKSPACE_MANAGE):
        raise HTTPException(403, 'Forbidden')

    ws = ctx.current_workspace()
    ws_dict = ws_config.dict()
    if 'engine_type' in ws_dict:
        if ws_dict['engine_type'] != LANGCHAIN_TYPE and ws_dict['engine_type'] != LLAMA_TYPE:
            raise HTTPException(400, f'engine_type should be one of {{{LANGCHAIN_TYPE}, {LLAMA_TYPE}}}')

    ws_dict['config'] = ws_config.config if ws_config.config else {}

    ws = await db_api.update_workspace(ws, ws_dict)

    return ORJSONResponse(content=ws.to_dict(), status_code=200)


async def prompt_config(workspace_id: int):
    manager = engine_manager.EngineManager(ctx.current_org(), ctx.current_workspace())

    return ORJSONResponse(content={
        'chat': {'prompt_config': manager.get_default_prompts()},
        'detection': {'prompt_config': DEFAULT_PROMPT_CONFIG},
    })


async def get_prompt_config(ws_data: CheckWorkspace):
    ws_dict = ws_data.dict(exclude_unset=True)
    ws = models.Workspace(**ws_dict)
    ws.config = ws.config if ws.config else {}
    manager = engine_manager.EngineManager(ctx.current_org(), ws)

    return ORJSONResponse(content={
        'chat': {'prompt_config': manager.get_default_prompts()},
        'detection': {'prompt_config': DEFAULT_PROMPT_CONFIG},
    })


async def get_available_models():
    all_llms = ira_llm.llms
    all_models = {k: v.available_models() for k, v in all_llms.items() if v.provides_llm}

    return ORJSONResponse(content={'available_models': all_models})


async def list_workspaces():
    ses = ctx.current_session()
    all_ws = (
        ses.admin or
        policies.is_allowed(ctx.current_org_permissions(), policies.OrgAccessMode.ALL_WS_VIEW, policies.OrgAccessMode)
    )

    async with base.session_context():
        if all_ws:
            workspaces = await db_api.list_workspaces(org_id=ctx.current_org().id)
        else:
            if ctx.request.value.state.access_type == 'session':
                group_users = await db_api.list_groups_id_by_user_id(ctx.current_session().user_id)
                ws_ids = [gu.workspace_id for gu in group_users if gu.confirmed]
                workspaces = await db_api.list_workspaces(ids=ws_ids, org_id=ctx.current_org().id, public_access_types=True)
            else:
                # Assume workspace-token
                workspaces = [ctx.current_workspace()]

        workspaces = await set_user_permissions(workspaces, ses, ctx.current_org_permissions())
        # for ws in workspaces:
        #     _, permissions = await workspace.check_user_in_workspace(ctx.current_session().user_id, ws['id'])
        #     ws['user_permissions'] = policies.decode_permissions(permissions)

    return ORJSONResponse(content={'workspaces': workspaces}, status_code=200)


async def set_user_permissions(wss: list[models.Workspace], ses: models.Session, org_permissions):
    ws_ids = [ws.id for ws in wss]
    cache_results = {}
    for ws_id in ws_ids:
        ws_cache_key = common_utils.get_ws_cache_key(ctx.current_session(), ws_id)
        cache_result = await global_cache.get_cache().aget(ws_cache_key)
        if cache_result:
            cache_results[ws_id] = cache_result

    remain_ids = [ws.id for ws in wss if ws.id not in cache_results]
    groups = await db_api.list_groups(workspace_ids=remain_ids)
    group_permissions_map = {g.id: g.permissions for g in groups}
    group_users = await db_api.list_group_users(workspace_ids=remain_ids, user_id=ses.user_id)
    gu_workspace_permissions = {}
    for gu in group_users:
        gu_workspace_permissions[gu.workspace_id] = group_permissions_map.get(gu.group_id, 0)

    result = []
    for ws in wss:
        if ws.id in cache_results:
            _, _, base_permissions, _ = cache_results[ws.id]
        else:
            base_permissions = gu_workspace_permissions.get(ws.id, 0)
            allowed, _, group_ids = await check_user_in_workspace(ses.user_id, ws)

            ws_cache_key = common_utils.get_ws_cache_key(ctx.current_session(), ws.id)
            await global_cache.get_cache().aset(ws_cache_key, (ws, allowed, base_permissions, group_ids))

        ws_permissions, _ = auth_utils.extend_ws_permissions_with_org(base_permissions, org_permissions)
        if ses.admin:
            ws_permissions = ws_permissions | policies.AccessMode.OWNER
        ws_dict = ws.to_dict()
        ws_dict['user_permissions'] = policies.decode_permissions(ws_permissions)
        result.append(ws_dict)

    return result


async def create_workspace(ws_data: CreateWorkspace):
    if not policies.is_allowed(
        ctx.current_org_permissions(),
        policies.OrgAccessMode.WORKSPACE_CREATE,
        mode_class=policies.OrgAccessMode
    ):
        raise HTTPException(403, 'Forbidden')

    ws_dict = ws_data.dict(exclude_unset=True)
    if 'config' in ws_dict:
        config = ws_dict['config']
        if 'engine_type' in config:
            if config['engine_type'] != LANGCHAIN_TYPE and config['engine_type'] != LLAMA_TYPE:
                raise HTTPException(400, f'engine_type should be one of {{{LANGCHAIN_TYPE}, {LLAMA_TYPE}}}')

        ws_dict['config'] = ws_dict['config'] if ws_dict['config'] else {}
    else:
        ws_dict['config'] = {}

    ws_dict['org_id'] = ctx.current_org().id
    if ws_data.access_type and ws_data.access_type not in models.WS_ACCESS_TYPES:
        raise HTTPException(400, f'Workspace access can only be one of these: {models.WS_ACCESS_TYPES}')

    already_exists = await db_api.get_workspace(ws_dict['name'], org_id=ctx.current_org().id)
    if already_exists:
        raise HTTPException(409, 'Workspace with such name already exists.')

    ws = await db_api.create_workspace(ws_dict)
    owner_group = await db_api.create_group({'workspace_id': ws.id, 'name': 'Owners', 'permissions': AccessMode.OWNER})
    group_user = await db_api.create_group_user({
        'workspace_id': ws.id,
        'group_id': owner_group.id,
        'user_id': ctx.current_session().user_id,
        'confirmed': True,
    })
    user_group = await db_api.create_group({'workspace_id': ws.id, 'name': 'Users', 'permissions': 0})
    dev_group = await db_api.create_group({
        'workspace_id': ws.id, 'name': 'Developers', 'permissions': policies.encode_permissions(
            [policies.AccessName.FILE_MANAGE, policies.AccessName.METRICS_MANAGE,
             policies.AccessName.ALL_CHATS_VIEW, policies.AccessName.ALL_CHATS_MANAGE],
        ),
    })
    manager_group = await db_api.create_group({
        'workspace_id': ws.id, 'name': 'Managers', 'permissions': policies.encode_permissions(
            [
                policies.AccessName.FILE_MANAGE,
                policies.AccessName.METRICS_MANAGE,
                policies.AccessName.ALL_CHATS_VIEW,
                policies.AccessName.ALL_CHATS_MANAGE,
                policies.AccessName.WORKSPACE_MANAGE,
                policies.AccessName.MEMBERS_MANAGE,
                policies.AccessName.GROUP_MANAGE,
            ],
        ),
    })

    return ORJSONResponse(content=ws.to_dict(), status_code=200)


async def get_workspace(workspace_id: int):
    ws = ctx.current_workspace()

    if ws.org_id != ctx.current_org().id:
        raise HTTPException(404, 'Workspace not found in the organization')

    ws_dict = ws.to_dict()

    ws_dict['user_permissions'] = policies.decode_permissions(ctx.current_permissions())
    async with base.session_context():
        ws_checked = await dataset_utils.dataset_index_check(ctx.current_org().id, ws)
        ws_dict['status'] = ws_checked.status

    return ORJSONResponse(content=ws_dict, status_code=200)


async def edit_workspace(workspace_id: int, edit_ws: EditWorkspace):
    if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.WORKSPACE_MANAGE):
        raise HTTPException(403, 'Forbidden')

    ws = ctx.current_workspace()
    edit_dict = edit_ws.model_dump(exclude_unset=True)
    if 'config' in edit_dict:
        config = edit_dict['config']
        if 'engine_type' in config:
            if config['engine_type'] != LANGCHAIN_TYPE and config['engine_type'] != LLAMA_TYPE:
                raise HTTPException(400, f'engine_type should be one of {{{LANGCHAIN_TYPE}, {LLAMA_TYPE}}}')

        edit_dict['config'] = edit_dict['config'] if edit_dict['config'] else {}

    if edit_ws.access_type and edit_ws.access_type not in models.WS_ACCESS_TYPES:
        raise HTTPException(400, f'Workspace access can only be one of these: {models.WS_ACCESS_TYPES}')

    async with base.session_context():
        old_config = ws.get_config()
        ws = await db_api.update_workspace(ws, edit_dict)
        new_config = ws.get_config()
        if edit_dict.get('access_type') and edit_dict['access_type'] != ws.access_type:
            await common_utils.clear_cache_for_session(ctx.current_session().id)

        if new_config.get('index') != old_config.get('index'):
            # Perform online check
            ws = await dataset_utils.dataset_index_check(ctx.current_org().id, ws)

    # Global config
    await handle_chat_topics_change(old_config, new_config, ws, ctx.current_org())

    # Chat config
    chat_config_old = old_config.get('apps_config', {}).get('chat', {})
    chat_config_new = new_config.get('apps_config', {}).get('chat', {})
    await handle_chat_topics_change(chat_config_old, chat_config_new, ws, ctx.current_org(), chat_config_new)

    return ORJSONResponse(content=ws.to_dict(), status_code=200)


async def handle_chat_topics_change(old_config, new_config, ws, org, chat_config=None):
    chat_topics_changed = old_config.get('chat_topics') != new_config.get('chat_topics')
    not_chat_topics_changed = old_config.get('not_chat_topics') != new_config.get('not_chat_topics')
    if chat_topics_changed or not_chat_topics_changed:
        app = models.Chat(config=chat_config) if chat_config else None
        manager = await engine_manager_chats.ChatEngineManager.init_async(org, ws, app)
        t = asyncio.create_task(manager.index_chat_topics())


async def clone_workspace(workspace_id: int, clone_ws: CloneWorkspace):
    if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.WORKSPACE_MANAGE):
        raise HTTPException(403, 'Forbidden')

    if not policies.is_allowed(
        ctx.current_org_permissions(),
        policies.OrgAccessMode.WORKSPACE_CREATE,
        mode_class=policies.OrgAccessMode
    ):
        raise HTTPException(403, 'Forbidden')

    ws = ctx.current_workspace()
    org = ctx.current_org()
    clone_dict = clone_ws.model_dump(exclude_unset=True)
    if 'config' not in clone_dict or not clone_dict['config']:
        clone_dict['config'] = ws.get_config()
    else:
        clone_dict['config'] = clone_dict['config']
    if not clone_ws.description:
        clone_dict['description'] = clone_ws.description
    if not clone_ws.description:
        clone_dict['display_name'] = clone_ws.display_name

    clone_dict['access_type'] = ws.access_type
    clone_dict['org_id'] = ws.org_id
    clone_dict.pop('clone_files', None)
    clone_dict.pop('clone_members', None)

    already_exists = await db_api.get_workspace(clone_dict['name'], org_id=org.id)
    if already_exists:
        raise HTTPException(409, 'Workspace with such name already exists.')

    new_ws = await db_api.create_workspace(clone_dict)

    # Clone members/groups
    if clone_ws.clone_members:
        groups = await db_api.list_groups(workspace_id=ws.id)
        members = await db_api.list_group_users(workspace_id=ws.id)
        group_map = {g.id: g for g in groups}
        group_member_map = collections.defaultdict(list)
        for u in members:
            group_member_map[u.group_id].append(u)

        for group_id, group in group_map.items():
            group_members = group_member_map[group_id]
            new_group = await db_api.create_group({
                'workspace_id': new_ws.id,
                'permissions': group.permissions,
                'name': group.name,
            })
            new_group_members = [
                {'workspace_id': new_ws.id, 'group_id': new_group.id, 'user_id': u.user_id, 'confirmed': True}
                for u in group_members
            ]
            await db_api.create_group_users(new_group_members)

    return ORJSONResponse(content=new_ws.to_dict(), status_code=200)


async def delete_workspace(workspace_id: int):
    if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.WORKSPACE_MANAGE):
        raise HTTPException(403, 'Forbidden')

    chats, _ = await db_api.list_chats(workspace_id)

    for chat in chats:
        await db_api.delete_messages(chat.id)
        await db_api.delete_message_outputs(chat.id)
    await db_api.delete_suggestions_for_chats([c.id for c in chats])

    await db_api.delete_groups(workspace_id)
    await db_api.delete_users_from_workspace(workspace_id)

    await db_api.delete_groups(workspace_id)
    await db_api.delete_users_from_workspace(workspace_id)

    await db_api.delete_metrics(workspace_id)
    await db_api.delete_workspace(workspace_id)

    return fastapi.Response(status_code=204)


async def get_workspace_members(workspace_id: int):
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE)

    groups = await db_api.list_groups(workspace_id)
    if not all_allowed:
        groups = [g for g in groups if g.id in ctx.current_groups()]

    groups_by_id = {g.id: g for g in groups}
    group_users = await db_api.list_group_users_for_groups([g.id for g in groups])

    users = await db_api.list_users([g.user_id for g in group_users])
    users_by_id = {u.id: u for u in users}

    result = []
    for gu in group_users:
        user = users_by_id.get(gu.user_id)
        group = groups_by_id.get(gu.group_id)
        item = {
            'confirmed': gu.confirmed,
            'member_id': gu.id,
        }
        if group:
            item.update({
                'group': {
                    'id': group.id,
                    'name': group.name,
                    'permissions': policies.decode_permissions(group.permissions),
                }
            })
        if user:
            item.update({
                'id': user.id,
                'login': user.login,
                'name': user.name,
                'info': user.info,
            })
        else:
            item.update({
                'id': None,
                'login': gu.user_login,
                'name': None,
                'info': None,
            })
        result.append(item)

    group_dicts = [g.to_dict() for g in groups]
    for g in group_dicts:
        g['permissions'] = policies.decode_permissions(g['permissions'])

    return ORJSONResponse(content={'members': result, 'groups': group_dicts}, status_code=200)


async def add_workspace_member(workspace_id: int, add_member: AddMember):
    return await groups.create_member(add_member.group_id, groups.GroupUser(login=add_member.login))


async def edit_workspace_member(workspace_id: int, member_id: int, edit_member: EditMember):
    group_user = await db_api.get_group_user_by_id(member_id)
    if not group_user:
        raise HTTPException(404, 'No such member in the group')

    group_id = group_user.group_id
    is_in_group = group_id in ctx.current_groups()
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE)
    allowed_in_group = is_in_group and policies.is_allowed(ctx.current_permissions(),
                                                           policies.AccessMode.MEMBERS_MANAGE)
    if not all_allowed and not allowed_in_group:
        raise HTTPException(403, 'Forbidden')

    new_group = await db_api.get_group_by_id(edit_member.group_id)
    if not new_group:
        raise HTTPException(404, 'No such group')

    if new_group.workspace_id != group_user.workspace_id:
        raise HTTPException(400, 'Incorrect group id')

    if new_group.id == group_id:
        raise HTTPException(400, 'Group ids are the same')

    new_group_user = await db_api.update_group_user(group_user, {'group_id': edit_member.group_id})
    u = await db_api.get_user_by_id(group_user.user_id)
    result = {
        'member_id': member_id,
        'group': {
            'id': new_group.id,
            'name': new_group.name,
            'permissions': policies.decode_permissions(new_group.permissions)
        },
        'confirmed': group_user.confirmed,
    }
    if u:
        result.update({
            'name': u.name,
            'login': u.login,
            'info': u.info,
            'id': u.id,
        })
    else:
        result.update({
            'name': None,
            'login': group_user.user_login,
            'info': None,
            'id': None,
        })

    await common_utils.clear_cache_for_user(group_user.user_id)

    return ORJSONResponse(status_code=200, content=result)


async def delete_workspace_member(workspace_id: int, member_id: int):
    group_user = await db_api.get_group_user_by_id(member_id)
    if not group_user:
        raise HTTPException(404, 'No such member in the group')

    group_id = group_user.group_id

    is_in_group = group_id in ctx.current_groups()
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE)
    allowed_in_group = is_in_group and policies.is_allowed(ctx.current_permissions(),
                                                           policies.AccessMode.MEMBERS_MANAGE)
    if not all_allowed and not allowed_in_group:
        raise HTTPException(403, 'Forbidden')

    group = await db_api.get_group_by_id(group_id)
    if group.name == 'Owners' and ctx.current_session().user_id == group_user.user_id:
        raise HTTPException(403, 'Can not delete self from the group "Owners"')

    ws = ctx.current_workspace()
    if ws.id != group.workspace_id:
        raise HTTPException(403, 'Not correct group id')

    await db_api.delete_group_user(group_user.id)

    return Response(status_code=204)
