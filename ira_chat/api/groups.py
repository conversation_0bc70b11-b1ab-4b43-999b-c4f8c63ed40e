import datetime
import logging
import os
import uuid
from typing import Optional

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse, Response
from pydantic import BaseModel

from ira_chat.context import context as ctx
from ira_chat.db import api as db_api
from ira_chat.e_mail import send
from ira_chat.policies import policies
from ira_chat.utils import utils

logger = logging.getLogger(__name__)


class Group(BaseModel):
    name: Optional[str] = None
    permissions: Optional[list[str]] = None


class GroupUser(BaseModel):
    login: str
    do_invite: Optional[bool] = False


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'groups'), tags=['Groups'])

    router.add_api_route("", list_groups, methods=['GET'], name='List groups')
    router.add_api_route("", create_group, methods=['POST'], name='Create group')
    router.add_api_route("/{group_id}", get_group, methods=['GET'], name='Get group')
    router.add_api_route("/{group_id}", update_group, methods=['PUT'], name='Update group')
    router.add_api_route("/{group_id}", delete_group, methods=['DELETE'], name='Delete group')
    router.add_api_route("/{group_id}/users", list_members, methods=['GET'], name='List users in group')
    router.add_api_route("/{group_id}/members", list_members, methods=['GET'], name='List users in group')
    router.add_api_route("/{group_id}/members/{member_id}", get_member, methods=['GET'], name='List users in group')
    router.add_api_route("/{group_id}/members", create_member, methods=['POST'], name='Add a user to a group')
    router.add_api_route("/{group_id}/users", create_member, methods=['POST'], name='Add a user to a group')
    router.add_api_route(
        "/{group_id}/users/{user_id}", delete_user,
        methods=['DELETE'], name='Delete a user from a group'
    )
    router.add_api_route(
        "/{group_id}/members/{member_id}", delete_member,
        methods=['DELETE'], name='Delete a user from a group'
    )
    router.add_api_route(
        "/{group_id}/members/{member_id}", send_invite_again_group_user,
        methods=['POST'], name='Send the invite to this user'
    )

    return router


async def list_groups():
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE)

    ws = ctx.current_workspace()
    groups = await db_api.list_groups(ws.id)
    if not all_allowed:
        groups = [g for g in groups if g.id in ctx.current_groups()]

    group_dicts = [g.to_dict() for g in groups]
    for g in group_dicts:
        g['permissions'] = policies.decode_permissions(g['permissions'])

    return JSONResponse(content={'groups': group_dicts}, status_code=200)


async def create_group(group_req: Group):
    raise HTTPException(403, 'Temporary disabled')
    if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE):
        raise HTTPException(403, 'Forbidden')

    ws = ctx.current_workspace()

    group_dict = group_req.dict(exclude_unset=True)
    if 'permissions' in group_dict:
        group_dict['permissions'] = policies.encode_permissions(group_dict['permissions'])
    if 'name' not in group_dict:
        raise HTTPException(400, 'Group must have a name')
    group_dict['workspace_id'] = ws.id

    group = await db_api.create_group(group_dict)
    new_group_dict = group.to_dict()
    new_group_dict['permissions'] = policies.decode_permissions(group_dict['permissions'])

    return JSONResponse(content=new_group_dict, status_code=200)


async def get_group(group_id: int):
    is_in_group = group_id in ctx.current_groups()
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE)

    if not is_in_group and not all_allowed:
        raise HTTPException(403, 'Forbidden')

    group = await db_api.get_group_by_id(group_id)
    group.permissions = policies.decode_permissions(group.permissions)

    return JSONResponse(content=group.to_dict(), status_code=200)


async def update_group(group_id: int, group_req: Group):
    raise HTTPException(403, 'Temporary disabled')
    is_in_group = group_id in ctx.current_groups()
    group_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE)
    if not group_allowed:
        raise HTTPException(403, 'Forbidden')

    group = await db_api.get_group_by_id(group_id)

    if group_req.name == 'Owners':
        raise HTTPException(403, 'Name is already taken')
    if group.name == 'Owners':
        raise HTTPException(403, 'Group "Owners" can not be changed.')

    group_dict = group_req.dict(exclude_unset=True)
    if 'permissions' in group_dict:
        group_dict['permissions'] = policies.encode_permissions(group_dict['permissions'])

    group = await db_api.update_group(group, group_dict)

    group_dict = group.to_dict()
    group_dict['permissions'] = policies.decode_permissions(group_dict['permissions'])
    return JSONResponse(content=group_dict, status_code=200)


async def delete_group(group_id: int):
    raise HTTPException(403, 'Temporary disabled')
    is_in_group = group_id in ctx.current_groups()
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE)
    allowed_in_group = is_in_group and policies.is_allowed(ctx.current_permissions(), policies.AccessMode.MEMBERS_MANAGE)
    if not allowed_in_group and not all_allowed:
        raise HTTPException(403, 'Forbidden')

    group = await db_api.get_group_by_id(group_id)
    if group.name == 'Owners':
        raise HTTPException(422, 'Unable to delete "Owners" group')

    ws = ctx.current_workspace()
    if ws.id != group.workspace_id:
        raise HTTPException(403, 'Not correct group id')

    await db_api.delete_users_from_group(group_id)
    await db_api.delete_group(group_id)

    return Response(status_code=204)


async def list_members(group_id: int):
    is_in_group = group_id in ctx.current_groups()
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE)
    if not is_in_group and not all_allowed:
        raise HTTPException(403, 'Forbidden')

    group = await db_api.get_group_by_id(group_id)

    ws = ctx.current_workspace()
    if ws.id != group.workspace_id:
        raise HTTPException(403, 'Not correct group id')

    group_users = await db_api.list_group_users(group_id)

    users = await db_api.list_users([g.user_id for g in group_users])
    users_by_id = {u.id: u for u in users}

    result = []
    for gu in group_users:
        user = users_by_id.get(gu.user_id)
        item = {
            'confirmed': gu.confirmed,
            'member_id': gu.id,
        }
        if user:
            item.update({
                'id': user.id,
                'login': user.login,
                'name': user.name,
                'info': user.info,
            })
        else:
            if gu.user_login:
                item.update({
                    'id': None,
                    'login': gu.user_login,
                    'name': None,
                    'info': None,
                })
        result.append(item)

    return JSONResponse(content={'users': result}, status_code=200)


async def get_member(group_id: int, member_id: int):
    is_in_group = group_id in ctx.current_groups()
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE)
    if not is_in_group and not all_allowed:
        raise HTTPException(403, 'Forbidden')

    group_user = await db_api.get_group_user_by_id(member_id)
    if not group_user:
        raise HTTPException(404, 'No such user in the group')

    ws = ctx.current_workspace()
    if ws.id != group_user.workspace_id:
        raise HTTPException(403, 'Not correct group id')

    if group_user.user_id:
        user = await db_api.get_user_by_id(group_user.user_id)
        allowed_keys = {'id', 'name', 'login', 'info'}
        # to_dict with filter by keys
        user_dict = {k: val for k, val in user.to_dict().items() if k in allowed_keys}
    else:
        user_dict = {'id': None, 'login': group_user.user_login, 'name': None, 'info': None}

    user_dict['confirmed'] = group_user.confirmed
    user_dict['member_id'] = group_user.id

    return JSONResponse(content=user_dict, status_code=200)


async def create_member(group_id: int, new_user: GroupUser):
    is_in_group = group_id in ctx.current_groups()
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE)
    allowed_in_group = is_in_group and policies.is_allowed(ctx.current_permissions(),
                                                           policies.AccessMode.MEMBERS_MANAGE)
    if not all_allowed and not allowed_in_group:
        raise HTTPException(403, 'Forbidden')

    group = await db_api.get_group_by_id(group_id)
    ws = ctx.current_workspace()

    ses = ctx.current_session()
    if ws.id != group.workspace_id:
        raise HTTPException(403, 'Not correct group id')

    user = await db_api.get_user_by_login(new_user.login, notfoundok=True)
    if user:
        exists = await db_api.get_group_user(group_id, user.id)
        if exists is not None:
            raise HTTPException(409, 'User already in the group')
        org_user = await db_api.get_org_user(ctx.current_org().id, user.id)
    else:
        org_user = await db_api.get_org_user(ctx.current_org().id, user_login=new_user.login)

    new_group_user = {
        'user_id': user.id if user else 0,
        'group_id': group_id,
        'workspace_id': ws.id,
        'confirmed': False,
        'user_login': new_user.login if not user else None
    }

    if new_user.do_invite or not org_user:
        invite_vals = {
            'id': str(uuid.uuid4()),
            'from_user_id': ses.user_id,
            'to_user_id': user.id if user else 0,
            'to_workspace_id': ws.id,
            'to_group_id': group_id,
            'to_user_login': new_user.login if not user else None,
            'expiry_time': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=1),
        }

        invite = await db_api.create_user_invite(invite_vals)

        base_url = utils.get_base_url(ctx.request.value)
        send.send_invite(new_user.login, base_url, ws, ctx.current_org(), invite)
    else:
        new_group_user['confirmed'] = org_user.confirmed

    group_user = await db_api.create_group_user(new_group_user)

    group_user_dict = group_user.to_dict()
    group_user_dict.pop('user_login')
    group_user_dict['login'] = new_user.login
    group_user_dict['member_id'] = group_user_dict.pop('id')
    group_user_dict['id'] = group_user_dict.pop('user_id')

    return JSONResponse(content=group_user_dict, status_code=200)


async def send_invite_again_group_user(group_id: int, member_id: int):
    is_in_group = group_id in ctx.current_groups()
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE)
    allowed_in_group = is_in_group and policies.is_allowed(ctx.current_permissions(),
                                                           policies.AccessMode.MEMBERS_MANAGE)
    if not all_allowed and not allowed_in_group:
        raise HTTPException(403, 'Forbidden')

    group_user = await db_api.get_group_user_by_id(member_id)
    if not group_user:
        raise HTTPException(404, 'No such user in the group')
    ws = ctx.current_workspace()

    if group_user.confirmed:
        raise HTTPException(400, 'Already confirmed')

    ses = ctx.current_session()
    if ws.id != group_user.workspace_id:
        raise HTTPException(403, 'Not correct group id')

    invite_vals = {
        'id': str(uuid.uuid4()),
        'from_user_id': ses.user_id,
        'to_user_id': group_user.user_id,
        'to_workspace_id': ws.id,
        'to_group_id': group_id,
        'to_user_login': group_user.user_login,
        'expiry_time': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=1),
    }

    user_login = group_user.user_login
    if not user_login:
        user = await db_api.get_user_by_id(group_user.user_id)
        user_login = user.login

    invite = await db_api.create_user_invite(invite_vals)

    base_url = utils.get_base_url(ctx.request.value)
    send.send_invite(user_login, base_url, ws, ctx.current_org(), invite)

    return JSONResponse(content=group_user.to_dict(), status_code=200)


async def delete_user(group_id: int, user_id: int):
    is_in_group = group_id in ctx.current_groups()
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE)
    allowed_in_group = is_in_group and policies.is_allowed(ctx.current_permissions(),
                                                           policies.AccessMode.MEMBERS_MANAGE)
    if not all_allowed and not allowed_in_group:
        raise HTTPException(403, 'Forbidden')

    group = await db_api.get_group_by_id(group_id)
    if group.name == 'Owners' and ctx.current_session().user_id == user_id:
        raise HTTPException(403, 'Can not delete self from the group "Owners"')

    ws = ctx.current_workspace()
    if ws.id != group.workspace_id:
        raise HTTPException(403, 'Not correct group id')

    group_user = await db_api.get_group_user(group_id, user_id)
    if not group_user:
        raise HTTPException(404, 'No such user in the group')

    await db_api.delete_group_user(group_user.id)

    return Response(status_code=204)


async def delete_member(group_id: int, member_id: int):
    is_in_group = group_id in ctx.current_groups()
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.GROUP_MANAGE)
    allowed_in_group = is_in_group and policies.is_allowed(ctx.current_permissions(),
                                                           policies.AccessMode.MEMBERS_MANAGE)
    if not all_allowed and not allowed_in_group:
        raise HTTPException(403, 'Forbidden')

    group_user = await db_api.get_group_user_by_id(member_id)
    if not group_user:
        raise HTTPException(404, 'No such user in the group')
    group = await db_api.get_group_by_id(group_id)
    if group.name == 'Owners' and ctx.current_session().user_id == group_user.user_id:
        raise HTTPException(403, 'Can not delete self from the group "Owners"')

    ws = ctx.current_workspace()
    if ws.id != group.workspace_id:
        raise HTTPException(403, 'Not correct group id')

    await db_api.delete_group_user(group_user.id)

    return Response(status_code=204)
