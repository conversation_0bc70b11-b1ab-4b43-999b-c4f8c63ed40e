import json
import logging
import os
from typing import Optional
from urllib.parse import urlparse

from fastapi import APIRouter, HTTPException
from fastapi.responses import ORJSONResponse
from pydantic import BaseModel
from starlette.responses import Response

from ira_chat.context import context as ctx
from ira_chat.db import api as db_api, base
from ira_chat.policies import policies
from ira_chat.services import webhooks

logger = logging.getLogger(__name__)


class WebhookModel(BaseModel):
    description: Optional[str] = None
    url: str
    headers: Optional[dict] = None
    config: Optional[webhooks.WebhookConfig] = webhooks.WebhookConfig()


class WebhookUpdate(BaseModel):
    description: Optional[str] = None
    url: Optional[str] = None
    headers: Optional[dict] = None
    config: Optional[webhooks.WebhookConfig] = None


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'webhooks'))

    router.add_api_route("", list_webhooks, methods=['GET'], name='Available webhook list')
    router.add_api_route("", create_webhook, methods=['POST'], name='Create a new webhook')
    router.add_api_route("/{webhook_id}", delete_webhook, methods=['DELETE'], name='DELETE webhook')
    router.add_api_route("/{webhook_id}", update_webhook, methods=['PUT'], name='Update webhook')
    router.add_api_route("/{webhook_id}/history", list_webhook_items, methods=['GET'], name='Webhook history')

    return router


async def _check_permission(org_id):
    is_admin = ctx.current_session().admin

    if not is_admin:
        org_user = await db_api.get_org_user(org_id, ctx.current_session().user_id)
        current_role = db_api.get_role_by_id(org_user.role_id)
        if not is_admin and not policies.is_allowed(
                current_role['permissions'], policies.OrgAccessMode.ORG_MANAGE, policies.OrgAccessMode
        ):
            raise HTTPException(403, 'Forbidden')


async def list_webhooks(org_id: int):
    async with base.session_context():
        await _check_permission(org_id)
        webhooks = await db_api.list_org_webhooks(org_id)

    hooks_dicts = [h.to_dict() for h in webhooks]

    return ORJSONResponse(content={'webhooks': hooks_dicts}, status_code=200)


async def list_webhook_items(org_id: int, webhook_id: int):
    async with base.session_context():
        await _check_permission(org_id)
        hook = await db_api.get_org_webhook_by_id(webhook_id)
        if hook.org_id != org_id:
            raise HTTPException(404, f"Not found Webhook for id={webhook_id}")

        webhook_items = await db_api.list_org_webhook_items(webhook_id)

    item_dicts = [i.to_dict() for i in webhook_items]

    return ORJSONResponse(content={'history': item_dicts}, status_code=200)


async def create_webhook(org_id: int, webhook_data: WebhookModel):
    await _check_permission(org_id)

    parsed = urlparse(webhook_data.url)
    if not parsed.scheme or not parsed.hostname or parsed.scheme not in ['http', 'https']:
        raise HTTPException(400, f'Invalid url: {webhook_data.url}')

    webhook = await db_api.create_org_webhook({
        'org_id': ctx.current_org().id,
        'url': webhook_data.url,
        'headers': json.dumps(webhook_data.headers),
        'description': webhook_data.description,
        'config': webhook_data.config.dict(),
    })

    return ORJSONResponse(content=webhook.to_dict(show_key=True), status_code=200)


async def update_webhook(org_id: int, webhook_id: int, update_req: WebhookUpdate):
    await _check_permission(org_id)

    webhook = await db_api.get_org_webhook_by_id(webhook_id)
    if webhook.org_id != ctx.current_org().id:
        raise HTTPException(404, f'OrganizationWebhook not found for id: {webhook_id}')

    update_dict = update_req.dict(exclude_unset=True)
    if not update_dict:
        raise HTTPException(400, 'Provide data to update')

    if 'headers' in update_dict:
        update_dict['headers'] = json.dumps(update_dict['headers'])
    webhook = await db_api.update_org_webhook(webhook, update_dict)

    return ORJSONResponse(content=webhook.to_dict())


async def delete_webhook(org_id: int, webhook_id: int):
    await _check_permission(org_id)

    webhook = await db_api.get_org_webhook_by_id(webhook_id)
    if webhook.org_id != ctx.current_org().id:
        raise HTTPException(404, f'OrganizationWebhook not found for id: {webhook_id}')

    await db_api.delete_org_webhook(webhook.id)

    return Response(status_code=204)
