import datetime
import functools
import logging
import os
import copy
from collections import defaultdict

from fastapi import APIRouter, HTTPException
from fastapi.responses import ORJSONResponse
from pydantic import BaseModel
import pandas as pd
import numpy as np

from ira_chat.context import context as ctx
from ira_chat.db import api as db_api, models, base
from ira_chat.api import common_utils
from ira_chat.policies import policies

logger = logging.getLogger(__name__)
additional_metrics = {'detection_count': 'count', 'chat_count': 'count', 'file_count': 'count'}
additional_metrics_table = {'detection_count': 'detections', 'chat_count': 'chats', 'file_count': 'files'}
dependable_metrics = {
    'grade_event_percentage': [models.METRIC_GRADE_EVENT, models.METRIC_MESSAGE],
    'correct_answer_percentage': [models.METRIC_CORRECT_ANSWER, models.METRIC_GRADE_EVENT],
    'incorrect_answer_percentage': [models.METRIC_INCORRECT_ANSWER, models.METRIC_GRADE_EVENT],
    'partial_answer_percentage': [models.METRIC_PARTIAL_ANSWER, models.METRIC_GRADE_EVENT],
    'messages_with_answer': [models.METRIC_MESSAGE, models.METRIC_EXCLUDED_ANSWER_EVENT, models.METRIC_NO_ANSWER_EVENT],
}


class MetricModel(BaseModel):
    start_date: datetime.datetime
    end_date: datetime.datetime
    message_count: int
    average_response_time_ms: int


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'metrics'))

    router.add_api_route("", available_metrics, methods=['GET'], name='Available metrics')
    router.add_api_route("/messages", messages_metrics, methods=['GET'], name='Get metrics about chats/messages')
    router.add_api_route("/all", messages_metrics, methods=['GET'], name='Get all aggregated metrics')
    router.add_api_route("/workspaces", workspace_metrics, methods=['GET'], name='Get metric table per workspace')

    return router


async def available_metrics():
    if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.METRICS_MANAGE):
        raise HTTPException(403, 'Forbidden')

    all_metrics = list(common_utils.available_metrics.keys())
    display_names = [common_utils.to_display_name(name) for name in all_metrics]
    app_types = [models.APP_TYPE_CHAT, models.APP_TYPE_DETECTION]
    return ORJSONResponse(
        content={'available': all_metrics, 'app_types': app_types, 'display_names': display_names},
        status_code=200
    )


async def messages_metrics(
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    app_type: str = None,
    metrics: str = None,
    app_id: int = None,
    object_id: int = None,
    insert_missing_values: str = '0',
    include_moving_average: int = None,
    include_lower_upper: bool = False,
    tag: str = None,
):
    insert_missing_values = common_utils.insert_missing_values_val(insert_missing_values)
    if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.METRICS_MANAGE):
        raise HTTPException(403, 'Forbidden')

    workspace = ctx.current_workspace()

    async with base.session_context():
        if workspace:
            ws_ids = [workspace.id]
        else:
            org = ctx.current_org()
            wss = await db_api.list_workspaces(org_id=org.id)
            ws_ids = [w.id for w in wss]

        if not start_date:
            start_date = models.now() - datetime.timedelta(days=1)
        if not end_date:
            end_date = models.now()

        result = await get_all_metrics(
            ws_ids,
            start_date,
            end_date,
            app_type,
            metrics,
            app_id,
            object_id,
            insert_missing_values=insert_missing_values,
            include_moving_average=include_moving_average,
            include_lower_upper=include_lower_upper,
            tag=tag,
        )

    return ORJSONResponse(content=result, status_code=200)


async def get_all_metrics(
    ws_ids: list[int],
    start_date: datetime.datetime,
    end_date: datetime.datetime,
    app_type: str = None,
    metrics: str = None,
    app_id: int = None,
    object_id: int = None,
    insert_missing_values: int = 0,
    include_moving_average: int = None,
    include_lower_upper: bool = False,
    tag: str = None,
):
    delta = end_date.date() - start_date.date()
    chat_count = await db_api.get_chat_count(workspace_ids=ws_ids)
    result = {
        'start_date': base.date_to_string(start_date),
        'end_date': base.date_to_string(end_date),
        'chat_count': chat_count,
    }
    if metrics:
        metrics = [m.strip() for m in metrics.split(',')]
    else:
        metrics = list(common_utils.available_metrics.keys())
    event_metrics = common_utils.available_metrics
    original_metrics = metrics.copy()
    for metric in original_metrics:
        if metric not in event_metrics and metric not in dependable_metrics:
            raise HTTPException(400, f'Metric {metric} does not exist')
        if metric in dependable_metrics:
            for dependable_metric in dependable_metrics[metric]:
                if dependable_metric not in metrics:
                    metrics.append(dependable_metric)
            metrics.remove(metric)

    filtered_metrics = {m: f for m, f in event_metrics.items() if m in metrics}

    exclude_metrics = []
    if app_type == models.APP_TYPE_CHAT:
        exclude_metrics = [models.METRIC_DETECTION_ITEM, models.METRIC_EXTRACTOR_ITEM, models.METRIC_EXTRACTOR_RESULT]
    elif app_type == models.APP_TYPE_DETECTION:
        exclude_metrics = [
            models.METRIC_MESSAGE, models.METRIC_MESSAGE_DELETED, models.METRIC_MESSAGE_RESPONSE_TIME,
            models.METRIC_CHAT_CREATED, models.METRIC_CHAT_DELETED, models.METRIC_EXTRACTOR_ITEM,
            models.METRIC_EXTRACTOR_RESULT
        ]
    filtered_metrics = {k: v for k, v in filtered_metrics.items() if k not in exclude_metrics}

    metric_aggs = {k: v for k, v in common_utils.available_metrics.items() if k in metrics}
    # metric_aggs = {k: v for k, v in common_utils.get_available_metrics().items()}

    # TODO: totals could be computed manually based on daily aggregations (with small error for avg aggregation)
    totals = await db_api.get_metrics(
        ws_ids, type_groups=metric_aggs, start_date=start_date, end_date=end_date,
        app_type=app_type, app_id=app_id, object_id=object_id, tag=tag
    )
    total_by_metric = {m['type']: m['value'] for m in totals}

    by_days_by_type = await db_api.get_all_metrics_by_days(
        workspace_ids=ws_ids, type_groups=metric_aggs,
        start_date=start_date, end_date=end_date,
        days=delta.days,  app_type=app_type,
        app_id=app_id, object_id=object_id, tag=tag, insert_missing_values=insert_missing_values
    )

    metric_names = common_utils.sorted_metric_names(list(filtered_metrics.keys()), common_utils.metric_views['org'])
    for metric_meta in metric_names:
        metric = metric_meta['name']
        total = total_by_metric.get(metric, 0)
        by_days = by_days_by_type.get(metric, default_by_days(start_date, end_date, insert_missing_values))

        if include_moving_average:
            by_days = add_moving_average(by_days, window=include_moving_average)
        if include_lower_upper:
            by_days = add_lower_upper_bounds(
                by_days, window=include_moving_average if include_moving_average else 3, clip=True
            )
        result[metric] = {
            'type': filtered_metrics[metric],
            'total': total,
            'by_days': by_days,
            'display_name': metric_meta['display_name'],
            'group': metric_meta['group'],
        }

    # Compute dependable metrics
    result['metric_names'] = metric_names
    result = compute_dependable_metrics(original_metrics, result, insert_missing_values)

    return result


def default_by_days(start, end, missing_value=0):
    start_dt = start.date()
    end_dt = end.date()
    dates = pd.date_range(start_dt, end_dt)
    return [{"date": str(dt.date()), "count": missing_value} for dt in dates]


def compute_dependable_metrics(original_metric_list, result, insert_missing_values):
    for original_metric in original_metric_list:
        if original_metric in dependable_metrics:
            result[original_metric] = dependable_metrics_funcs[original_metric](
                result, insert_missing_values=insert_missing_values
            )

            result['metric_names'].append({
                'name': original_metric,
                'display_name': common_utils.to_display_name(original_metric),
                'group': models.METRIC_GROUP_COMBINED,
            })
    return result


def compute_percentage_fn(base_name, total_name, override_name=None):
    return functools.partial(
        compute_percentage_metric, nominator_name=base_name, denominator_name=total_name, override_name=override_name
    )


def compute_percentage_metric(
    result, nominator_name: str, denominator_name: str,
    override_name=None, insert_missing_values=0
):
    nominator_total = result[nominator_name]['total']
    denominator_total = result[denominator_name]['total']

    total_percentage = (nominator_total if nominator_total else 0) / denominator_total if denominator_total else 0

    denominator_by_days = result[denominator_name]['by_days']
    nominator_by_days = copy.deepcopy(result[nominator_name]['by_days'])
    keys = ['count', 'moving_average', 'lower', 'upper']
    for i in range(len(denominator_by_days)):
        for key in keys:
            key_exists = key in nominator_by_days[i]
            if not key_exists:
                continue

            denominator_count = denominator_by_days[i].get(key)
            nominator_count = nominator_by_days[i].get(key)
            if denominator_count and not np.isnan(denominator_count):
                cnt = (nominator_count if nominator_count and not np.isnan(nominator_count) else 0) or 0
                nominator_by_days[i][key] = cnt / denominator_count
            else:
                nominator_by_days[i][key] = insert_missing_values

    type_ = override_name
    return {
        'type': type_,
        'total': total_percentage,
        'by_days': nominator_by_days,
        'display_name': common_utils.to_display_name(type_),
        'group': models.METRIC_GROUP_COMBINED,
    }


def compute_message_with_answer_fn(override_name=None):
    return functools.partial(
        compute_message_with_answer, override_name=override_name
    )


def compute_message_with_answer(
    result,
    override_name='messages_with_answer', insert_missing_values=0
):
    message_total = result['message']['total']
    no_answer_total = result['no_answer']['total']
    excluded_answer_total = result['excluded_answer']['total']
    result_total = message_total - (no_answer_total + excluded_answer_total)

    message_by_days = copy.deepcopy(result['message']['by_days'])
    no_answer_by_days = result['no_answer']['by_days']
    excluded_answer_by_days = result['excluded_answer']['by_days']
    keys = ['count', 'moving_average', 'lower', 'upper']

    for i in range(len(message_by_days)):
        for key in keys:
            msg_val = message_by_days[i].get(key, insert_missing_values)
            no_ans_val = no_answer_by_days[i].get(key, 0)
            excl_ans_val = excluded_answer_by_days[i].get(key, 0)
            if msg_val is not None and not np.isnan(msg_val):
                no_ans_val = no_ans_val if no_ans_val is not None and not np.isnan(no_ans_val) else 0
                excl_ans_val = excl_ans_val if excl_ans_val is not None and not np.isnan(excl_ans_val) else 0
                message_by_days[i][key] = msg_val - (no_ans_val + excl_ans_val)
            else:
                message_by_days[i][key] = insert_missing_values

    type_ = override_name
    return {
        'type': type_,
        'total': result_total,
        'by_days': message_by_days,
        'display_name': common_utils.to_display_name(type_),
        'group': models.METRIC_GROUP_COMBINED,
    }


def add_moving_average(data, window=7):
    if not data:
        return []
    df = pd.DataFrame(data)
    # df['date'] = df['date']
    # Compute the moving average using vectorized rolling mean on 'count'
    df['moving_average'] = df['count'].ffill().rolling(window=window, center=True, min_periods=1).mean()
    # Set moving_average to None where count is NaN
    df.loc[df['count'].isna(), 'moving_average'] = None
    return df.to_dict(orient='records')


def add_lower_upper_bounds(data, window=3, confidence=1, clip=None):
    if not data:
        return []
    df = pd.DataFrame(data)
    # df['date'] = pd.to_datetime(df['date'])

    # Set 'date' as index and fill missing count values
    # df.set_index('date', inplace=True)
    df['count_filled'] = df['count'].ffill()

    # Compute rolling mean and std in a vectorized way
    rolling_obj = df['count_filled'].rolling(window=window, center=True, min_periods=1)
    df['mean'] = rolling_obj.mean()
    df['std'] = rolling_obj.std()

    # Compute lower and upper bounds
    df['lower'] = df['mean'] - (confidence * df['std'])
    df['upper'] = df['mean'] + (confidence * df['std'])

    # df.reset_index(inplace=True)

    # Apply clipping if requested
    if clip:
        if clip is True:
            clip = (df['count'].min(), df['count'].max())
        df['lower'] = df['lower'].clip(lower=clip[0], upper=clip[1])
        df['upper'] = df['upper'].clip(lower=clip[0], upper=clip[1])

    # Set lower and upper to None where count is NaN
    df.loc[df['count'].isna(), ['lower', 'upper']] = None
    # Drop intermediate columns if not needed
    df.drop(columns=['count_filled', 'mean', 'std'], inplace=True)

    return df.to_dict(orient='records')


dependable_metrics_funcs = {
    'grade_event_percentage': compute_percentage_fn('grade_event', 'message', 'grade_event_percentage'),
    'correct_answer_percentage': compute_percentage_fn('correct_answer', 'grade_event', 'correct_answer_percentage'),
    'incorrect_answer_percentage': compute_percentage_fn('incorrect_answer', 'grade_event', 'incorrect_answer_percentage'),
    'partial_answer_percentage': compute_percentage_fn('partial_answer', 'grade_event', 'partial_answer_percentage'),
    'messages_with_answer': compute_message_with_answer_fn('messages_with_answer'),
}


async def workspace_metrics(
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    metrics: str = 'message,detection_item',
):
    if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.METRICS_MANAGE):
        raise HTTPException(403, 'Forbidden')

    workspace = ctx.current_workspace()
    async with base.session_context():
        if workspace:
            ws_ids = [workspace.id]
            wss = [workspace]
        else:
            org = ctx.current_org()
            wss = await db_api.list_workspaces(org_id=org.id)
            ws_ids = [w.id for w in wss]

        if not start_date:
            start_date = models.now() - datetime.timedelta(days=1)
        if not end_date:
            end_date = models.now()

        metrics = [m.strip() for m in metrics.split(',')]
        event_metrics = common_utils.available_metrics

        event_metrics.update(additional_metrics)
        for metric in metrics:
            if metric not in event_metrics:
                raise HTTPException(400, f'Metric {metric} does not exist')

        chat_count = await db_api.get_chat_count(workspace_ids=ws_ids)
        detection_count = await db_api.get_detection_count(workspace_ids=ws_ids)
        result = {
            'start_date': base.date_to_string(start_date),
            'end_date': base.date_to_string(end_date),
            'chat_count': chat_count,
            'detection_count': detection_count,
        }
        filtered_metrics = {m: f for m, f in event_metrics.items() if m in metrics}
        ws_map = {ws.id: ws for ws in wss}

        per_workspace = defaultdict(dict)
        metric_names = common_utils.sorted_metric_names(list(filtered_metrics.keys()), common_utils.metric_views['ws'])
        for metric_meta in metric_names:
            metric = metric_meta['name']
            agg_func = filtered_metrics[metric]
            if metric in additional_metrics:
                grouped_count = await db_api.get_grouped_count(additional_metrics_table[metric], workspace_ids=ws_ids)
                for ws_id in ws_ids:
                    per_workspace[ws_id][metric] = {'type': agg_func, 'total': grouped_count.get(ws_id, 0)}
            else:
                total_list = await db_api.get_metric(
                    ws_ids, start_date=start_date, end_date=end_date, metric_type=metric,
                    agg_func=agg_func, agg_groups=['workspace_id']
                )
                total_by_workspace = {t['workspace_id']: t for t in total_list}
                for ws in wss:
                    if ws.id in total_by_workspace:
                        per_workspace[ws.id][metric] = {'type': agg_func, 'total': total_by_workspace[ws.id]['count']}
                    else:
                        per_workspace[ws.id][metric] = {'type': agg_func, 'total': 0}

    # Squash to list
    per_workspace = [{'workspace_id': ws_id, 'metrics': v} for ws_id, v in per_workspace.items()]
    # Enrich data
    for ws_metrics in per_workspace:
        ws_metrics['name'] = ws_map[ws_metrics['workspace_id']].name
        ws_metrics['display_name'] = ws_map[ws_metrics['workspace_id']].display_name

    result['workspaces'] = per_workspace
    result['metric_names'] = metric_names
    return ORJSONResponse(content=result, status_code=200)
