import asyncio
import collections
import copy
import datetime
import json
import operator
import logging
import mimetypes
import os
import time
from typing import Optional, Literal

import pydantic
from fastapi import APIRouter, HTTPException
from fastapi import UploadFile, Form
from fastapi.responses import ORJSONResponse, Response
from pydantic import BaseModel
from starlette.responses import StreamingResponse

from ira_chat.api import extractor as extractor_api
from ira_chat.config.shared_config import SharedConfig
from ira_chat.context import context as ctx
from ira_chat.db import api as db_api, models, base
from ira_chat.policies import policies
from ira_chat.services import timeline_utils, jobs
from ira_chat.services.dataset_manager import get_storage_dataset_file_name
from ira_chat.services.engine_manager_timeline import TimelineEngineManager
from ira_chat.utils import json_utils, utils

logger = logging.getLogger(__name__)
processing_max_ping_time = datetime.timedelta(minutes=20)


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'timelines'))

    router.add_api_route("", list_timelines, methods=['GET'], name='List timelines')
    # router.add_api_route("/metrics", list_timeline_metrics, methods=['GET'], name='List timelines metrics')
    router.add_api_route("", create_timeline, methods=['POST'], name='Create timeline')
    router.add_api_route("/{timeline_id}", get_timeline, methods=['GET'], name='Get timeline')
    router.add_api_route("/{timeline_id}", update_timeline, methods=['PUT'], name='Update timeline')
    router.add_api_route("/{timeline_id}", delete_timeline, methods=['DELETE'], name='Delete timeline')

    router.add_api_route("/{timeline_id}/files/check_hash", all_files_check_hash, methods=['GET'],
                         name='Check if file exists')
    router.add_api_route("/{timeline_id}/files/{file_id}", get_file_timeline, methods=['GET'],
                         name='Get a file timeline by id')
    router.add_api_route("/{timeline_id}/files/{file_id}/download", get_file_timeline_download, methods=['GET'],
                         name='Download a file timeline by id')

    router.add_api_route("/{timeline_id}/points", create_point_timeline, methods=['POST'], name='Upload a point timeline')
    router.add_api_route("/{timeline_id}/points", list_points_timeline, methods=['GET'], name='List points timeline')
    router.add_api_route("/{timeline_id}/points/by-date/{date}", get_timeline_point_by_date, methods=['GET'], name='Get a point timeline by date')
    router.add_api_route("/{timeline_id}/points/{point_id}", get_point_timeline, methods=['GET'], name='Get a point timeline')
    router.add_api_route("/{timeline_id}/points/{point_id}", update_point_timeline, methods=['PUT'], name='Update a point timeline')
    router.add_api_route("/{timeline_id}/points/{point_id}", delete_point_timeline, methods=['DELETE'], name='Delete a point timeline')

    router.add_api_route("/{timeline_id}/points/{point_id}/available_files", list_all_files_point, methods=['GET'], name='List available files for a point')
    router.add_api_route("/{timeline_id}/points/{point_id}/available_files/check_hash", all_files_check_hash, methods=['GET'], name='Check if file exists')
    router.add_api_route("/{timeline_id}/points/{point_id}/files", upload_file_timeline, methods=['POST'], name='Upload a file timeline')
    router.add_api_route("/{timeline_id}/points/{point_id}/files", list_files_timeline, methods=['GET'], name='List files timeline')
    router.add_api_route("/{timeline_id}/points/{point_id}/files/{file_id}", get_file_timeline_point, methods=['GET'], name='Get a file timeline')
    router.add_api_route("/{timeline_id}/points/{point_id}/files/{file_id}/contents", get_file_timeline_contents, methods=['GET'], name='Get a file timeline index content')
    router.add_api_route("/{timeline_id}/points/{point_id}/files/{file_id}", update_file_timeline, methods=['PUT'], name='Update a file in timeline point')
    router.add_api_route("/{timeline_id}/points/{point_id}/files/{file_id}", delete_file_timeline, methods=['DELETE'], name='Delete a file timeline')
    router.add_api_route("/{timeline_id}/points/{point_id}/files/{file_id}/reindex", reindex_file_timeline, methods=['PUT'], name='Reindex a file timeline')
    router.add_api_route("/{timeline_id}/points/{point_id}/files/{file_id}/download", download_file_timeline, methods=['GET'], name='Download a file timeline')

    router.add_api_route("/{timeline_id}/process", process_timeline, methods=['POST'], name='Process timeline')
    router.add_api_route("/{timeline_id}/points/{point_id}/process", process_timeline_point, methods=['POST'], name='Process timeline point')
    router.add_api_route("/{timeline_id}/points/{point_id}/process_extractor", process_timeline_point_extractor, methods=['POST'], name='Process timeline underlying extractor')

    router.add_api_route("/{timeline_id}/historical_result", timeline_historical_result, methods=['GET'], name='Get historical data for timeline')
    router.add_api_route("/{timeline_id}/points/{point_id}/historical_result", point_historical_result, methods=['GET'], name='Get historical data for point')

    router.add_api_route("/{timeline_id}/result", get_timeline_last_result, methods=['GET'], name='Last timeline result')
    router.add_api_route("/{timeline_id}/points/{point_id}/result", get_timeline_point_last_result, methods=['GET'], name='Last timeline point result')
    router.add_api_route("/{timeline_id}/points/{point_id}/result/csv", get_timeline_result_csv, methods=['GET'], name='Get timeline result csv')
    router.add_api_route("/{timeline_id}/points/{point_id}/result/xlsx", get_timeline_result_xlsx, methods=['GET'], name='Get timeline result xlsx')

    router.add_api_route("/{timeline_id}/points/{point_id}/changes", list_point_changes, methods=['GET'], name='List point changes')
    router.add_api_route("/{timeline_id}/points/{point_id}/changes", add_point_change, methods=['POST'], name='Add point change')
    router.add_api_route("/{timeline_id}/points/{point_id}/changes/{change_id}", delete_point_change, methods=['DELETE'], name='Delete point change')

    router.add_api_route("/{timeline_id}/points/{point_id}/results_extractor", list_timeline_point_results_extractor, methods=['GET'], name='List timeline results')
    router.add_api_route("/{timeline_id}/points/{point_id}/results_extractor/{result_id}", get_timeline_point_result_extractor, methods=['GET'], name='Get timeline result')
    router.add_api_route("/{timeline_id}/points/{point_id}/results_extractor/{result_id}/csv", get_timeline_result_extractor_csv, methods=['GET'], name='Get timeline result csv')
    router.add_api_route("/{timeline_id}/points/{point_id}/results_extractor/{result_id}/xlsx", get_timeline_result_extractor_xlsx, methods=['GET'], name='Get timeline result xlsx')
    router.add_api_route("/{timeline_id}/points/{point_id}/results_extractor/{result_id}", delete_timeline_point_result_extractor, methods=['DELETE'], name='Delete timeline result')

    # router.add_api_route("/{timeline_id}/graph", get_timeline_graph, methods=['GET'], name='Get timeline graph metrics')
    # router.add_api_route("/{timeline_id}/items_metrics", get_items_metrics, methods=['GET'], name='Get all items metrics')
    # router.add_api_route("/{timeline_id}/items", list_timeline_items, methods=['GET'], name='List items')

    # router.add_api_route("/{timeline_id}/items", create_timeline_item, methods=['POST'], name='Post an item')
    # router.add_api_route("/{timeline_id}/items/{item_id}", get_timeline_item, methods=['GET'], name='Get item')
    # router.add_api_route("/{timeline_id}/items/{item_id}/metrics", get_item_metrics, methods=['GET'], name='Get item metrics')

    # router.add_api_route("/{timeline_id}/items/{item_id}", edit_item, methods=['PUT'], name='Edit an item')
    # router.add_api_route(
    #     "/{timeline_id}/items/{item_id}", delete_timeline_item, methods=['DELETE'], name='Delete a item'
    # )

    return router


class Timeline(BaseModel):
    config: Optional[dict] = None
    meta: Optional[dict] = None
    data_schema: Optional[dict] = pydantic.Field(None, description='data schema')
    description: Optional[str] = None
    title: str


class TimelineUpdate(BaseModel):
    config: Optional[dict] = None
    meta: Optional[dict] = None
    data_schema: Optional[dict] = pydantic.Field(None, description='data schema')
    description: Optional[str] = None
    title: Optional[str] = None


class TimelinePoint(BaseModel):
    description: Optional[str] = None
    title: str
    date: datetime.date


class TimelinePointUpdate(BaseModel):
    description: Optional[str] = None
    title: Optional[str] = None
    date: Optional[datetime.date] = None
    note: Optional[str] = None


class NewChange(BaseModel):
    description: Optional[str] = None
    data: Optional[dict] = None
    diff: Optional[dict] = None


class TimelineItemCreate(BaseModel):
    input: str


async def list_timelines(
    limit: int = 100,
    page: Optional[int] = None,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
):
    ses = ctx.current_session()
    ws = ctx.current_workspace()
    paginate = page is not None
    params = dict(
        workspace_id=ws.id,
        limit=limit,
        page=page or 1,
        order=order,
        desc=desc,
        q=q,
    )

    if not ws:
        raise HTTPException(400, 'Workspace is not set')

    async with base.session_context():
        if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW):
            timelines, count = await db_api.list_timelines(owner_id=ses.user_id, **params)
            tl_dicts = [t.to_dict() for t in timelines]
        else:
            timelines, count = await db_api.list_timelines(**params)
            tl_dicts = [t.to_dict() for t in timelines]
            user_ids = list(set([c.owner_id for c in timelines]))
            if user_ids:
                users = await db_api.list_users(user_ids)
                user_map = {u.id: u for u in users}
            else:
                user_map = {}

            for timeline in tl_dicts:
                if timeline['owner_id'] in user_map:
                    timeline['owner_login'] = user_map[timeline['owner_id']].login
                    timeline['owner_name'] = user_map[timeline['owner_id']].name

    if paginate:
        return ORJSONResponse(content={'items': tl_dicts, 'count': count, 'limit': limit, 'page': page})
    return ORJSONResponse(content=tl_dicts)


async def create_timeline(req: Timeline):
    ses = ctx.current_session()

    timeline: models.Timeline = await db_api.create_timeline({
        'owner_id': ses.user_id,
        'status': utils.build_status(models.STATUS_SUCCESS, None),
        'has_updates': False,
        'workspace_id': ctx.current_workspace().id,
        'title': req.title,
        'config': req.config or {},
        'data_schema': json.dumps(req.data_schema or {}),
        'description': req.description,
        'meta': req.meta,
    })

    return ORJSONResponse(content=timeline.to_dict())


async def get_timeline(timeline_id: int):
    async with base.session_context():
        timeline = await _get_timeline(timeline_id, edit=False)

        points = await db_api.list_timeline_points(timeline_id=timeline.id, order='date', desc=False)

    point_dicts = [f.to_dict() for f in points]
    result = timeline.to_dict()
    result['points'] = point_dicts

    return ORJSONResponse(content=result)


async def update_timeline(timeline_id: int, req: TimelineUpdate):
    async with base.session_context():
        timeline = await _get_timeline(timeline_id, edit=True)

        points = await db_api.list_timeline_points(timeline_id=timeline_id)

        req_update = req.model_dump(exclude_unset=True)
        schema_change = False
        if 'data_schema' in req_update:
            req_update['data_schema'] = json.dumps(req_update.pop('data_schema'))
            if req.data_schema != json.loads(timeline.data_schema):
                schema_change = len(points) > 0
                req_update['has_updates'] = schema_change

        if 'config' in req_update:
            req_update['config'] = req_update['config']

        timeline: models.Timeline = await db_api.update_timeline(timeline, req_update)
        points = await db_api.list_timeline_points(timeline_id=timeline.id)
        if points:
            extractors, _ = await db_api.list_extractors(ids=[p.extractor_id for p in points])
        else:
            extractors = []
        # Update schema and config of underlying extractors
        # need to preserve index config!
        for ext in extractors:
            ext_config = json_utils.dict_merge(ext.get_config(), timeline.get_config())
            ext_config['index'] = ext.get_config().get('index')
            await db_api.update_extractor(
                ext,
                {'schema': timeline.data_schema, 'config': ext_config, 'has_updates': schema_change}
            )

        if schema_change:
            await db_api.update_timeline_points(timeline_id, new_values={'status': utils.build_status(models.STATUS_UPDATED)})

    return ORJSONResponse(content=timeline.to_dict())


async def delete_timeline(timeline_id: int):
    _ = await _get_timeline(timeline_id, edit=True)

    points = await db_api.list_timeline_points(timeline_id=timeline_id)
    for point in points:
        await extractor_api.delete_extractor(point.extractor_id, delete_dataset=True)

    await db_api.delete_timeline_point_changes(timeline_id=timeline_id)
    await db_api.delete_timeline_points(timeline_id)
    await db_api.delete_timeline(timeline_id)

    return Response(status_code=204)


async def list_points_timeline(timeline_id: int):
    async with base.session_context():
        timeline = await _get_timeline(timeline_id, edit=False)
        points = await db_api.list_timeline_points(timeline_id=timeline.id, order='date', desc=False)
        datasets, extractors = await timeline_utils.list_datasets_for_points(ctx.current_org().id, points, get_extractors=True)
        if datasets:
            files = await db_api.list_dataset_files(dataset_ids=[d.id for d in datasets], order='name', desc=False)
        else:
            files = []

        extractor_point_map = {p.extractor_id: p.id for p in points}
        file_dataset_map = {f.id: f.dataset_id for f in files}
        dataset_name_to_extractor = {e.get_dataset_index()[0]: e for e in extractors}
        try:
            dataset_to_extractor = {d.id: dataset_name_to_extractor[d.name] for d in datasets}
            file_to_point = {f.id: extractor_point_map[dataset_to_extractor[file_dataset_map[f.id]].id] for f in files}
            point_to_files = collections.defaultdict(list)
            for f in files:
                point_to_files[file_to_point[f.id]].append(f.to_dict())
        except Exception as e:
            logger.error(str(e))
            point_to_files = {}

    point_dicts = [f.to_dict() for f in points]
    for point_dict in point_dicts:
        # point_dict['extractor'] = extractor_map[point_dict['extractor_id']].to_dict()
        point_dict['files'] = point_to_files.get(point_dict['id'], [])

    return ORJSONResponse(content=point_dicts)


@base.session_aware()
async def _get_timeline_point(timeline_id, point_id, edit=True, session=None) -> models.TimelinePoint:
    ses = ctx.current_session()
    point = await db_api.get_timeline_point_by_id(point_id, session=session)

    perm_check = policies.AccessMode.ALL_CHATS_MANAGE if edit else policies.AccessMode.ALL_CHATS_VIEW

    all_chat_allowed = policies.is_allowed(ctx.current_permissions(), perm_check)
    if ses.user_id != point.owner_id and not all_chat_allowed:
        raise HTTPException(403, 'Must be the owner to operate with a timeline point')

    if point.timeline_id != timeline_id:
        raise HTTPException(404, f"Not found timeline point by id: {point_id}")

    return point


@base.session_aware()
async def _get_timeline(timeline_id, edit=True, session=None) -> models.Timeline:
    ses = ctx.current_session()
    ws = ctx.current_workspace()

    perm_check = policies.AccessMode.ALL_CHATS_MANAGE if edit else policies.AccessMode.ALL_CHATS_VIEW
    all_chats_allowed = policies.is_allowed(ctx.current_permissions(), perm_check)
    timeline = await db_api.get_timeline_by_id(timeline_id, session=session)

    if ses.user_id != timeline.owner_id and not all_chats_allowed:
        raise HTTPException(403, 'Forbidden')
    if ws.id != timeline.workspace_id:
        raise HTTPException(404, 'Not found')

    return timeline


async def get_timeline_point_by_date(timeline_id: int, date: datetime.date):
    async with base.session_context():
        tl = await _get_timeline(timeline_id, edit=False)
        point_db = await db_api.get_timeline_point_by_date(timeline_id, date)
        if point_db:
            point_dict = point_db.to_dict()

            ext, ext_dict = await extractor_api.get_extractor_obj(point_db.extractor_id)
            point_dict['files'] = ext_dict['files']

    return ORJSONResponse(content=point_dict if point_db else point_db)


async def get_point_timeline(timeline_id: int, point_id: int):
    # point_dict = await get_point_timeline_internal(timeline_id, point_id)
    async with base.session_context():
        point_db = await _get_timeline_point(timeline_id, point_id, edit=False)
        point_dict = point_db.to_dict()

        ext, ext_dict = await extractor_api.get_extractor_obj(point_db.extractor_id)
        # point_dict['extractor'] = ext
        point_dict['files'] = ext_dict['files']

    return ORJSONResponse(content=point_dict)


async def update_point_timeline(timeline_id: int, point_id: int, req: TimelinePointUpdate):
    # point_dict = await get_point_timeline_internal(timeline_id, point_id)
    update_req = req.model_dump(exclude_unset=True)
    if req.date:
        update_req['status'] = utils.build_status(models.STATUS_UPDATED, None)
        await db_api.update_timeline(models.Timeline(id=timeline_id), {'has_updates': True})

    if not update_req:
        raise HTTPException(400, 'Provide data to update')

    async with base.session_context():
        point_db = await _get_timeline_point(timeline_id, point_id, edit=True)
        point_db = await db_api.update_timeline_point(point_db, update_req)

        point_dict = point_db.to_dict()
        ext, ext_dict = await extractor_api.get_extractor_obj(point_db.extractor_id)
        point_dict['extractor'] = ext_dict

    return ORJSONResponse(content=point_dict)


async def create_point_timeline(timeline_id: int, req: TimelinePoint):
    ses = ctx.current_session()
    ws = ctx.current_workspace()

    async with base.session_context() as session:
        timeline = await _get_timeline(timeline_id, edit=True)

        point_date = await db_api.get_timeline_point_by_date(timeline_id, req.date)
        if point_date:
            raise HTTPException(409, 'Point at this date already exists')

        ext_config = timeline.config.copy()
        ext_config.pop('index', None)
        ext: models.Extractor = await db_api.create_extractor({
            'owner_id': ses.user_id,
            'status': utils.build_status(models.STATUS_CREATED, None),
            'has_updates': False,
            'workspace_id': ctx.current_workspace().id,
            'title': req.title,
            'config': ext_config,
            'schema': timeline.data_schema,
            'description': req.description,
            'hidden': True,
        })
        await session.flush()
        ext = await extractor_api.auto_create_dataset_index(ext, ws.get_config(), ctx.current_org().id, ses.user_id)

        prev_point = await db_api.get_timeline_prev_point(timeline_id, models.TimelinePoint(date=req.date))
        if prev_point:
            prev_result = json.loads(prev_point.output)
        else:
            prev_result = json_utils.generate_dummy_json(ext.to_dict()['data_schema'], mode='default')

        point: models.TimelinePoint = await db_api.create_timeline_point({
            'workspace_id': ctx.current_workspace().id,
            'timeline_id': timeline_id,
            'extractor_id': ext.id,
            'owner_id': ses.user_id,
            'title': req.title,
            'description': req.description,
            'status': utils.build_status(models.STATUS_SUCCESS, None),
            'date': req.date,
            'output': json.dumps(prev_result),
        })

    return ORJSONResponse(content=point.to_dict())


async def delete_point_timeline(timeline_id: int, point_id: int):
    point = await _get_timeline_point(timeline_id, point_id)

    try:
        await extractor_api.delete_extractor(extractor_id=point.extractor_id, delete_dataset=True)
    except Exception as e:
        if '404' in str(e):
            logger.warning(f'Extractor not found for point_id={point_id}')
        else:
            pass

    await db_api.delete_timeline_point_changes(timeline_id=timeline_id, point_id=point_id)
    await db_api.delete_timeline_point(point_id)

    return Response(status_code=204)


async def upload_file_timeline(
    timeline_id: int,
    point_id: int,
    file: Optional[UploadFile],
    mode: Optional[Literal['llm', 'ocr']] = 'llm',
    replace: Optional[bool] = Form(False),
):
    async with base.session_context():
        point = await _get_timeline_point(timeline_id, point_id)
        all_points = await db_api.list_timeline_points(timeline_id=timeline_id, order='date', desc=False)

        extractor_id = point.extractor_id

        file_data = file.file.read()
        file.file.seek(0)
        datasets = await timeline_utils.list_datasets_for_points(ctx.current_org().id, all_points)
        found_file = await db_api.get_dataset_file_by_hash(hash=utils.hash_sha256(file_data), dataset_ids=[d.id for d in datasets])

        if found_file:
            if not replace:
                raise HTTPException(409, f'File already exists with name={found_file.name} and size={found_file.size}')

            await extractor_api.delete_file_extractor(found_file.extractor_id, found_file.id)
            found_point = await db_api.get_timeline_point_by_extractor(found_file.extractor_id)
            await db_api.update_timeline_point(found_point, {'status': utils.build_status(models.STATUS_UPDATED, None)})

        await db_api.update_timeline_point(point, {'status': utils.build_status(models.STATUS_PREPROCESSING, None)})

    await db_api.update_timeline(models.Timeline(id=timeline_id), {'has_updates': True})

    result = await extractor_api.upload_file_extractor(extractor_id, file=file, mode=mode)

    file_dict = json.loads(result.body)
    t = asyncio.create_task(watch_file_upload_point(point, file_dict))

    return result


async def update_file_timeline(
    timeline_id: int,
    point_id: int,
    file_id: str,
    file: Optional[UploadFile],
    mode: Optional[Literal['llm', 'ocr']] = 'llm',
):
    async with base.session_context():
        point = await _get_timeline_point(timeline_id, point_id)
        dataset = await timeline_utils.get_dataset_for_point(ctx.current_org().id, point)
        file_db = await db_api.get_dataset_file(file_id, dataset_id=dataset.id)

        extractor_id = point.extractor_id

        next_points = await db_api.list_timeline_points(timeline_id=timeline_id, start_date=point.date, order='date')
        if next_points:
            await db_api.update_timeline_points(ids=[p.id for p in next_points], new_values={'status': utils.build_status(models.STATUS_UPDATED)})

        await db_api.update_timeline_point(point, {'status': utils.build_status(models.STATUS_PREPROCESSING, None)})
        await db_api.update_timeline(models.Timeline(id=timeline_id), {'has_updates': True})

    result = await extractor_api.replace_file_extractor(extractor_id, file_db.id, file=file, mode=mode)

    file_dict = json.loads(result.body)
    t = asyncio.create_task(watch_file_upload_point(point, file_dict))

    return result


@jobs.job_tracker
async def watch_file_upload_point(point: models.TimelinePoint, file_dict: dict):
    try:
        file_status = file_dict['status']
        t = time.time()
        timeout = processing_max_ping_time.total_seconds()
        while file_status['status'] == models.STATUS_PROCESSING:
            await asyncio.sleep(1)
            if time.time() - t > timeout:
                file_db = await db_api.update_dataset_file(
                    models.DatasetFile(id=file_dict['id']),
                    {'status': utils.build_status(
                        models.STATUS_ERROR, 'Timeout exceeded while trying to process file. Please try again.'
                    )}
                )
            else:
                file_db = await db_api.get_dataset_file(file_dict['id'])
            file_dict = file_db.to_dict()
            file_status = file_dict['status']

        status = utils.build_status(models.STATUS_UPDATED)
        tl_update = {'has_updates': True}
        if file_status['status'] == models.STATUS_ERROR:
            status = file_status
            tl_update['status'] = file_status

        await db_api.update_timeline(models.Timeline(id=point.timeline_id), tl_update)
        next_points = await db_api.list_timeline_points(timeline_id=point.timeline_id, start_date=point.date, order='date')
        if next_points:
            await db_api.update_timeline_points(ids=[p.id for p in next_points], new_values={'status': utils.build_status(models.STATUS_UPDATED)})

        point = await db_api.get_timeline_point_by_id(point.id)
        dataset = await timeline_utils.get_dataset_for_point(ctx.current_org().id, point)

        files = await db_api.list_dataset_files(dataset.id)
        if not any(f.to_dict()['status']['status'] == models.STATUS_PROCESSING for f in files) and point.auto_process:
            was_error = any(f.to_dict()['status']['status'] == models.STATUS_ERROR for f in files)
            await db_api.update_timeline_point(point, {'auto_process': False})
            if not was_error:
                # Trigger processing
                await asyncio.sleep(1)
                logger.info(f'[FILE] TRIGGER PROCESSING [point id={point.id} name={point.title}]')
                t = asyncio.create_task(process_timeline_point_internal(point.timeline_id, point.id))
            else:
                await db_api.update_timeline_point(point, {'status': status})
                logger.info('[FILE] NOT TRIGGER PROCESSING due to error in files')
        elif not any(f.to_dict()['status']['status'] == models.STATUS_PROCESSING for f in files) and not point.auto_process:
            await db_api.update_timeline_point(point, {'status': status})

    except Exception as e:
        logger.exception(str(e))


async def list_all_files_point(timeline_id: int, point_id: int):
    ws = ctx.current_workspace()

    async with base.session_context():
        point = await _get_timeline_point(timeline_id, point_id, edit=False)

        # Get all points until the current one
        points = await db_api.list_timeline_points(
            workspace_id=ws.id, timeline_id=timeline_id, order='date', desc=False, end_date=point.date
        )
        extractor_point_map = {p.extractor_id: p for p in points}
        datasets, extractors = await timeline_utils.list_datasets_for_points(ctx.current_org().id, points, get_extractors=True)
        if datasets:
            files = await db_api.list_dataset_files(dataset_ids=[d.id for d in datasets], order='name', desc=False)
        else:
            files = []
        file_dicts = [f.to_dict() for f in files]

        file_dataset_map = {f.id: f.dataset_id for f in files}
        dataset_name_to_extractor = {e.get_dataset_index()[0]: e for e in extractors if e.get_dataset_index()[0]}
        dataset_to_extractor = {d.id: dataset_name_to_extractor[d.name] for d in datasets if d.name in dataset_name_to_extractor}
        file_to_point = {
            f.id: extractor_point_map[dataset_to_extractor[file_dataset_map[f.id]].id]
            for f in files
            if file_dataset_map[f.id] in dataset_to_extractor
        }

    for file_dict in file_dicts:
        file_dict['point_id'] = file_to_point[file_dict['id']].id if file_dict['id'] in file_to_point else None
        file_dict['point_date'] = base.dt_to_string(file_to_point[file_dict['id']].date) if file_dict['id'] in file_to_point else None

    file_dicts = sorted(file_dicts, key=operator.itemgetter('point_date', 'name'))

    return ORJSONResponse(content=file_dicts)


async def all_files_check_hash(timeline_id: int, hash: str, point_id: Optional[int]=None):
    ws = ctx.current_workspace()

    async with base.session_context():
        if point_id:
            point = await _get_timeline_point(timeline_id, point_id, edit=False)

            # Get all points until the current one
            points = await db_api.list_timeline_points(
                workspace_id=ws.id, timeline_id=timeline_id, end_date=point.date if point else None
            )

        else:
            points = await db_api.list_timeline_points(workspace_id=ws.id, timeline_id=timeline_id)

        datasets = await timeline_utils.list_datasets_for_points(ctx.current_org().id, points)
        if datasets:
            found_file = await db_api.get_dataset_file_by_hash(hash=hash, dataset_ids=[d.id for d in datasets])
        else:
            found_file = None

    return ORJSONResponse(content=found_file.to_dict() if found_file else found_file)


async def list_files_timeline(timeline_id: int, point_id: int):
    point = await _get_timeline_point(timeline_id, point_id)

    extractor_id = point.extractor_id
    return await extractor_api.list_files_extractor(extractor_id)


async def get_file_timeline_point(timeline_id: int, point_id: int, file_id: str):
    point = await _get_timeline_point(timeline_id, point_id, edit=False)

    extractor_id = point.extractor_id
    return await extractor_api.get_file_extractor(extractor_id, file_id)


async def get_file_timeline(timeline_id: int, file_id: str):
    # Check access
    await _get_timeline(timeline_id, edit=False)

    file_db = await db_api.get_dataset_file(file_id)
    return ORJSONResponse(content=file_db.to_dict())


async def get_file_timeline_download(timeline_id: int, file_id: str, inline: Optional[bool] = False):
    # Check access
    async with base.session_context():
        await _get_timeline(timeline_id, edit=False)

        file_db = await db_api.get_dataset_file(file_id)
        dataset = await db_api.get_dataset(file_db.dataset_id)
        storage_name = get_storage_dataset_file_name(ctx.current_org().name, dataset.name, file_db)

    media_type = mimetypes.guess_type(storage_name)[0]
    if not media_type:
        media_type = 'application/octet-stream'

    data = await SharedConfig().file_manager.read_file(storage_name)
    disposition = 'inline' if inline else 'attachment'

    return StreamingResponse(
        content=SharedConfig().file_manager.get_data_generator(data),
        media_type=media_type,
        headers={'content-length': str(file_db.size), 'Content-Disposition': f'{disposition}; filename="{file_db.name}"'}
    )


async def get_file_timeline_contents(timeline_id: int, point_id: int, file_id: str):
    point = await _get_timeline_point(timeline_id, point_id)

    extractor_id = point.extractor_id
    return await extractor_api.get_file_extractor_contents(extractor_id, file_id)


async def delete_file_timeline(timeline_id: int, point_id: int, file_id: str):
    point = await _get_timeline_point(timeline_id, point_id)

    extractor_id = point.extractor_id
    result = await extractor_api.delete_file_extractor(extractor_id, file_id)

    await db_api.update_timeline_point(point, {'status': utils.build_status(models.STATUS_UPDATED, None)})
    await db_api.update_timeline(models.Timeline(id=timeline_id), {'has_updates': True})

    return result


async def reindex_file_timeline(timeline_id: int, point_id: int, file_id: str):
    point = await _get_timeline_point(timeline_id, point_id)

    extractor_id = point.extractor_id
    return await extractor_api.reindex_file_extractor(extractor_id, file_id)


async def download_file_timeline(timeline_id: int, point_id: int, file_id: str, inline: Optional[bool] = False):
    point = await _get_timeline_point(timeline_id, point_id)

    extractor_id = point.extractor_id
    return await extractor_api.download_file_extractor(extractor_id, file_id, inline=inline)


async def process_timeline(timeline_id: int, force: Optional[bool] = False):
    ws = ctx.current_workspace()

    points = await db_api.list_timeline_points(workspace_id=ws.id, timeline_id=timeline_id, order='date', desc=False)
    if len(points) == 0:
        raise HTTPException(400, "Nothing to process, 0 points.")

    last_point = points[-1]
    point_id = last_point.id

    return await process_timeline_point(timeline_id, point_id)


async def _reanimate_files(
    files: list[models.DatasetFile], point,
    timeline,
    extractor_id_by_file: dict[str, int]
) -> bool:
    now = models.now()
    stale_files = []
    for file in files:
        fd = file.to_dict()
        if fd['status']['status'] in [models.STATUS_PROCESSING, models.STATUS_PROCESSING_META] and (now - file.updated_at) > processing_max_ping_time:
            logger.info(f'[FILE] Detected stuck file name={file.name} id={file.id}')
            stale_files.append(file)

    if stale_files:
        # Reanimate file processing
        async def process_stale_files(stale_files: list, point: models.TimelinePoint):
            for file in stale_files:
                # Get extractor id by file
                extractor_id = extractor_id_by_file[file.id]
                try:
                    await extractor_api.reindex_file_extractor(extractor_id, file_id=file.id)
                except Exception as e:
                    msg = f'{e.__class__.__name__}: {str(e)}'
                    msg = f'Failed processing file "{file.name}; Try to delete and upload it again"\n {msg}'
                    status = utils.build_status(models.STATUS_ERROR, msg)
                    await db_api.update_dataset_file(file, {'status': status})
                    await db_api.update_extractor(models.Extractor(id=extractor_id), {'status': status})
                await watch_file_upload_point(point, file.to_dict())

        await db_api.update_timeline_point(point, {'status': utils.build_status(models.STATUS_PREPROCESSING)})
        await db_api.update_dataset_files([f.id for f in stale_files], {'status': utils.build_status(models.STATUS_PROCESSING)})
        await process_stale_files(stale_files, point)

    return len(stale_files) > 0


async def process_timeline_point_internal(timeline_id: int, point_id: int, force: Optional[bool] = False) -> models.TimelinePoint:
    ws = ctx.current_workspace()

    async with base.session_context():
        # Validate access rights
        timeline = await _get_timeline(timeline_id, edit=True)
        point = await _get_timeline_point(timeline_id, point_id, edit=True)
        # Get all points until the current one
        points = await db_api.list_timeline_points(
            workspace_id=ws.id, timeline_id=timeline_id, order='date', desc=False, end_date=point.date
        )
        datasets = await timeline_utils.list_datasets_for_points(ctx.current_org().id, points)
        files = await db_api.list_dataset_files(dataset_ids=[d.id for d in datasets])
        point_by_dataset = {d.id: p for d, p in zip(datasets, points)}
        extractor_id_by_file = {f.id: point_by_dataset[f.dataset_id].extractor_id for f in files}

    reanimated = await _reanimate_files(files, point, timeline, extractor_id_by_file)

    async with base.session_context():
        if any(f.to_dict()['status']['status'] == models.STATUS_PROCESSING for f in files) or reanimated and not force:
            logger.info('[POINT] Some files are PROCESSING. Mark as AUTO PROCESS and wait.')
            point = await db_api.update_timeline_point(point, new_values={'auto_process': True})
            return point

        extractors, _ = await db_api.list_extractors(workspace_id=ws.id, ids=[p.extractor_id for p in points])

        status_update = {'status': utils.build_status(models.STATUS_PROCESSING)}
        await db_api.update_timeline(models.Timeline(id=timeline_id), status_update)
        await db_api.update_timeline_points(ids=[p.id for p in points], new_values=status_update)
        point.update(status_update)

    results = []
    # Process these extractors if not processed yet
    for extractor in extractors:
        if extractor.has_updates:
            # Process extractor first

            result = await extractor_api.process_extractor_internal(extractor.id)
            results.append(result)

    manager = TimelineEngineManager(ctx.current_org(), ws, app=timeline)
    t = asyncio.create_task(manager.process_point_result(extractors, results, points, point))

    return point


async def process_timeline_point(timeline_id: int, point_id: int, force: Optional[bool] = False):
    point = await process_timeline_point_internal(timeline_id, point_id, force)
    return ORJSONResponse(point.to_dict())


class HistoryItem(pydantic.BaseModel):
    datetime: str
    value: Optional[dict | str | None] = None
    source: str
    id: int
    point_id: int


async def timeline_historical_result(timeline_id: int):
    async with base.session_context():
        timeline = await _get_timeline(timeline_id, edit=False)
        history = await historical_result(timeline)

    return ORJSONResponse(content=history)


async def point_historical_result(timeline_id: int, point_id: int):
    async with base.session_context():
        point = await _get_timeline_point(timeline_id, point_id, edit=False)
        timeline = await _get_timeline(timeline_id, edit=False)
        history = await historical_result(timeline, point)

    return ORJSONResponse(content=history)


async def historical_result(timeline: models.Timeline, point: models.TimelinePoint = None):
    async with base.session_context():
        kwargs = dict(timeline_id=timeline.id, order='date', desc=False)
        if point:
            kwargs['end_date'] = point.date
        points = await db_api.list_timeline_points(**kwargs)
        changes = await db_api.list_timeline_point_changes(timeline_id=timeline.id)

        changes_map = collections.defaultdict(list)
        [changes_map[c.point_id].append(c) for c in changes]

        schema = json.loads(timeline.data_schema)
        # History result
        history = json_utils.generate_dummy_json(schema)
        # Actual result
        current_result = copy.deepcopy(history)
        for point in points:
            ext_result = await db_api.get_last_extractor_result(point.extractor_id)
            if ext_result and ext_result.output:
                next_result = json_utils.merge_jsons([current_result, json.loads(ext_result.output)])
                new_diff = json_utils.diff_json(current_result, next_result)
                meta_diff = HistoryItem(
                    datetime=base.date_to_string(ext_result.created_at), source='extractor',
                    id=ext_result.extractor_id, point_id=point.id,

                )
                history = json_utils.set_history_item(
                    current_result,
                    next_result,
                    new_diff,
                    meta_diff.model_dump(),
                    history,
                )
                current_result = next_result

            changes = changes_map[point.id]
            for change in changes:
                new_diff = json.loads(change.diff)
                next_result = json_utils.patch_json(current_result, new_diff)
                meta_diff = HistoryItem(
                    datetime=base.date_to_string(change.created_at), source='manual_change',
                    id=change.id, point_id=point.id,

                )
                history = json_utils.set_history_item(
                    current_result,
                    next_result,
                    new_diff,
                    meta_diff.model_dump(),
                    history,
                )
                current_result = next_result

    return history


async def list_point_changes(timeline_id: int, point_id: int):
    _ = await _get_timeline_point(timeline_id, point_id, edit=False)

    changes = await db_api.list_timeline_point_changes(timeline_id=timeline_id, point_id=point_id)
    changes_dict = [c.to_dict() for c in changes]
    return ORJSONResponse(content=changes_dict)


async def add_point_change(
    timeline_id: int,
    point_id: int,
    change: NewChange,
    force: Optional[bool] = False,
):
    point = await _get_timeline_point(timeline_id, point_id)
    timeline = await _get_timeline(timeline_id)

    schema = json.loads(timeline.data_schema)
    # empty_schema = json_utils.generate_dummy_json(schema)

    if not change.data and not change.diff:
        raise HTTPException(400, "One of diff or data must be provided")

    if change.data:
        try:
            json_utils.validate_schema(change.data, schema)
        except Exception as e:
            if not force:
                raise HTTPException(400, f'Provided data is not compatible with timeline schema: {str(e)}')

    if change.diff and not change.data:
        diff = change.diff
    else:
        diff = json_utils.diff_json(json.loads(point.output), change.data)

    point_change: models.TimelinePointChange = await db_api.create_timeline_point_change({
        'description': change.description,
        'data': json.dumps(change.data),
        'diff': json.dumps(diff) if diff else None,
        'owner_id': ctx.current_session().user_id,
        'timeline_id': timeline_id,
        'point_id': point_id,
        'workspace_id': ctx.current_workspace().id,
    })

    async with base.session_context():
        next_points = await db_api.list_timeline_points(
            workspace_id=ctx.current_workspace().id, timeline_id=timeline_id, start_date=point.date
        )
        if next_points:
            await db_api.update_timeline_points(
                ids=[p.id for p in next_points], new_values={'status': utils.build_status(models.STATUS_UPDATED)}
            )
            await db_api.update_timeline(models.Timeline(id=timeline_id), {'has_updates': True})

        if diff:
            prev_point = await db_api.get_timeline_prev_point(timeline_id, point)
            prev_result = json.loads(prev_point.output) if prev_point else None
            manager = TimelineEngineManager(ctx.current_org(), ctx.current_workspace(), app=timeline)

            # Re-compute result
            extractor = await db_api.get_extractor_by_id(point.extractor_id)
            # Updates the output in DB automatically
            await manager.process_single_point_result(point, extractor, prev_result)

    return ORJSONResponse(content=point_change.to_dict())


async def delete_point_change(timeline_id: int, point_id: int, change_id: int):
    _ = await _get_timeline_point(timeline_id, point_id, edit=True)

    change = await db_api.get_timeline_point_change_by_id(change_id)
    if change.point_id != point_id:
        raise HTTPException(404, f"Not found change for id {change_id}")

    await db_api.delete_timeline_point_change(change_id)
    return Response(status_code=204)


async def process_timeline_point_extractor(timeline_id: int, point_id: int):
    point = await _get_timeline_point(timeline_id, point_id)

    result = await extractor_api.process_extractor_internal(point.extractor_id)

    return ORJSONResponse(content=result.to_dict())


async def list_timeline_point_results_extractor(timeline_id: int, point_id: int):
    point = await _get_timeline_point(timeline_id, point_id, edit=False)
    return await extractor_api.list_extractor_results(point.extractor_id)


async def get_timeline_point_result_extractor(timeline_id: int, point_id: int, result_id: int):
    point = await _get_timeline_point(timeline_id, point_id, edit=False)
    return await extractor_api.get_extractor_result(point.extractor_id, result_id)


async def get_timeline_result_extractor_csv(timeline_id: int, point_id: int, result_id: int):
    point = await _get_timeline_point(timeline_id, point_id, edit=False)
    return await extractor_api.get_extractor_result_csv(point.extractor_id, result_id)


async def get_timeline_result_extractor_xlsx(timeline_id: int, point_id: int, result_id: int):
    point = await _get_timeline_point(timeline_id, point_id, edit=False)
    return await extractor_api.get_extractor_result_xlsx(point.extractor_id, result_id)


async def delete_timeline_point_result_extractor(timeline_id: int, point_id: int, result_id: int):
    point = await _get_timeline_point(timeline_id, point_id, edit=False)
    return await extractor_api.delete_extractor_result(point.extractor_id, result_id)


async def get_timeline_last_result(timeline_id: int):
    async with base.session_context():
        timeline = await _get_timeline(timeline_id, edit=False)

        ws = ctx.current_workspace()
        points = await db_api.list_timeline_points(
            workspace_id=ws.id, timeline_id=timeline_id, order='date', desc=False
        )
        if len(points) == 0:
            t_dict = timeline.to_dict()
            schema = t_dict['data_schema']
            empty_data = json_utils.generate_dummy_json(schema, mode='default')
            response = {
                'output': empty_data,
                'status': t_dict['status'],
                'point_id': -1,
                'history': None
            }
            return ORJSONResponse(content=response)

        result = utils.maybe_dict(points[-1].output)
        status = points[-1].status

        response = {
            'output': result,
            'status': status,
            'point_id': points[-1].id,
            'history': await historical_result(timeline, points[-1])
        }

    return ORJSONResponse(content=response)


async def get_timeline_point_last_result(timeline_id: int, point_id: int):
    async with base.session_context():
        timeline = await _get_timeline(timeline_id, edit=False)
        point = await _get_timeline_point(timeline_id, point_id, edit=False)
        result = utils.maybe_dict(point.output)
        status = point.status

        response = {
            'output': result,
            'status': status,
            'point_id': point.id,
            'history': await historical_result(timeline, point)
        }

    return ORJSONResponse(content=response)


async def get_timeline_result_csv(timeline_id: int, point_id: int):
    point = await _get_timeline_point(timeline_id, point_id, edit=False)
    result = utils.maybe_dict(point.output)

    csv_data = json_utils.json_to_csv(result)

    return Response(
        csv_data,
        media_type='text/csv',
        headers={'Content-Disposition': f'attachment; filename="{point.title}.csv"'}
    )


async def get_timeline_result_xlsx(timeline_id: int, point_id: int):
    point = await _get_timeline_point(timeline_id, point_id, edit=False)
    result = utils.maybe_dict(point.output)

    xlsx_data = json_utils.json_to_xlsx(result)

    return Response(
        xlsx_data,
        media_type=mimetypes.guess_type('file.xlsx')[0],
        headers={'Content-Disposition': f'attachment; filename="{point.title}.xlsx"'}
    )
