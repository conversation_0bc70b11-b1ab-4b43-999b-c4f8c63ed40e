import asyncio
import collections
import datetime
import logging
import mimetypes
import os
from collections import defaultdict
from typing import Optional

from fastapi import APIRouter, HTTPException
from fastapi import UploadFile, Form
from fastapi.responses import ORJSONResponse, Response, StreamingResponse
from pydantic import BaseModel

from ira_chat.api import common_utils
from ira_chat.config.shared_config import SharedConfig
from ira_chat.context import context as ctx
from ira_chat.db import api as db_api, base
from ira_chat.db import models
from ira_chat.policies import policies
from ira_chat.services.detection import get_detection_file_path, check_file_extension
from ira_chat.services.engine_manager import EngineManager
from ira_chat.utils import metric_utils
from ira_chat.utils.http_utils import estimate_url_extension

logger = logging.getLogger(__name__)


class Detection(BaseModel):
    type: str
    config: dict
    meta: Optional[dict] = None
    description: Optional[str] = None
    title: Optional[str] = None


class DetectionUpdate(BaseModel):
    type: Optional[str] = None
    config: Optional[dict] = None
    meta: Optional[dict] = None
    description: Optional[str] = None
    title: Optional[str] = None


def get_routers(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'detections'))

    router.add_api_route("", list_detections, methods=['GET'], name='List detections')
    router.add_api_route("/metrics", list_detection_metrics, methods=['GET'], name='List detections metrics')
    router.add_api_route("/graph", agg_detections_graph, methods=['GET'], name='Agg detections graph')
    router.add_api_route("", create_detection, methods=['POST'], name='Create detection')
    router.add_api_route("/{detection_id}", get_detection, methods=['GET'], name='Get detection')
    router.add_api_route("/{detection_id}", update_detection, methods=['PUT'], name='Update detection')
    router.add_api_route("/{detection_id}", delete_detection, methods=['DELETE'], name='Delete detection')
    router.add_api_route("/{detection_id}/items", list_detection_items, methods=['GET'], name='List detection items')
    router.add_api_route("/{detection_id}/graph", get_detection_graph, methods=['GET'], name='Get detection metric graph')
    router.add_api_route("/{detection_id}/items_metrics", get_detection_items_metrics, methods=['GET'], name='Get detection items metrics')
    router.add_api_route("/{detection_id}/items", create_detection_item, methods=['POST'], name='Create detection item')
    router.add_api_route("/{detection_id}/items/{item_id}", get_detection_item, methods=['GET'], name='Get detection item')
    router.add_api_route("/{detection_id}/items/{item_id}", update_detection_item, methods=['PUT'], name='Update detection item')
    router.add_api_route("/{detection_id}/items/{item_id}", delete_detection_item, methods=['DELETE'], name='Delete detection item')
    router.add_api_route("/{detection_id}/items/{item_id}/results/{result_id}", delete_detection_item_result, methods=['DELETE'], name='Delete detection item result')
    router.add_api_route("/{detection_id}/items/{item_id}/metrics", get_detection_item_metrics, methods=['GET'], name='Get detection item metrics')

    file_router = APIRouter(prefix=os.path.join(prefix, 'detection_files'))
    file_router.add_api_route("/{detection_file_id}", get_detection_file, methods=['GET'], name='Get detection file data')
    file_router.add_api_route("/{detection_file_id}", delete_detection_file, methods=['DELETE'], name='Delete detection file data (and keep metadata)')
    file_router.add_api_route("/{detection_file_id}/meta", get_detection_file_meta, methods=['GET'], name='Get detection file metadata')

    return [router, file_router]


additional_metrics = {'detection_item_count': 'count'}


async def list_detections(
    type: Optional[str] = None,
    limit: Optional[int] = 100,
    page: Optional[int] = None,
    order: Optional[str] = 'id',
    desc: Optional[bool] = False,
    q: Optional[str] = None,
):
    see_all = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    ws = ctx.current_workspace()

    if not ws:
        raise HTTPException(400, 'Workspace is not set')

    ses = ctx.current_session()
    params = dict(workspace_id=ws.id)
    if not see_all:
        params['owner_id'] = ses.user_id

    paginate = page is not None
    params.update({
        'limit': limit,
        'page': page,
        'order': order,
        'desc': desc,
        'type': type,
        'q': q,
    })

    async with base.session_context():
        detections, count = await db_api.list_detections(**params)
        owner_ids = [t.owner_id for t in detections]
        users = await db_api.list_users(owner_ids)
        user_map = {u.id: u for u in users}

    detection_dicts = [c.to_dict() for c in detections]
    for detection_dict in detection_dicts:
        detection_dict['owner_login'] = None
        detection_dict['files'] = []

        if detection_dict['owner_id'] in user_map:
            detection_dict['owner_login'] = user_map[detection_dict['owner_id']].login

    if paginate:
        return ORJSONResponse(content={'items': detection_dicts, 'count': count, 'page': page, 'limit': limit})
    else:
        return ORJSONResponse(content=detection_dicts, status_code=200)


async def list_detection_metrics(
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    metrics: str = 'detection_item,detection_item_count',
    insert_missing_values: str = '',
):
    insert_missing_values = common_utils.insert_missing_values_val(insert_missing_values)
    see_all = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    ws = ctx.current_workspace()

    if not ws:
        raise HTTPException(400, 'Workspace is not set')

    ses = ctx.current_session()
    params = dict(workspace_id=ws.id)
    if not see_all:
        params['owner_id'] = ses.user_id

    if not start_date:
        start_date = models.now() - datetime.timedelta(days=1)
    if not end_date:
        end_date = models.now()

    metrics = [m.strip() for m in metrics.split(',')]
    event_metrics = common_utils.get_available_metrics()

    event_metrics.update(additional_metrics)
    for metric in metrics:
        if metric not in event_metrics:
            raise HTTPException(400, f'Metric {metric} does not exist')

    result = {
        'start_date': base.date_to_string(start_date),
        'end_date': base.date_to_string(end_date),
    }
    filtered_metrics = {m: f for m, f in event_metrics.items() if m in metrics}

    async with base.session_context():
        apps, _ = await db_api.list_detections(**params)
        apps_map = {app.id: app for app in apps}

        per_app_id = defaultdict(dict)
        metric_types = list(filtered_metrics.keys())
        total_list = await db_api.get_metrics(
            [ws.id], start_date=start_date, end_date=end_date, metric_types=metric_types,
            app_type=models.APP_TYPE_DETECTION,
            type_groups=filtered_metrics, agg_groups=['app_id']  # , 'type']
        )
        metrics_in_result = {t['type'] for t in total_list}
        for t in total_list:
            app_id = t['app_id']
            metric, value = t['type'], t['value']
            if app_id in apps_map:
                per_app_id[app_id][metric] = {'type': filtered_metrics[metric], 'total': value}
        for app_id in apps_map:
            if app_id in per_app_id:
                absent_metrics = metrics_in_result - set(per_app_id[app_id].keys())
                for m in absent_metrics:
                    per_app_id[app_id][m] = {'type': filtered_metrics[m], 'total': insert_missing_values}
            else:
                for metric in metric_types:
                    if metric in metrics_in_result:
                        per_app_id[app_id][metric] = {'type': filtered_metrics[metric], 'total': insert_missing_values}

        # totals = await get_agg_metrics(ws.id, start_date, end_date, total_metrics)

    for app in apps:
        per_app_id[app.id]['last_activity'] = {'type': 'datetime', 'value': app.metrics.get('last_activity')}
        per_app_id[app.id]['detection_item_count'] = {'type': 'count', 'total': app.metrics.get('detection_item_count', 0)}

    # Squash to list
    per_app_id = [{'app_id': app_id, 'metrics': v} for app_id, v in per_app_id.items()]
    # Enrich data
    for app_metrics in per_app_id:
        app_metrics['title'] = apps_map[app_metrics['app_id']].title

    result['metrics'] = per_app_id
    # result['totals'] = totals

    return ORJSONResponse(content=result, status_code=200)


async def create_detection(req: Detection):
    # if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.WORKSPACE_MANAGE):
    #     raise HTTPException(403, 'Forbidden')
    ses = ctx.current_session()

    if req.type == 'prompt':
        prompts = req.config.get('prompts')
        if prompts is None:
            raise HTTPException(422, 'config must contain "prompts" key')
        if not isinstance(prompts, dict) or 'system_prompt' not in prompts:
            raise HTTPException(422, 'config must contain "prompts" dict with at least "system_prompt" filled in')

    det: models.Detection = await db_api.create_detection({
        'owner_id': ses.user_id,
        'workspace_id': ctx.current_workspace().id,
        'type': req.type,
        'title': req.title,
        'config': req.config,
        'description': req.description,
        'meta': req.meta,
    })

    return ORJSONResponse(content=det.to_dict(), status_code=200)


async def _get_detection(detection_id: int, edit=True) -> models.Detection:
    ses = ctx.current_session()
    ws = ctx.current_workspace()
    policy_check = policies.AccessMode.ALL_CHATS_MANAGE if edit else policies.AccessMode.ALL_CHATS_VIEW
    all_chats_allowed = policies.is_allowed(ctx.current_permissions(), policy_check)
    det = await db_api.get_detection_by_id(detection_id)

    if ses.user_id != det.owner_id and not all_chats_allowed:
        raise HTTPException(403, 'Forbidden')
    if ws.id != det.workspace_id:
        raise HTTPException(404, 'Not found for id: {detection_id}')

    return det


async def get_detection(detection_id: int):
    det = await _get_detection(detection_id, edit=False)
    return ORJSONResponse(content=det.to_dict(), status_code=200)


async def update_detection(
    detection_id: int, req: DetectionUpdate,
):
    # ws = ctx.current_workspace()
    det = await _get_detection(detection_id, edit=True)

    req_update = req.model_dump(exclude_unset=True)
    if req.config:
        prompts = req.config.get('prompts')
        if prompts is None:
            raise HTTPException(422, 'config must contain "prompts" key')
        if not isinstance(prompts, dict) or 'system_prompt' not in prompts:
            raise HTTPException(422, 'config must contain "prompts" dict with at least "system_prompt" filled in')

        req_update['config'] = req.config

    det: models.Detection = await db_api.update_detection(det, req_update)

    return ORJSONResponse(content=det.to_dict(), status_code=200)


async def delete_detection(detection_id: int):
    det = await _get_detection(detection_id, edit=True)

    items, _ = await db_api.list_detection_items(detection_id=detection_id)

    item_ids = [i.id for i in items]
    files = await db_api.list_detection_files(detection_item_ids=item_ids)

    outputs = await db_api.list_detection_item_outputs(detection_item_ids=item_ids)
    output_file_ids = [o.output_file_id for o in outputs if o.output_file_id]

    for f in files:
        path = get_detection_file_path(ctx.current_org().name, ctx.current_workspace().name, f)
        await SharedConfig().file_manager.delete_file(path)

    await db_api.delete_detection_files(ids=[f.id for f in files])
    await db_api.delete_detection_files(ids=output_file_ids)
    await db_api.delete_detection_items(detection_id)
    await db_api.delete_detection_item_outputs(detection_id=detection_id)
    await db_api.delete_detection(detection_id)

    return Response(status_code=204)


async def list_detection_items(
    detection_id: int,
    ids: Optional[str] = None,
    limit: Optional[int] = 50,
    page: Optional[int] = 1,
    order: Optional[str] = 'id',
    desc: Optional[bool] = False,
):
    see_all = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    ws = ctx.current_workspace()

    if not ws:
        raise HTTPException(400, 'Workspace is not set')

    ses = ctx.current_session()
    params = dict(workspace_id=ws.id, detection_id=detection_id)
    if not see_all:
        params['owner_id'] = ses.user_id

    async with base.session_context():
        params.update({
            'ids': [int(i) for i in ids.split(',')] if ids else None,
            'limit': limit,
            'page': page,
            'order': order,
            'desc': desc,
        })

        items, files, outputs, count = await db_api.list_joined_detection_items(**params)
        # items, count = await db_api.list_detection_items(**params)
        owner_ids = [t.owner_id for t in items]
        # detection_item_ids = [t.id for t in items]
        users = await db_api.list_users(list(set(owner_ids))) if items else []
        # files = await db_api.list_detection_files(detection_item_ids=detection_item_ids) if items else []
        # outputs = await db_api.list_detection_item_outputs(detection_item_ids=detection_item_ids) if items else []

        output_file_ids = {o.output_file_id: o.id for o in outputs if o.output_file_id}
        output_files = await db_api.list_detection_files(ids=list(output_file_ids.keys())) if output_file_ids else []

    files_by_detection = collections.defaultdict(list)
    outputs_by_detection = collections.defaultdict(list)
    for f in files:
        file_dict = f.to_dict(skip_ids=True)
        file_dict['id'] = f'{file_dict["id"]}.{file_dict["name"].split(".")[-1]}'
        files_by_detection[f.detection_item_id].append(file_dict)

    output_files_by_output_id = {output_file_ids[o_file.id]: o_file for o_file in output_files}
    [setattr(o, 'output_file', output_files_by_output_id.get(o.id)) for o in outputs]

    [outputs_by_detection[o.detection_item_id].append(o.to_dict(skip_ids=True)) for o in outputs]
    user_map = {u.id: u for u in users}

    detection_dicts = [c.to_dict() for c in items]
    for detection_dict in detection_dicts:
        # detection_dict.pop('config', None)

        if detection_dict['owner_id'] in user_map:
            detection_dict['owner_login'] = user_map[detection_dict['owner_id']].login
        else:
            detection_dict['owner_login'] = None

        detection_dict['files'] = files_by_detection[detection_dict['id']]
        detection_dict['results'] = outputs_by_detection[detection_dict['id']]

    result = {'items': detection_dicts, 'count': count, 'page': page, 'limit': limit}
    return ORJSONResponse(content=result, status_code=200)


async def create_detection_item(
    detection_id: int,
    file: Optional[UploadFile] = Form(None),
    source_url: Optional[str] = Form(None),
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    input: Optional[str] = Form(None),
    config: Optional[str] = Form(None),
):
    ses = ctx.current_session()
    ws = ctx.current_workspace()

    det = await _get_detection(detection_id, edit=False)

    # create_config = det.config
    # if config:
    #     config_dict = json.loads(config)
    #     config_dict = json_utils.dict_merge(det.get_config(), config_dict)
    #     if config_dict:
    #         prompts = config_dict.get('prompts')
    #         if prompts is None:
    #             raise HTTPException(422, 'config must contain "prompts" key')
    #         if not isinstance(prompts, dict) or 'system_prompt' not in prompts:
    #             raise HTTPException(422, 'config must contain "prompts" dict with at least "system_prompt" filled in')
    #         create_config = config_dict
    #         det.config = create_config

    manager = EngineManager(ctx.current_org(), ws, app=det)

    if file:
        # Preserve file data
        file = check_file_extension(file)
        file_data = file.file.read()
        filename = os.path.basename(file.filename)
        size = file.size
    elif source_url:
        extension = await estimate_url_extension(source_url)
        basename = os.path.basename(source_url)
        base, _ = os.path.splitext(basename)
        filename = f'{base}{extension}'
        size = 0
    else:
        raise HTTPException(422, 'Either file or source_url must be set')

    detection_item: models.DetectionItem = await db_api.create_detection_item({
        'owner_id': ses.user_id,
        'workspace_id': ws.id,
        'detection_id': detection_id,
        'type': det.type,
        'title': title,
        'description': description,
        # 'config': create_config.copy(),
        'input': input,
        'status': models.STATUS_PROCESSING,
    })

    file_db = await db_api.create_detection_file({
        'owner_id': detection_item.owner_id,
        'workspace_id': ws.id,
        'detection_item_id': detection_item.id,
        'size': size,
        'name': filename,
        'status': models.STATUS_CREATED,
        'source_url': source_url,
    })
    if file:
        await SharedConfig().file_manager.save_file(
            get_detection_file_path(ctx.current_org().name, ws.name, file_db),
            file_data,
        )
    detection_out: models.DetectionItemOutput = await db_api.create_detection_item_output({
        'workspace_id': ws.id,
        'detection_id': detection_id,
        'detection_item_id': detection_item.id,
        # 'config': create_config,
        'status': models.STATUS_PROCESSING,
    })

    t1 = asyncio.create_task(manager.process_detection(detection_item, detection_out, ctx.request.value.state.access_type))
    t2 = asyncio.create_task(metric_utils.create_metric(
        ctx.current_org().id, ws.id, app_id=det.id, type_=models.METRIC_DETECTION_ITEM,
        app_type=models.APP_TYPE_DETECTION, obj_id=detection_out.id,
    ))
    await db_api.update_metrics_for_detection(det)

    detection_item_dict = detection_item.to_dict()
    detection_item_dict['results'] = [detection_out.to_dict(skip_ids=True)]

    return ORJSONResponse(content=detection_item_dict, status_code=200)


async def update_detection_item(
    detection_id: int,
    item_id: int,
    result_id: Optional[int] = Form(None),
    file: Optional[UploadFile] = None,
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    input: Optional[str] = Form(None),
    config: Optional[str] = Form(None),
    note: Optional[str] = Form(None),
    rating: Optional[int] = Form(None),
    regenerate: Optional[bool] = None,
):
    ses = ctx.current_session()
    ws = ctx.current_workspace()

    detection_item = await db_api.get_detection_item_by_id(item_id)

    all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
    if ses.user_id != detection_item.owner_id and not all_chat_allowed:
        raise HTTPException(403, 'Must be the owner to edit')

    det = await db_api.get_detection_by_id(detection_id)
    if det.workspace_id != ws.id:
        raise HTTPException(404, f'Not found for id: {detection_id}')

    update_kwargs = {}
    result_update = {}
    if title:
        update_kwargs['title'] = title
    if description:
        update_kwargs['description'] = description
    if input:
        update_kwargs['input'] = input
    if note:
        result_update['note'] = note
    if rating:
        result_update['rating'] = rating
    # if config:
    #     config_dict = json.loads(config)
    #     if config_dict:
    #         prompts = config_dict.get('prompts')
    #         if prompts is None:
    #             raise HTTPException(422, 'config must contain "prompts" key')
    #         if not isinstance(prompts, dict) or 'system_prompt' not in prompts:
    #             raise HTTPException(422, 'config must contain "prompts" dict with at least "system_prompt" filled in')
    #         update_kwargs['config'] = config

    if not update_kwargs and not result_update and file is None and not regenerate:
        raise HTTPException(400, "Provide data to update")

    manager = None
    if file is not None:
        # Replace file(s)
        files = await db_api.list_detection_files(detection_item_id=detection_item.id)
        if files:
            for f in files:
                path = get_detection_file_path(ctx.current_org().name, ws.name, f)
                await SharedConfig().file_manager.delete_file(path)
                await db_api.delete_detection_files(detection_item_id=detection_item.id, file_id=f.id)

        manager = EngineManager(ctx.current_org(), ws, app=det)
        file = check_file_extension(file)
        file_name = file.filename
        file_data = file.file.read()

        file_db = await db_api.create_detection_file({
            'owner_id': detection_item.owner_id,
            'workspace_id': ws.id,
            'detection_item_id': detection_item.id,
            'size': len(file_data),
            'name': file_name,
            'status': models.STATUS_CREATED,
        })
        await SharedConfig().file_manager.save_file(
            get_detection_file_path(ctx.current_org().name, ws.name, file_db),
            file_data,
        )

    if file is not None or input is not None or regenerate:
        # Trigger re-generate
        if manager is None:
            manager = EngineManager(ctx.current_org(), ws, app=det)

        update_kwargs['status'] = models.STATUS_PROCESSING
        # if 'config' not in update_kwargs:
            # Reset the config
            # update_kwargs['config'] = det.config
        detection_item: models.DetectionItem = await db_api.update_detection_item(detection_item, update_kwargs)
        out: models.DetectionItemOutput = await db_api.create_detection_item_output({
            'workspace_id': ws.id,
            'detection_id': detection_id,
            'detection_item_id': detection_item.id,
            # 'config': update_kwargs['config'],
            'status': models.STATUS_PROCESSING,
        })
        # Run processing
        t = asyncio.create_task(manager.process_detection(detection_item, out, ctx.request.value.state.access_type))
        t2 = asyncio.create_task(metric_utils.create_metric(
            ctx.current_org().id, ws.id, app_id=detection_id, type_=models.METRIC_DETECTION_ITEM,
            app_type=models.APP_TYPE_DETECTION, obj_id=out.id,
        ))
        detection_item_dict = detection_item.to_dict()
        detection_item_dict['results'] = [out.to_dict(skip_ids=True)]
    else:
        if update_kwargs:
            detection_item: models.DetectionItem = await db_api.update_detection_item(detection_item, update_kwargs)
        detection_item_dict = detection_item.to_dict()
        if result_update:
            if not result_id:
                raise HTTPException(400, 'Must provide result_id to update')
            result = await db_api.get_detection_item_output_by_id(result_id)
            if result.detection_id != detection_id or result.detection_item_id != item_id:
                raise HTTPException(404, f'Not found result_id = {result_id}')
            await db_api.update_detection_item_output(result, result_update)
            detection_item_dict['results'] = [result.to_dict(skip_ids=True)]

    return ORJSONResponse(content=detection_item_dict, status_code=200)


async def get_detection_item(detection_id: int, item_id: int):
    ses = ctx.current_session()
    async with base.session_context():
        detection_item = await db_api.get_detection_item_by_id(item_id)

        if detection_item.detection_id != detection_id:
            raise HTTPException(404, f'Not found for id: {item_id}')

        all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
        if ses.user_id != detection_item.owner_id and not all_allowed:
            raise HTTPException(403, 'Must be the owner to get')

        owner = await db_api.get_user_by_id(detection_item.owner_id, notfoundok=True)
        item_dict = detection_item.to_dict()
        item_dict['owner_login'] = owner.login if owner else None

        files = await db_api.list_detection_files(detection_item_id=item_id)
        outputs = await db_api.list_detection_item_outputs(detection_item_id=item_id)

        output_file_ids = {o.output_file_id: o.id for o in outputs}
        output_files = await db_api.list_detection_files(ids=list(output_file_ids.keys()))

    output_files_by_output_id = {output_file_ids[o_file.id]: o_file for o_file in output_files}
    [setattr(o, 'output_file', output_files_by_output_id.get(o.id)) for o in outputs]

    files_dicts = []
    for f in files:
        file_dict = f.to_dict()
        file_dict['id'] = f'{file_dict["id"]}.{file_dict["name"].split(".")[-1]}'
        files_dicts.append(file_dict)
    item_dict['files'] = files_dicts
    item_dict['results'] = [o.to_dict(skip_ids=True) for o in outputs]

    return ORJSONResponse(content=item_dict, status_code=200)


async def agg_detections_graph(
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    metrics: str = None,
    existing_only: bool = False,
):
    if not metrics:
        metrics = list(common_utils.get_available_metrics().keys())

    ws = ctx.current_workspace()
    filtered_metrics = {m: f for m, f in common_utils.get_available_metrics().items() if m in metrics}
    async with base.session_context():
        total_list = await db_api.get_metrics(
            [ws.id], start_date=start_date, end_date=end_date, metric_types=list(filtered_metrics.keys()),
            app_type=models.APP_TYPE_DETECTION,
            type_groups=filtered_metrics, agg_groups=['app_id']
        )
        apps, _ = await db_api.list_detections(workspace_id=ws.id)
        app_map = {a.id: a for a in apps}

    # ==== Insert missing metrics
    metrics_in_result = {t['type'] for t in total_list}
    metrics_by_app = defaultdict(lambda: metrics_in_result.copy())
    for t in total_list:
        metrics_by_app[t['app_id']].discard(t['type'])
    for app_id, missing_metrics in metrics_by_app.items():
        for m in missing_metrics:
            total_list.append({'app_id': app_id, 'type': m, 'value': 0})
    # ==== Done inserting missing metrics

    by_metric = defaultdict(list)
    for t in total_list:
        app_id = t['app_id']
        metric, value = t['type'], t['value']
        by_metric[metric].append({'app_id': app_id, 'value': value})

    result = []
    for type, metric_list in by_metric.items():
        metric_list = sorted(metric_list, key=lambda x: x['app_id'])
        item = {
            'type': type,
            'display_name': common_utils.to_display_name(type),
            'group': common_utils.metric_group(type),
            'points': [
                {
                    # 'created_at': p['updated_at'],
                    # 'timestamp': p['timestamp'],
                    'value': p['value'],
                    'object_id': p['app_id'],
                    'title': app_map.get(p['app_id']).title if p['app_id'] in app_map else None,
                    # 'object_id': p['object_id'],
                    # 'item_id': output_id_map.get(p['object_id']),
                    # 'extra': json.loads(p['extra']) if p['extra'] else None,
                }
                for p in metric_list
                if not existing_only or p['app_id'] in app_map
            ],
        }
        result.append(item)

    metric_names = common_utils.sorted_metric_names(list(metrics_in_result), common_utils.metric_views['inner'])
    return ORJSONResponse(content={'metrics': result, 'metric_names': metric_names}, status_code=200)


async def get_detection_graph(
    detection_id: int,
    metrics: str = None,
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    existing_only: bool = False,
):
    ws = ctx.current_workspace()
    async with base.session_context():
        det = await _get_detection(detection_id, edit=False)

        if metrics:
            metrics = [m.strip() for m in metrics.split(',')]
        else:
            metrics = list(common_utils.get_available_metrics().keys())

        metric_aggs = {k: v for k, v in common_utils.get_available_metrics().items() if k in metrics}
        metrics_db = await db_api.get_metrics_graph(
            workspace_id=ws.id, app_id=detection_id, type=metrics, type_groups=metric_aggs, object_base_type=int,
            start_date=start_date, end_date=end_date,
        )
        metrics_in_result = list(metrics_db.keys())
        outputs = await db_api.list_detection_item_outputs(
            workspace_id=ws.id, detection_id=detection_id, columns=['id', 'detection_item_id']
        )
    output_id_map = {o.id: o.detection_item_id for o in outputs}

    result = []
    for type, metric_list in metrics_db.items():
        item = {
            'type': type,
            'display_name': common_utils.to_display_name(type),
            'group': common_utils.metric_group(type),
            'points': [
                {
                    'created_at': p['updated_at'],
                    'timestamp': p['timestamp'],
                    'value': p['value'],
                    'object_id': p['object_id'],
                    'item_id': output_id_map.get(p['object_id']),
                    'extra': p['extra'],
                }
                for p in metric_list
            ],
        }
        result.append(item)

    metric_names = common_utils.sorted_metric_names(list(metrics_in_result), common_utils.metric_views['inner'])

    return ORJSONResponse(content={'metrics': result, 'metric_names': metric_names}, status_code=200)


async def get_detection_items_metrics(
    detection_id: int,
    # metrics: Optional[str] = None,
    limit: Optional[int] = 50,
    page: Optional[int] = 1,
    order: Optional[str] = 'id',
    desc: Optional[bool] = False,
):
    ses = ctx.current_session()
    ws = ctx.current_workspace()
    see_all = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)

    if not ws:
        raise HTTPException(400, 'Workspace is not set')

    params = dict(workspace_id=ws.id, detection_id=detection_id)
    if not see_all:
        params['owner_id'] = ses.user_id

    async with base.session_context():
        detection = await db_api.get_detection_by_id(detection_id)

        if ses.user_id != detection.owner_id and not see_all:
            raise HTTPException(403, 'Forbidden')

        skip_metrics = [
            models.METRIC_DETECTION_ITEM, models.METRIC_REQUEST, models.METRIC_EXTRACTOR_ITEM, models.METRIC_EXTRACTOR_RESULT
        ]

        params.update({
            'limit': limit,
            'page': page,
            'order': order,
            'desc': desc,
            'return_count': False,
            'return_files': False,
        })

        items, outputs = await db_api.list_joined_detection_items(**params)
        # items, _ = await db_api.list_detection_items(**params)
        # outputs = await db_api.list_detection_item_outputs(
        #     workspace_id=ws.id,
        #     detection_id=detection_id,
        #     detection_item_ids=[item.id for item in items]
        # )

        metric_dict = await common_utils.build_last_metrics(
            ws, models.APP_TYPE_DETECTION, detection_id, skip_metrics, filter_by='object_id',
            filter_values=[o.id for o in outputs],
            filter_values_addition_ids=[o.detection_item_id for o in outputs],
        )

    return ORJSONResponse(content=metric_dict, status_code=200)


async def get_detection_item_metrics(detection_id: int, item_id: int):
    ses = ctx.current_session()
    ws = ctx.current_workspace()
    detection_item = await db_api.get_detection_item_by_id(item_id)

    if detection_item.detection_id != detection_id:
        raise HTTPException(404, f'Not found for id: {item_id}')

    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    if ses.user_id != detection_item.owner_id and not all_allowed:
        raise HTTPException(403, 'Must be the owner to get')

    outputs = await db_api.list_detection_item_outputs(workspace_id=ws.id, detection_item_id=detection_item.id)
    metric_dict = await common_utils.build_last_metrics(
        ws, models.APP_TYPE_DETECTION, detection_id, filter_by='object_id',
        filter_values=[o.id for o in outputs],
        filter_values_addition_ids=[detection_item.id] * len(outputs)
    )

    return ORJSONResponse(content=metric_dict, status_code=200)


async def delete_detection_item(detection_id: int, item_id: int):
    ses = ctx.current_session()

    async with base.session_context():
        det = await db_api.get_detection_by_id(detection_id)

        detection_item = await db_api.get_detection_item_by_id(item_id)
        all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
        if ses.user_id != detection_item.owner_id and not all_allowed:
            raise HTTPException(403, 'Must be the owner to delete')
        if det.id != detection_item.detection_id:
            raise HTTPException(404, f'Not found detection item for id {item_id}')

        files = await db_api.list_detection_files(detection_item_id=item_id)

        results = await db_api.list_detection_item_outputs(detection_item_id=item_id)
        output_file_ids = [r.output_file_id for r in results if r.output_file_id]
        if output_file_ids:
            output_files = await db_api.list_detection_files(ids=output_file_ids)
            files += output_files
            await db_api.delete_detection_files(ids=output_file_ids)

        for f in files:
            path = get_detection_file_path(ctx.current_org().name, ctx.current_workspace().name, f)
            await SharedConfig().file_manager.delete_file(path)
        await db_api.delete_detection_files(item_id)
        await db_api.delete_detection_item_outputs(detection_item_id=item_id)
        await db_api.delete_detection_item(item_id)
        await db_api.update_metrics_for_detection(det)

    return Response(status_code=204)


async def delete_detection_item_result(detection_id: int, item_id: int, result_id: int):
    ses = ctx.current_session()

    async with base.session_context():
        det = await db_api.get_detection_by_id(detection_id)
        detection_item = await db_api.get_detection_item_by_id(item_id)

        all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
        if ses.user_id != detection_item.owner_id and not all_allowed:
            raise HTTPException(403, 'Must be the owner to delete')
        if det.id != detection_item.detection_id:
            raise HTTPException(404, f'Not found detection item for id {item_id}')

        files = []

        result = await db_api.get_detection_item_output_by_id(result_id)
        output_file_id = result.output_file_id
        if output_file_id:
            output_files = await db_api.list_detection_files(ids=[output_file_id])
            files += output_files
            await db_api.delete_detection_files(ids=[output_file_id])

        for f in files:
            path = get_detection_file_path(ctx.current_org().name, ctx.current_workspace().name, f)
            await SharedConfig().file_manager.delete_file(path)
        await db_api.delete_detection_item_output(result_id)
        await db_api.update_metrics_for_detection(det)

    return Response(status_code=204)


async def get_detection_file(detection_file_id: str, inline: bool = False):
    detection_file_id = detection_file_id.split('.')[0]
    file = await db_api.get_detection_file_by_id(detection_file_id)

    if file.status == models.STATUS_DELETED:
        raise HTTPException(410, 'The file was intentionally deleted after processing.')

    path = get_detection_file_path(ctx.current_org().name, ctx.current_workspace().name, file)

    media_type = mimetypes.guess_type(path)[0]
    if not media_type:
        media_type = 'application/octet-stream'
    data = await SharedConfig().file_manager.read_file(path)

    last_modified = file.created_at.strftime('%a, %d %b %Y %H:%M:%S %Z')
    expires = file.created_at + datetime.timedelta(days=30)
    expires_s = expires.strftime('%a, %d %b %Y %H:%M:%S %Z')
    disposition = 'inline' if inline else 'attachment'
    file_name = file.name.encode('latin-1', 'replace').decode('latin-1')

    return StreamingResponse(
        content=SharedConfig().file_manager.get_data_generator(data),
        media_type=media_type,
        headers={
            'content-length': str(file.size),
            'Content-Disposition': f'{disposition}; filename="{file_name}"',
            'Cache-Control': 'private,max-age=2592000',
            'Last-Modified': last_modified,
            'Expires': expires_s,
        }
    )


async def get_detection_file_meta(detection_file_id: str):
    detection_file_id = detection_file_id.split('.')[0]
    file = await db_api.get_detection_file_by_id(detection_file_id)

    return ORJSONResponse(content=file.to_dict(), status_code=200)


async def delete_detection_file(detection_file_id: str):
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
    if not all_allowed:
        raise HTTPException(403, 'Forbidden')

    detection_file_id = detection_file_id.split('.')[0]
    file = await db_api.get_detection_file_by_id(detection_file_id)

    path = get_detection_file_path(ctx.current_org().name, ctx.current_workspace().name, file)
    await SharedConfig().file_manager.delete_file(path)
    await db_api.update_detection_file(file.id, {'status': models.STATUS_DELETED})
    file.update({'status': models.STATUS_DELETED})

    return ORJSONResponse(content=file.to_dict(), status_code=200)
