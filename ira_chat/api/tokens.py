import asyncio
import datetime
import logging
from typing import Optional

from fastapi import APIRouter, HTTPException
from fastapi.responses import ORJSONResponse, Response
from pydantic import BaseModel

from ira_chat.context import context as ctx
from ira_chat.db import api as db_api, models

logger = logging.getLogger(__name__)


class Token(BaseModel):
    description: str
    expiry_time: Optional[datetime.datetime] = None


def get_router():
    router = APIRouter(prefix='/tokens')
    # router.include_router(chat_files.get_router())

    router.add_api_route("", list_tokens, methods=['GET'], name='List tokens')
    router.add_api_route("", create_token, methods=['POST'], name='Create token')
    router.add_api_route("/{token_id}", delete_token, methods=['DELETE'], name='Delete token')

    return router


async def list_tokens():
    ses = ctx.current_session()
    tokens = await db_api.list_tokens(object_type=models.TOKEN_USER, owner_id=ses.user_id)
    return ORJSONResponse(content=[c.to_dict() for c in tokens])


async def create_token(token_req: Token):
    ses = ctx.current_session()

    token_dict = token_req.dict()
    token_dict['owner_id'] = ses.user_id
    token_dict['object_type'] = models.TOKEN_USER
    token_dict['object_id'] = ses.user_id

    if not token_req.expiry_time:
        token_dict['expiry_time'] = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=365)

    token = await db_api.create_token(token_dict)

    return ORJSONResponse(content=token.to_dict())


async def delete_token(token_id: str):
    token = await db_api.get_token_by_id(token_id)

    ses = ctx.current_session()
    if ses.user_id != token.owner_id:
        raise HTTPException(403, 'Must be the owner to clear')

    await db_api.delete_token(token_id)

    return Response(status_code=204)
