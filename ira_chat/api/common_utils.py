import collections
import datetime
import logging

from fastapi import Request, HTTPException
from typing import Literal

from ira_chat.cache import global_cache
from ira_chat.db import api as db_api
from ira_chat.db import models
from ira_chat.utils import utils


logger = logging.getLogger(__name__)


display_name_map = {
    models.METRIC_COST_DOLLARS: 'Cost In Dollars',
    models.METRIC_MESSAGE_RESPONSE_TIME: 'Message Response Time',
    models.METRIC_REQUEST: 'Request Count',
}
metric_groups_order_org_view = [
    models.METRIC_GROUP_ITEMS,
    models.METRIC_GROUP_EVENTS,
    models.METRIC_GROUP_LATENCIES,
    models.METRIC_GROUP_TOKENS,
    models.METRIC_GROUP_COST,
    models.METRIC_GROUP_EVALUATION,
    models.METRIC_GROUP_LLM_EVENTS,
    models.METRIC_GROUP_FEEDBACK,
    models.METRIC_GROUP_RATING,
    models.METRIC_GROUP_ERROR,
]
metric_groups_order_inner_view = [
    models.METRIC_GROUP_EVALUATION,
    models.METRIC_GROUP_FEEDBACK,
    models.METRIC_GROUP_RATING,
    models.METRIC_GROUP_LATENCIES,
    models.METRIC_GROUP_COST,
    models.METRIC_GROUP_TOKENS,
    models.METRIC_GROUP_LLM_EVENTS,
    models.METRIC_GROUP_ERROR,
    models.METRIC_GROUP_EVENTS,
    models.METRIC_GROUP_ITEMS,
]
metric_views = {
    'inner': metric_groups_order_inner_view,
    'org': metric_groups_order_org_view,
    'ws': metric_groups_order_org_view,
}
metric_props = {
    # metric_name: [group, agg_func]
    models.METRIC_RELEVANCE: [models.METRIC_GROUP_EVALUATION, 'avg'],
    models.METRIC_CONTEXT_UTIL: [models.METRIC_GROUP_EVALUATION, 'avg'],
    # models.METRIC_CONTEXT_RELEVANCY: [models.METRIC_GROUP_EVALUATION, 'avg'],
    models.METRIC_FAITHFULNESS: [models.METRIC_GROUP_EVALUATION, 'avg'],
    models.METRIC_WEIGHTED_EVAL: [models.METRIC_GROUP_EVALUATION, 'avg'],

    models.METRIC_CORRECTNESS_GRADE: [models.METRIC_GROUP_FEEDBACK, 'avg'],
    models.METRIC_RATING_VALUE: [models.METRIC_GROUP_RATING, 'avg'],

    models.METRIC_RESPONSE_TIME: [models.METRIC_GROUP_LATENCIES, 'avg'],
    models.METRIC_MESSAGE_RESPONSE_TIME: [models.METRIC_GROUP_LATENCIES, 'avg'],
    models.METRIC_COST_DOLLARS: [models.METRIC_GROUP_COST, 'sum'],

    models.METRIC_INPUT_TOKENS: [models.METRIC_GROUP_TOKENS, 'sum'],
    models.METRIC_OUTPUT_TOKENS: [models.METRIC_GROUP_TOKENS, 'sum'],
    models.METRIC_TOKENS_SECOND: [models.METRIC_GROUP_TOKENS, 'avg'],

    models.METRIC_REQUEST: [models.METRIC_GROUP_LLM_EVENTS, 'sum'],
    models.METRIC_ERROR: [models.METRIC_GROUP_ERROR, 'count'],

    models.METRIC_CORRECTED_ANSWER: [models.METRIC_GROUP_EVENTS, 'sum'],
    models.METRIC_RATING_EVENT: [models.METRIC_GROUP_EVENTS, 'sum'],
    models.METRIC_GRADE_EVENT: [models.METRIC_GROUP_EVENTS, 'sum'],
    models.METRIC_CORRECT_ANSWER: [models.METRIC_GROUP_EVENTS, 'sum'],
    models.METRIC_PARTIAL_ANSWER: [models.METRIC_GROUP_EVENTS, 'sum'],
    models.METRIC_INCORRECT_ANSWER: [models.METRIC_GROUP_EVENTS, 'sum'],
    models.METRIC_NO_ANSWER_EVENT: [models.METRIC_GROUP_EVENTS, 'sum'],
    models.METRIC_EXCLUDED_ANSWER_EVENT: [models.METRIC_GROUP_EVENTS, 'sum'],
    models.METRIC_OFF_TOPIC_EVENT: [models.METRIC_GROUP_EVENTS, 'sum'],
    models.METRIC_CACHE_HIT: [models.METRIC_GROUP_EVENTS, 'sum'],
    models.METRIC_CHAT_CREATED: [models.METRIC_GROUP_EVENTS, 'count'],
    models.METRIC_CHAT_DELETED: [models.METRIC_GROUP_EVENTS, 'count'],
    models.METRIC_MESSAGE_DELETED: [models.METRIC_GROUP_EVENTS, 'sum'],

    models.METRIC_MESSAGE: [models.METRIC_GROUP_ITEMS, 'count'],
    models.METRIC_DETECTION_ITEM: [models.METRIC_GROUP_ITEMS, 'count'],
    models.METRIC_EXTRACTOR_ITEM: [models.METRIC_GROUP_ITEMS, 'count'],
    models.METRIC_EXTRACTOR_RESULT: [models.METRIC_GROUP_ITEMS, 'count'],
}
group_ranges = {
    models.METRIC_GROUP_EVALUATION: [0, 1],
}


def sorted_metric_names(metric_names: list[dict | str], group_order: list[str]) -> list[dict]:
    if not metric_names:
        return []
    if isinstance(metric_names[0], str):
        metric_names = [
            {
                'name': m,
                'display_name': to_display_name(m),
                'group': metric_group(m),
                'range': group_ranges.get(metric_group(m), None),
            } for m in metric_names
        ]

    order = {k: i for i, k in enumerate(group_order)}
    metric_names = sorted(metric_names, key=lambda x: order.get(x['group'], 99))
    return metric_names


async def ensure_user_in_org(org_id, user_login, user_id):
    org_user = await db_api.get_org_user(org_id=org_id, user_login=user_login)
    if not org_user:
        org_user = await db_api.get_org_user(org_id=org_id, user_id=user_id)

    if not org_user:
        await db_api.create_org_user({
            'org_id': org_id,
            'user_id': user_id,
            'role_id': db_api.get_role_by_name('user')['id'],
            'confirmed': True,
        })
    else:
        # Update org user to confirm
        await db_api.update_org_user(org_user, {
            'confirmed': True,
            'user_id': user_id,
            'user_login': None,
        })


async def auto_add_in_org(org: models.Organization, user: models.User):
    # config = org.get_config()
    # auto_add = config.get('auto_add_users', False)
    # if not auto_add:
    #     return
    if not user:
        return

    await ensure_user_in_org(org.id, user.login, user.id)


async def get_current_role(org_user: models.OrganizationUser | None, sess: models.Session):
    if sess.admin:
        return db_api.get_role_by_name('owner')
    current_role = db_api.get_role_by_id(org_user.role_id)
    return current_role


def get_org_host(req: Request):
    org_host = req.url.hostname
    return org_host


async def resolve_org_domain(org_id, domain=None):
    if not domain:
        domains = await db_api.list_org_domains(org_id)
        return domains[0].domain
    else:
        org = await db_api.get_org_by_domain(domain)
        if org.id != org_id:
            raise HTTPException(403, 'Requested domain is not the organization\'s domain')
        return domain


async def resolve_org_base_url(org_id, domain=None, req_port=None):
    base_url = f'{utils.get_global_schema()}://{await resolve_org_domain(org_id)}'
    if req_port:
        base_url += ':' + str(req_port)
    return base_url


def get_ws_cache_key(ses: models.Session, ws_id):
    return f'session/{ses.id}/ws/{ws_id}'


def get_cache_key(sess_id, request: Request, domain=None):
    domain = domain or get_org_host(request)
    cache_key = f'session/{sess_id}/domain/{domain}'
    return cache_key


async def clear_cache_for_session(sess_id):
    await global_cache.get_cache().adelete_prefix(f'session/{sess_id}')


async def clear_cache_for_user(user_id):
    sessions = await db_api.get_sessions(user_id=user_id)
    for session in sessions:
        await clear_cache_for_session(session.id)


def get_available_metrics():
    return {k: v[1] for k, v in metric_props.items()}


def get_metric_groups():
    return {k: v[0] for k, v in metric_props.items()}


async def build_metrics_items_graph(
    ws: models.Workspace, app_type: str, app_id: int | None = None,
    metrics: list = None, object_ids: list = None,
    object_values_addition_ids: list = None,
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    metric_view: Literal['inner', 'org'] = 'org',
):
    if metrics:
        metric_aggs = {k: v for k, v in get_available_metrics().items() if k in metrics}
    else:
        metric_aggs = {k: v for k, v in get_available_metrics().items()}

    if object_ids is not None:
        object_ids = [str(v) for v in object_ids]
    graph = await db_api.get_metrics_graph(
        workspace_id=ws.id, app_type=app_type, app_id=app_id,
        # type=metrics,
        type_groups=metric_aggs, object_ids=object_ids,
        start_date=start_date, end_date=end_date,
    )
    # metrics is a dict [type -> metric_list]
    ids_map = None
    if object_values_addition_ids is not None:
        ids_map = {o: item_id for o, item_id in zip(object_ids, object_values_addition_ids)}

    metric_map = collections.defaultdict(lambda: {'metrics': []})
    metric_names = set()
    for type, metric_list in graph.items():
        metric_names.add(type)
        for m in metric_list:
            obj_id = m['object_id']
            metric_map[obj_id]['id'] = obj_id
            metric_map[obj_id]['item_id'] = ids_map.get(obj_id) if ids_map else None
            metric_map[obj_id]['metrics'].append({
                'type': type,
                'value': m['value'],
                'updated_at': m['updated_at'],
            })
            # m['display_name'] = to_display_name(m['type'])
            # metric_map[m['object_id']].append(m)

    names = [{'name': m, 'display_name': to_display_name(m), 'group': metric_group(m)} for m in metric_names]
    names = sorted_metric_names(names, metric_views[metric_view])

    # metric_arr = []
    # for key, val in metric_map.items():
    #     m = {'id': key}
    #     m['metrics'] = val
    #     metric_arr.append(m)

    return {
        'metric_names': names,
        'data': list(metric_map.values()),
    }


async def build_last_metrics(
    ws: models.Workspace, app_type: str, app_id: int,
    skip_metrics: list = None, filter_by: str = None, filter_values: list = None,
    filter_values_addition_ids: list = None,
    metric_view: Literal['inner', 'org'] = 'inner',
):
    if filter_by == 'object_id':
        filter_values = [str(v) for v in filter_values]
    metrics = await db_api.get_last_metrics(
        workspace_id=ws.id, app_type=app_type, app_id=app_id, split_by=('type', 'object_id'),
        filter_by=filter_by, filter_values=filter_values,
    )
    ids_map = None
    if filter_values_addition_ids:
        ids_map = {o: item_id for o, item_id in zip(filter_values, filter_values_addition_ids)}

    metric_map = collections.defaultdict(list)
    metric_names = set()
    for m in metrics:
        if skip_metrics and m['type'] in skip_metrics:
            continue
        # m['display_name'] = to_display_name(m['type'])
        metric_map[m['object_id']].append(m)
        metric_names.add(m['type'])

        m.pop('object_id', None)
        m.pop('id', None)
        m.pop('app_id', None)

    metric_arr = []
    for key, val in metric_map.items():
        m = {'id': key}
        if ids_map:
            m['item_id'] = ids_map[key]
        m['metrics'] = val
        metric_arr.append(m)
    names = [{'name': m, 'display_name': to_display_name(m), 'group': metric_group(m)} for m in metric_names]
    names = sorted_metric_names(names, metric_views[metric_view])
    return {
        'metric_names': names,
        'data': metric_arr,
    }


def to_display_name(name: str):
    if name in display_name_map:
        return display_name_map[name]
    return ' '.join([s.capitalize() for s in name.split('_')])


def metric_group(name: str):
    return metric_props.get(name, ['Default'])[0]


def insert_missing_values_val(insert_missing_values: str):
    try:
        return int(insert_missing_values)
    except ValueError:
        if insert_missing_values == 'null':
            return None
        return None


available_metrics = get_available_metrics()
