import datetime
import logging
import os
from typing import Optional

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse, Response
from pydantic import BaseModel

from ira_chat.context import context as ctx
from ira_chat.db import api as db_api, models
from ira_chat.policies import policies

logger = logging.getLogger(__name__)


class Token(BaseModel):
    description: str
    expiry_time: Optional[datetime.datetime] = None
    permissions: Optional[list[str]] = None


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'tokens'))

    router.add_api_route("", list_tokens, methods=['GET'], name='List tokens')
    router.add_api_route("", create_token, methods=['POST'], name='Create token')
    router.add_api_route("/{token_id}", delete_token, methods=['DELETE'], name='Delete token')

    return router


async def list_tokens():
    if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.WORKSPACE_MANAGE):
        raise HTTPException(403, 'Forbidden')
    ws = ctx.current_workspace()
    tokens = await db_api.list_tokens(object_type=models.TOKEN_WORKSPACE, object_id=ws.id)
    owner_ids = [t.owner_id for t in tokens]
    users = await db_api.list_users(owner_ids)
    user_map = {u.id: u for u in users}

    token_dicts = [c.to_dict() for c in tokens]
    for token_dict in token_dicts:
        if token_dict['owner_id'] in user_map:
            token_dict['owner_login'] = user_map[token_dict['owner_id']].login
        else:
            token_dict['owner_login'] = 'unknown'
        token_dict['permissions'] = policies.decode_permissions(token_dict['permissions'])

    return JSONResponse(content=token_dicts, status_code=200)


async def create_token(token_req: Token):
    if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.WORKSPACE_MANAGE):
        raise HTTPException(403, 'Forbidden')
    ses = ctx.current_session()

    token_dict = token_req.dict()
    token_dict['owner_id'] = ses.user_id
    token_dict['object_type'] = models.TOKEN_WORKSPACE
    token_dict['object_id'] = ctx.current_workspace().id
    if token_req.permissions:
        token_dict['permissions'] = policies.encode_permissions(token_req.permissions)
    else:
        # TODO Fix ??
        token_dict['permissions'] = policies.encode_permissions(
            [policies.AccessName.ALL_CHATS_VIEW, policies.AccessName.ALL_CHATS_MANAGE]
        )
    token_dict['org_permissions'] = 1

    if not token_req.expiry_time:
        token_dict['expiry_time'] = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=365)

    token = await db_api.create_token(token_dict)
    result_dict = token.to_dict()
    result_dict['permissions'] = policies.decode_permissions(result_dict['permissions'])

    return JSONResponse(content=result_dict, status_code=200)


async def delete_token(token_id: str):
    if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.WORKSPACE_MANAGE):
        raise HTTPException(403, 'Forbidden')
    token = await db_api.get_token_by_id(token_id)

    if not token:
        raise HTTPException(404, 'Token not found')

    # ses = ctx.current_session()
    # if ses.user_id != token.owner_id:
    #     raise HTTPException(403, 'Must be the owner to clear')

    await db_api.delete_token(token_id)

    return Response(status_code=204)
