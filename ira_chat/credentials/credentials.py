import os
import json


CREDENTIAL_VAR = 'CREDENTIALS'


def read_credentials(name: str) -> [dict, list]:
    cred_path = _credential_path(name)
    with open(cred_path, 'r') as f:
        data = f.read()
    return json.loads(data)


def credentials_dir() -> str:
    home = os.path.expandvars('$HOME')
    return os.getenv(CREDENTIAL_VAR, os.path.join(home, ".ira_chat"))


def _credential_path(name: str) -> str:
    return os.path.join(credentials_dir(), f'{name}.json')


try:
    ADMIN_IDS = set(read_credentials("admins"))
except Exception as e:
    ADMIN_IDS = set()
