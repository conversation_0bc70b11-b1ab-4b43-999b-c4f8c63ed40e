import logging
import os

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from starlette.exceptions import HTTPException as StarletteHTTPException
from starlette.middleware.base import BaseHTTPMiddleware

from ira_chat.api import api, auth_utils
from ira_chat.api import handle_error

logger = logging.getLogger(__name__)


def get_app() -> FastAPI:
    """
    Retrieves a FastAPI application instance.

    Returns
    ----------
        FastAPI
            FastAPI application instance.

    """

    fast_app = FastAPI(lifespan=api.lifespan)
    fast_app.include_router(api.get_router())

    init_openapi_security_route(fast_app)

    fast_app.add_exception_handler(StarletteHTTPException, handle_error.handle_exception)
    fast_app.add_exception_handler(HTTPException, handle_error.handle_exception)
    fast_app.add_exception_handler(Exception, handle_error.handle_exception)
    fast_app.add_middleware(BaseHTTPMiddleware, dispatch=api.req_middleware)
    fast_app.add_middleware(
        CORSMiddleware,
        allow_origins=['*'],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    fast_app.add_middleware(BaseHTTPMiddleware, dispatch=api.time_middleware)
    # fast_app.add_event_handler('startup', api.startup)

    return fast_app


def init_openapi_security_route(app: FastAPI):
    routes = [r for r in app.routes if r.path == '/openapi.json']
    if not routes:
        return
    route = routes[0]
    only_admin = BaseHTTPMiddleware(route.app, auth_utils.only_admin_or_org_owner)
    route.app = only_admin


app = get_app()
if __name__ == "__main__":
    port = os.environ.get("PORT", "8083")
    workers = os.environ.get("WORKERS", "1")
    asyncio_loop = os.environ.get("ASYNCIO_LOOP", "uvloop")
    uvicorn.run(
        'ira_chat.main:app',
        host="0.0.0.0",
        port=int(port),
        access_log=False,
        workers=int(workers),
        # loop='asyncio',
        loop=asyncio_loop,
    )
