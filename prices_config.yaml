prices:
    # model-name: [input-tokens-price, output-tokens-price]
    # price format: x.yy for 1M tokens

    # openai models
    gpt-3.5-turbo: [0.5, 1.5]
    gpt-3.5-turbo-1106: [1, 2]
    gpt-3.5-turbo-16k: [0.5, 1.5]
    gpt-3.5-turbo-0613: [1.5, 2]
    gpt-3.5-turbo-16k-0613: [3, 4]
    gpt-4: [30, 60]
    gpt-4-0613: [30, 60]
    gpt-4-32k: [60, 120]
    gpt-4-32k-0613: [60, 120]
    gpt-4-turbo-preview: [10, 30]
    gpt-4-1106-preview: [10, 30]
    gpt-4-0125-preview: [10, 30]
    gpt-4-vision-preview: [10, 30]
    gpt-4-1106-vision-preview: [10, 30]

    # anthropic models
    claude-3-opus-20240229: [15, 75]
    claude-3-sonnet-20240229: [3, 15]
    claude-3-haiku-20240229: [0.25, 1.25]

    # mistralai models
    open-mistral-7b: [0.25, 0.25]
    open-mixtral-8x7b: [0.7, 0.7]
    mistral-small-latest: [2, 6]
    mistral-medium-latest: [2.7, 8.1]
    mistral-large-latest: [8, 24]

    # gemini models
    gemini-pro: [0.125, 0.375]