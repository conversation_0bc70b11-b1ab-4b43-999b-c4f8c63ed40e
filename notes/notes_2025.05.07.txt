Versions 1.3.100 - 1.3.135

## 1. Retrieval-Augmented Generation (RAG)

* **Subquery-based RAG**

  * Major rewrite: introduced “subquery RAG” which decomposes the question on subqueries and then combines the results back.

* **Topic Router**

  * Implemented a topic-router agent, made it optional/configurable
  * It shows significantly bettter routing between WAP / Not WAP / clarify / no_answer

* Implemented reranking (by embedding distance and LLM grading using RRF)

## 2. Chat Features & Filters

* **Chat filtering & history export**

  * Added multiple chat filtering features (by tags, grade selector, message content).
  * Enabled “Download history with full context,” optional inclusion of chat URL & context.
  * Added JSON/TXT attachments to chat messages
  * Grade-selector dropdown for XLSX history import/export, plus auto-rating safeguards.

## 3. Tagging, Metadata and Metrics

* **Tag support across the board**

  * Unified “set tag,” “filter by tags,” and API changes to expose tags in history and metrics.
  * Added tag to metrics and extended filters in metrics views.

* **Enhanced grading tools**
  * Rebuilt grading prompts, added “better relevance grader,” and tuned thresholds.

* **Metrics pipeline**
  * Squash-metrics scripts, improved logging, and “optimize chat metrics” by deduplicating object IDs.

## 4. Models & LLMs

* **New model defaults & choices**

  * Added `gpt-4.1`, `gpt-4.1-mini`, `o4-mini`, `gpt-4.1-nano`, and `gpt-4.5`.
  * Switched default tools to use `gpt-4.1-mini`.
  * Upgraded Google generative-AI integrations (Gemini 2.5 Pro, Google Tool model).

## 5. Data-Ingestion & File Processing

* **File processor overhaul**

  * Introduced a dedicated “file processor” module, with parallel section extraction and format detection (PDF, JPG, XLSX, JSON, TXT, MD).
  * Improved support for password-protected PDFs, better Excel/XLSX loading (via unstructured), and handling of large docs (new Markdown splitter).

## 6. Timeline & Extractors

* **Timeline API stabilization**

  * Correct create/delete/update of timeline points and extractors; fixed ID and search-limit edge cases.
  * Auto-delete datasets for extractors; “keep only last N results” option.

## 7. Core Engine & Concurrency

  * Added support for choosing between native asyncio and uvloop dynamically.
  * Cleaned up duplicate “Fix asyncio loop” entries.
  * Better memory management
  * Tweaked vectorstore initialization to avoid extra collections.
* **Index status handling**

  * More correct “set status” on index creation/rebuild.
  * Auto-create index for new timeline datasets.

## 8. DevOps, CI & Docs

* **Migrations & dependencies**

  * Updated PostgreSQL → 17.4.
  * Upgraded Qdrant → 1.13.2.
  * Upgraded major deps: `ragas` 0.2.15, `unstructured`, Qdrant, Dockerfiles, and fixed various package imports (e.g. `zbar`, `openpyxl`).
* **Scripts & tooling**

  * Router/test scripts polished, history-import validations, toolkit upgrades (API client sync), and README + doc fixes.


