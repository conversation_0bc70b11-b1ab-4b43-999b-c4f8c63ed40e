* Added new models:
  - OpenAI gpt-4o
  - Google Gemini 1.5-pro and 1.5-flash
* Added Groq API models:
  - llama3, mixtral, gemma
* Updated langchain and openai libraries to the latest versions
* Migrate to a new output model:
  - Now every output regenerate as saved as a separate result, preserving history
* Regenerate message in chat with streaming support
* More clear work with metrics in chats/detections

