* New dataset / files / indexes feature:
New dataset, files and indexes management section: it can be used to control over the data and various indexes of different providers and use datasets to provide and share data/indexes in workspaces.
  - Datasets exist independently of workspaces
  - Datasets can control file sets and indexes
  - Dataset indexes can include different embedding providers and models
  - Indexes can be locked (prevent changing), unlocked and then again synchronized
  - Dataset and indexes provide simple statistics about occupied space and file count
  - Dataset, files and indexes provide their status during changing and processing
  - Workspaces can connect dataset-index to work with as default
  - Apps (chats) can override dataset-index if it is needed

* Various Posology HTML formatiing improvements:
  - Fix images in tables
  - Fix wrong HTML encoding
  - Fix for bold with spaces

* Misc:
  - Improved document grade task in agentic RAG
  - Fix accuracy metric evaluations
