Add timeline schemas API
Upgrade ragas
Full fix for chat history in legacy chain
Just in case optimize history context before sending to LLM
Fix loop in the agent graph; todo: rewrite question
Fix context error; reserve some tokens for output
Fix some formatting
Try fix streaming
Fix agent context and retrieval
Take history into account
Fix delete message; try to work with history; optimize list messages API
Fix error in case of empty context
Optional agentic RAG done
WIP Agentic RAG
Fix merge json
Truncate error
Add langgraph dep
Fix initializing google
Async detection; upgrade qdrant; patch langchain_qdrant
Upgrade langchain qdrant; add more logging to file loader
Improve file re-processing
Use resource requests
Decrease workers
Barcode debug
Fix bug with PREPROCESSING stuck
Add support for new models in Groq
Refactor engine manager for chats
Update langchain dependencies and alembic
Improve example and correctly mark result source
Fix delete timeline; add api timeline example
Add gpt-4o-mini model
Put N/A by default; lift auto process flag; refactor extractor api
Don't include extractor in point list
Try to look stale files in all points backwards
Remove debug fail
Improve error message
Improve error handling and processing
Fix upload file session issue
More exceptions during merge
Optimize db engine a bit
Set 3 workers
Improvements: replace file api and check; list files sorting
Check hash API
Improve replace list logic: append/replace
Improve replace list logic
Improve replace list logic
Fix migration
Compute extractor file hash
Fix change status during file upload
Switch point to UPDATED only if auto-process is disabled
Fix historical result when extractor result is not ready yet
Add auto-processing timeline point after file processing
Always compute diff for a change
Correct empty schema result; fix cache invalidate during ws/org user changes
Hide extractors from timeline; create points with success state
Allow use llm during list merging
Get all available files for point
Fix schema validation
Improve schema change, updates and changes diff
Fix timeline schema update -> all points change status to UPDATED
Include history result into point result API
Add experimental history on request
Update next points when a new change to point added
Fix generate empty schema
Fix diff computing; diff on the fly during processing
Improve create/delete point and add changes
Fix csv/xlsx get
Recompute point result during adding changes if timeline is up-to-date
Encapsulate timeline logic into engine manager
Fix chat/detection metrics
fix syntax
Correctly process timeline
Delete timeline
Improve point to point processing and result saving
Fix delete point change
Delete change
Add point changes API
Add jsondiff
Optimize chat metrics
Optimize detection list metrics a bit
Keep processing status for timeline if any point is still processing
Fix extractor result for point processing
Improve point result
Only single point result
New migration for timeline points
Add point status, timeline status, has_updates change
Add point status; add files/extractor to point list
Add new timeline/point API to get last results
Fix timeline points date
Fix timeline point by date
test api: optionally parse JSON
Align base name for detection files
Update to success
Initial timeline points processing + json merging
