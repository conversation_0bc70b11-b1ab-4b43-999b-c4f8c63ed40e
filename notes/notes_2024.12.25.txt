Index provided answers separately; add script for their migration
Add search in corrected answers vectorstore
Fix metrics graph json extra output
Fix detection graph
Add symmetrical metrics graph for detections
Update model prices; add o1
Add source_type to dataset file; fix create file; add list file filter for source_type
Exclude metrics for org/workspace for appropriate app_types
Fix default group sorting
Add metric names to messages metrics
Re-order metrics in org and app views
Reformat
Filter existing only chats/messages in graph metrics
Add metric names to graph response; re-sort metrics
Optimize chats graph
Use better encoding for splitter
Add start/end date filters to message metrics api
Delete context relevancy metric
Add date filter to messages_metrics (not implemented yet)
Add metric group to graph
Better separation on metric groups
Add chats graph with aggregation
Support start/end date for chat graph
Chat graph object id sorting fix
Chat graph cast object id to int
Translate dataset/index status to app status
Fix set status sync for workspace/chats
Fix get dataset index
Add ws/chat status and perform status checks for indexes
Extractor: fix dataset/index watch; fix processing
Fix base file processing with extractors
Add script for migrating extractors
Fix extractor file status
Add 0 to metrics for no-answer and error metric
Add metric message deleted by default
Agg metrics sorting: support date range
Implement chats agg metric sort (special case)
Add metric groups; fix metric graph - aggregate metrics by object_id
Fix metric names
Add braintrust libs and integration
Implement message_deleted metric
Posology: fix table headers and webdings font translation
Posology: fix incorrect link formatting
Allow request new metric message_deleted
Add search in index
Fix chat config json create
Add chat_created metric in totals for chats
Fix chat graph
Upgrade langchain etc.
Fix imports
Add langfuse
Enable tracing with langchain and langfuse for chat (assistant) app
Get total agg chat metrics along with per chats metrics
Fix chat metrics sorting
Implement chat metrics inside chat data; todo: message metric over time
Fix sort with multiple dots
Enable sorting by config props
Slightly improve qa script
Not crash for wrong order by for chats
Add script for ragas evaluate; improve api Q/A, improve logging for filter routing
Posology: more accuracy with exceptions
Improve doc search in chat API; improve no answer template prompt
Fix chat count query
Add pagination and search to detections, extractors and timelines
Optional chat pagination
Allow deleting each message
Fixes: better prompt, non-answer prompt, added doc_search debug feature
Add workspace usage in datasetsg
Store workspace config as json
Fix migrate to dataset script
Fix migrate script and chart values
Disable draw graph
Implement dataset migration script
Add pagination to chats; more logging for output check score
Fix typo in email template
Fix posology pattern sub
Posology: skip download image if unavailable
Fix posology pattern sub
Fix posology pattern sub
Add vector count to index data
Upgrade file upload script
Token org permissions fix
Split pdf script
Posology: fix missing images and wrong rubriques links
Add groq llama3.2 90b vision model
Add claude 3.5 haiku model
Fix provided answer index status
Extractors, timelines: use json for status and config; add hash to ds files
