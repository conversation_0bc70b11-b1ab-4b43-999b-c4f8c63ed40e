Improve default prompt
Improvements:
* Suggestions fixed for Anthropic
* Fixed docstore retrieval order
* Fixed introduction words like 'Based on the context' bla bla
* Improved ragas metrics
Try fixing provided answers file
Add last_seen_at, last_login_at, last_activity_at times
Fix provided answers
Add provided answers table
Fix provided answers
Fix provided answers
Add provided answer mechanic with indexing
Rating message from 0 to 10
Auto add users
Process workspace access type for public types
Fix current role
Add auto add users in org
Change ws type to access_type
(tag: 1.3.5) Improve cache works
Fix cache issues during org/ws edit
Limit suggestions
Try fix APIConnectionError
Add chat graph
Improve helm deploy values
(tag: 1.3.4) Work with cache
(tag: 1.3.3) Increase file status len
Fix error in streaming chat
Implement paginations for items last metrics
Increase cache ttl for login
Process invite for google login
Add processing message note
Add message_count and detection_item_count output for detection/chat metrics
Implement pagination for detection items
Implement per-app metrics
Register user properly for google login
Don't load detection config in list
Fix google auth for global base url
Add and process Google auth
(tag: 1.3.1) Fix display user/member list
Fix default role id in org invite
Fix invite in organization
Fix redis port
Include redis cache
Fix cache issues
Update gemini models
Update openai models pricing
Optimize call workspace list with permissions
Add org and ws cache (local/redis) to decrease DB load
