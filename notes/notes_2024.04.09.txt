* Add new index type for better retriever quality: parent-child
* Organization config is encrypted now
* Support Anthropic and MistralAI LLMs
* Feature: Workspace clone
* LLMs can be disabled using org config now
* Metrics: new metric "tokens per second", "cost dollars", new metrics graphs and outputs
* Display detection and detection items metrics
* Chat message streaming (better response speed)
* Browse cache utilization during detection images loading
* Use correct detection config during "regenerate" action
* Other various bug fixes
