# Unified Collection per Workspace Implementation Plan

## Overview

This document outlines the implementation plan for migrating from individual extractor collections to a unified collection per workspace architecture in the IRA Chat system. The new approach will use metadata filtering to isolate documents by extractor/chat while maintaining a single Qdrant collection per workspace.

## Current State Analysis

### Current Architecture
- Each extractor creates its own collection in Qdrant using the pattern: `{org.name}-{dataset.name}-{index.name}`
- Collections are created via `auto_create_dataset_index()` which creates dataset/index pairs
- Chats use a similar pattern with their own collections
- Documents are stored with metadata including `dataset_file_id`, `index_id`, etc.
- No metadata filtering is currently implemented in searches

### Key Files Involved
- `ira_chat/services/engine_manager.py` - Core engine management and collection creation
- `ira_chat/api/extractor.py` - Extractor creation and auto-dataset creation
- `ira_chat/services/vectorstore.py` - Vectorstore initialization and collection naming
- `ira_chat/services/qdrant_vs.py` - Custom Qdrant implementation with metadata embedding
- `ira_chat/services/retrievers.py` - Document retrieval logic
- `ira_chat/services/extraction.py` - Extraction processing

## Implementation Plan

### Phase 1: Introduce Unified Collection Structure

#### 1.1 Update Collection Naming Strategy
**File:** `ira_chat/services/engine_manager.py`

**Changes:**
- Modify `index_namespace_suffix()` to create workspace-level collections: `{org.name}-{workspace.name}`
- Add new function `get_workspace_collection_name(org, workspace)` 
- Update `index_suffix` property to use workspace-level naming

#### 1.2 Add Metadata Fields for Filtering
**File:** `ira_chat/services/engine_manager.py`

**Changes:**
- Update `index_file_for_extraction()` to add `extractor_id` and `subcollection_id` to document metadata
- For extractors: `subcollection_id = f"extractor-{extractor.id}"`
- For chats: `subcollection_id = f"chat-{chat.id}"` (future expansion)
- Add `workspace_id` to metadata for additional filtering capability

#### 1.3 Create Metadata Filter Utilities
**File:** `ira_chat/services/vectorstore.py`

**New Functions:**
- `create_metadata_filter(extractor_id=None, chat_id=None, subcollection_id=None)`
- `apply_metadata_filter_to_search_kwargs(search_kwargs, metadata_filter)`

### Phase 2: Update Vectorstore Operations

#### 2.1 Enhance QdrantVectorStore with Filtering
**File:** `ira_chat/services/qdrant_vs.py`

**Changes:**
- Extend `QdrantVectorStoreEmbedMetadata` to support metadata filtering in searches
- Add methods for filtered similarity search operations
- Ensure metadata filters are properly applied to Qdrant queries

#### 2.2 Update Retriever Classes
**File:** `ira_chat/services/retrievers.py`

**Changes:**
- Modify `VectorStoreRetrieverWithScore` to accept and use metadata filters
- Update `_get_relevant_documents()` and `_aget_relevant_documents()` to apply filters
- Add `metadata_filter` parameter to retriever initialization

### Phase 3: Update Engine Manager Integration

#### 3.1 Modify EngineManager Initialization
**File:** `ira_chat/services/engine_manager.py`

**Changes:**
- Update `_get_named_vectorstore()` to use workspace-level collection names
- Modify `_get_retriever()` to automatically apply appropriate metadata filters based on app type
- For extractors: automatically filter by `extractor_id`
- For chats: automatically filter by `chat_id` (when implemented)

#### 3.2 Update Document Indexing
**File:** `ira_chat/services/engine_manager.py`

**Changes:**
- Modify `index_file_for_extraction()` to add required metadata fields
- Update `add_documents()` calls to include `extractor_id`, `subcollection_id`, `workspace_id`
- Ensure backward compatibility with existing metadata structure

### Phase 4: Update Extractor Creation Flow

#### 4.1 Modify Auto-Dataset Creation
**File:** `ira_chat/api/extractor.py`

**Changes:**
- Update `auto_create_dataset_index()` to use workspace-level collections
- Modify index configuration to include metadata filtering setup
- Ensure extractors point to the unified workspace collection

#### 4.2 Update Extraction Processing
**File:** `ira_chat/services/extraction.py`

**Changes:**
- Modify `_process_extractor()` to use filtered retrievers
- Ensure extraction only searches within the specific extractor's documents
- Update search parameters to include metadata filtering

### Phase 5: Database Schema Updates

#### 5.1 Update Models and Metadata Structure
**File:** `ira_chat/db/models.py`

**Changes:**
- Update `DatasetFileDocument` model to include `subcollection_id` field
- Add migration script to populate existing records with appropriate `subcollection_id` values
- Update `get_doc_metadata()` methods to include new fields

#### 5.2 Database Migration Strategy
**New File:** `scripts/migrate_to_unified_collections.py`

**Purpose:**
- Migrate existing separate collections to unified workspace collections
- Add appropriate metadata filters to existing documents
- Handle data migration without service interruption

### Phase 6: Chat Integration (Future Expansion)

#### 6.1 Extend to Chat Collections
**File:** `ira_chat/services/engine_manager.py`

**Changes:**
- Add chat-specific metadata filtering: `subcollection_id = f"chat-{chat.id}"`
- Update chat document indexing to use workspace collections
- Implement chat-specific retrieval with metadata filtering

### Phase 7: Configuration and Backward Compatibility

#### 7.1 Add Configuration Options
**File:** `ira_chat/services/engine_manager.py`

**Changes:**
- Add configuration flag `use_unified_collections` (default: True for new workspaces)
- Implement fallback logic for existing separate collections
- Add migration detection and automatic upgrade path

#### 7.2 Update Collection Management
**File:** `ira_chat/services/langchain_svc.py`

**Changes:**
- Update `prepare_vectorstore()` to handle workspace-level collections
- Add collection cleanup utilities for old separate collections
- Implement collection merging functionality

## Implementation Details

### Metadata Structure
```python
# New document metadata structure
document_metadata = {
    'workspace_id': workspace.id,
    'subcollection_id': f'extractor-{extractor.id}',  # or f'chat-{chat.id}'
    'extractor_id': extractor.id,  # for extractors
    'chat_id': chat.id,  # for chats (future)
    'dataset_file_id': file.id,
    'index_id': index.id,
    # ... existing metadata
}
```

### Filter Creation
```python
# Example filter usage
def create_extractor_filter(extractor_id: int):
    return {
        'must': [
            {'key': 'subcollection_id', 'match': {'value': f'extractor-{extractor_id}'}}
        ]
    }
```

### Collection Naming
```python
# New collection naming pattern
def get_workspace_collection_name(org: models.Organization, workspace: models.Workspace):
    return f'{org.name}-{workspace.name}'
```

## Migration Strategy

1. **Phase 1-2**: Implement new infrastructure alongside existing system
2. **Phase 3**: Add configuration flag to enable unified collections for new extractors
3. **Phase 4**: Create migration script to move existing data
4. **Phase 5**: Gradually migrate existing extractors to unified collections
5. **Phase 6**: Remove old collection creation logic after full migration

## Benefits

1. **Resource Efficiency**: Fewer collections per workspace reduces Qdrant overhead
2. **Simplified Management**: Single collection per workspace easier to monitor and maintain
3. **Flexible Filtering**: Metadata-based filtering allows for complex queries across multiple extractors
4. **Future Extensibility**: Easy to add new subcollection types (chats, documents, etc.)
5. **Performance**: Better resource utilization and potentially faster searches within workspace scope

## Risk Mitigation

1. **Backward Compatibility**: Maintain support for existing separate collections during transition
2. **Gradual Migration**: Implement feature flags to control rollout
3. **Data Integrity**: Comprehensive migration scripts with validation
4. **Performance Testing**: Validate that metadata filtering doesn't significantly impact search performance
5. **Rollback Plan**: Ability to revert to separate collections if issues arise

## Success Criteria

- [ ] New extractors use unified workspace collections
- [ ] Metadata filtering correctly isolates documents by extractor
- [ ] Existing extractors can be migrated without data loss
- [ ] Search performance remains acceptable with metadata filtering
- [ ] Chat integration follows the same pattern (future)
- [ ] Reduced Qdrant collection count per workspace
- [ ] Backward compatibility maintained during transition

## Timeline Estimate

- **Phase 1-2**: 2-3 weeks (Infrastructure and vectorstore updates)
- **Phase 3**: 1-2 weeks (Engine manager integration)
- **Phase 4**: 1 week (Extractor flow updates)
- **Phase 5**: 2-3 weeks (Database updates and migration scripts)
- **Phase 6**: 1-2 weeks (Chat integration - future)
- **Phase 7**: 1 week (Configuration and cleanup)

**Total Estimated Time**: 8-12 weeks for full implementation and migration
