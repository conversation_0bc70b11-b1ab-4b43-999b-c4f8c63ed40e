import argparse
import json
import os
import logging
import time
import queue
import threading
import tqdm

import numpy as np

from ira_chat.client import api_client
from ira_chat.utils import utils


logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-k', '--key', required=True)
    parser.add_argument('-u', '--url', default='https://winpharma.hotline.kibernetika.io')
    parser.add_argument('-i', '--input-dir', required=True)
    parser.add_argument('-s', '--start', type=int, default=0)
    parser.add_argument('-e', '--end', type=int, default=None)
    parser.add_argument('-d', '--dataset-id', type=str, default=None)
    parser.add_argument('-n', '--dataset-name')
    parser.add_argument('-w', '--workers', type=int, default=2)

    return parser.parse_args()


def _retry_send(func, *args, kwargs=None, delay=0, tries=1, backoff=1, jitter=0, max_delay=256):
    _tries, _delay = tries, delay
    kwargs = kwargs if kwargs else {}
    while _tries:
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f'[RETRY] Failed send: {str(e)}')
            _tries -= 1

            time.sleep(_delay)
            _delay *= backoff
            _delay += jitter
            _delay = max_delay if _delay > max_delay else _delay
    
    raise TimeoutError('All retries exceeded')


def upload_one(client: api_client.APIClient, path, q: queue.Queue, **kwargs):
    name = os.path.basename(path)
    kwargs.update({'wait': True})
    with open(path, 'rb') as f:
        new_kwargs = kwargs.copy()
        new_kwargs['name'] = name
        new_kwargs['fp'] = f

        result = _retry_send(client.dataset_file_upload, kwargs=new_kwargs, delay=1, tries=30, jitter=1)
        q.put(result)


def upload_many(client: api_client.APIClient, paths, q: queue.Queue, **kwargs):
    for path in paths:
        upload_one(client, path, q, **kwargs)


def select_dataset(client: api_client.APIClient, dataset_name: str = None, dataset_id: str = None):
    if dataset_id is None and dataset_name is None:
        raise ValueError('Either --dataset-id or --dataset-name must be provided')

    if dataset_id is None:
        matched_datasets = client.dataset_list(q=dataset_name)

        if not matched_datasets['items']:
            raise ValueError(f'Dataset with name "{dataset_name}" not found')
        if len(matched_datasets['items']) > 1:
            raise ValueError(f'Multiple datasets found with name "{dataset_name}", please provide --dataset-id')
        dataset_id = matched_datasets['items'][0]['id']

    return dataset_id


def main():
    utils.setup_logging()
    args = parse_args()
    input_dir = args.input_dir
    start, end = args.start, args.end
    files = sorted(os.listdir(input_dir))[start:end]
    files = [os.path.join(input_dir, f) for f in files]
    workers = args.workers
    client = api_client.APIClient(args.key, args.url)

    if args.dataset_id is None:
        args.dataset_id = select_dataset(client, dataset_name=args.dataset_name)

    paths_list = np.array_split(files, workers)
    threads = []
    q = queue.Queue()
    for paths in paths_list:
        t = threading.Thread(
            target=upload_many,
            args=(client, paths, q),
            kwargs={'dataset_id': args.dataset_id},
            daemon=True
        )
        t.start()
        threads.append(t)

    results = []
    for _ in tqdm.tqdm(files):
        results.append(q.get())

    for t in threads:
        t.join()


if __name__ == '__main__':
    main()
