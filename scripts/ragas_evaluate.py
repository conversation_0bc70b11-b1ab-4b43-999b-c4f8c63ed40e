import argparse
import json

import pandas as pd
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from ragas import RunConfig
from ragas import evaluate
from ragas.dataset_schema import EvaluationDataset
from ragas.metrics import faithfulness, answer_relevancy, ContextUtilization
from ragas.metrics import answer_correctness, answer_similarity, context_precision, context_recall


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-d', '--data', required=True, help='path to data file (.csv, .xlsx)')
    parser.add_argument('-o', '--output', default='metrics.csv')
    parser.add_argument('-l', '--limit', type=int, help='Limit number of rows to evaluate')
    return parser.parse_args()


def main():
    args = parse_args()
    file_name = args.data
    if file_name.endswith('.csv'):
        df = pd.read_csv(file_name)
    elif file_name.endswith('.xlsx'):
        df = pd.read_excel(file_name)
    else:
        raise ValueError('Unsupported file format')

    # Calculate metrics
    metrics = [
        faithfulness,
        answer_relevancy,
        ContextUtilization(),
        answer_correctness,
        answer_similarity,
        context_precision,
        context_recall
    ]
    llm = ChatOpenAI(model_name='gpt-4o-mini', temperature=0.2)
    embeddings = OpenAIEmbeddings()
    df['context'] = df['context'].apply(json.loads)
    df = df.rename(
        columns={
            'question': 'user_input',
            'answer': 'response',
            'ground_truth': 'reference',
            'context': 'retrieved_contexts',
        }
    )
    df_with_context = df[df['retrieved_contexts'].str.len() > 0].reset_index(drop=True)
    df_with_context.loc[:, 'retrieved_contexts'] = df_with_context['retrieved_contexts'].apply(
        lambda x: [d['page_content'] for d in x]
    )
    if args.limit:
        df_with_context = df_with_context[:args.limit]

    dataset = EvaluationDataset.from_list(df_with_context.to_dict(orient='records'))

    # Run evaluation
    results = evaluate(
        dataset,
        metrics,
        llm=llm,
        embeddings=embeddings,
        show_progress=True,
        run_config=RunConfig(max_workers=4),
    )
    scores_df = results.scores.to_pandas()
    for key in results:
        print(f'{key} = {results[key]:.4f}')

    output_path = args.output
    output_df = pd.concat([df_with_context, scores_df], axis=1)
    output_df.to_csv(output_path, index=False)
    print(f'Metrics saved to {output_path}')


if __name__ == '__main__':
    main()
