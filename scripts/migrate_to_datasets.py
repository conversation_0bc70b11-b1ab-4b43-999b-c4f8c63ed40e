import argparse

import asyncio
import logging

import tqdm

from ira_chat.api import api
from ira_chat.api.files import get_storage_filename
from ira_chat.config.shared_config import SharedConfig
from ira_chat.credentials.credentials import ADMIN_IDS
from ira_chat.db import api as db_api, models
from ira_chat.services import engine_manager, langchain_svc, vectorstore
from ira_chat.services.dataset_manager import get_storage_dataset_file_name
from ira_chat.services.engine_manager import index_namespace_suffix
from ira_chat.services.vectorstore import get_index_namespace
from ira_chat.utils import utils


logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser()
    # parser.add_argument('file')
    # parser.add_argument('-o', '--output', default='section_extract.zip')
    return parser.parse_args()


async def main():
    await api.startup()
    # suppress logging from httpx
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('ira_chat.services.vectorstore').setLevel(logging.WARNING)
    logging.getLogger('ira_chat.files.local_manager').setLevel(logging.WARNING)
    logging.getLogger('ira_chat.services.llms.openai_llm').setLevel(logging.WARNING)

    admin_id = next(iter(ADMIN_IDS)) if ADMIN_IDS else 5
    args = parse_args()

    workspaces: list[models.Workspace] = await db_api.list_workspaces()
    orgs = await db_api.list_orgs(list(set([w.org_id for w in workspaces])))
    org_map = {o.id: o for o in orgs}
    file_manager = SharedConfig().file_manager

    count = 0
    for ws in workspaces:
        file_count = await db_api.get_file_count(workspace_id=ws.id)
        count += file_count

    logger.info(f'Found {len(workspaces)} workspaces with {count} files')

    pbar = tqdm.tqdm(total=count)

    # Create dataset from each workspace having files
    for ws in workspaces:
        files, _ = await db_api.list_files(workspace_id=ws.id)
        if not files:
            continue

        # Create dataset
        try:
            dataset = await db_api.get_dataset_by_org_and_name(org_id=ws.org_id, name=ws.name)
        except:
            dataset = await db_api.create_dataset({
                'name': ws.name,
                'display_name': ws.display_name,
                'description': ws.description,
                'org_id': ws.org_id,
                'owner_id': admin_id,
                'total_size': sum([f.size for f in files]),
                'status': {'status': models.STATUS_SUCCESS, 'message': None},
                'file_count': len(files),
            })

        # Create a single index for each dataset
        try:
            index = await db_api.get_dataset_index_by_dataset_and_name(dataset_id=dataset.id, name=ws.name)
        except:
            index = await db_api.create_dataset_index({
                'name': ws.name,
                'display_name': ws.display_name,
                'dataset_id': dataset.id,
                'org_id': ws.org_id,
                'owner_id': admin_id,
                'status': {'status': models.STATUS_SUCCESS, 'message': None},
                'total_size': 0,
                'file_count': len(files),
                'vector_count': 0,
                'config': {'index_type': ws.get_config().get('index_type', engine_manager.INDEX_NORMAL)},
                'embedding_provider': ws.get_config().get('llm_type', org_map[ws.org_id].get_config().get('llm_type', 'openai')),
            })
        # Copy each file in new location
        # Re-create files for datasets
        vs = vectorstore.init_vectorstore('', org_map[ws.org_id].get_config(), ws.id)
        qdrant_client = vs.client

        # Copy qdrant collection itself for each index:
        old_collection_name = get_index_namespace(index_namespace_suffix(org_map[ws.org_id], ws, None))
        new_collection_name = get_index_namespace(index_namespace_suffix(org_map[ws.org_id], dataset, index.name))
        exists_new = qdrant_client.collection_exists(new_collection_name)
        exists = qdrant_client.collection_exists(old_collection_name)
        if not exists_new:
            langchain_svc.prepare_vectorstore(vs, new_collection_name, True)

        if exists:
            old_vs = langchain_svc.copy_prepare_vectorstore(vs, old_collection_name)
            size, vector_count = langchain_svc.get_vectorstore_size_vector_count(old_vs)
            await db_api.update_dataset_index(index, {'total_size': size, 'vector_count': vector_count})

        offset = None
        while True and exists:
            points, offset = qdrant_client.scroll(
                old_collection_name, limit=2000, with_payload=True, with_vectors=True, offset=offset,
            )

            if points:
                qdrant_client.upload_points(new_collection_name, points, batch_size=500)

            if not offset:
                break
        # Delete old qdrant index
        qdrant_client.delete_collection(old_collection_name)

        for f in files:
            old_name = get_storage_filename(org_map[ws.org_id].name, ws.name, f)
            # Create new dataset file
            try:
                data = await file_manager.read_and_get_data(old_name)
            except:
                pbar.update(1)
                continue

            new_file = await db_api.create_dataset_file({
                'owner_id': f.owner_id,
                'name': f.name,
                'size': f.size,
                'org_id': ws.org_id,
                'dataset_id': dataset.id,
                'hash': utils.hash_sha256(data),
                'status': {'status': models.STATUS_SUCCESS, 'message': None}
            })
            new_name = get_storage_dataset_file_name(org_map[ws.org_id].name, ws.name, new_file)
            await file_manager.copy_file(old_name, new_name)

            # Re-create file embeddings for files
            embeddings = await db_api.list_embeddings(file_id=f.id)
            new_embeddings = []
            for emb in embeddings:
                new_embeddings.append({'dataset_file_id': new_file.id, 'id': emb.id, 'index_id': index.id})
            await db_api.create_dataset_file_embeddings(new_embeddings)

            docs = await db_api.list_documents(file_id=f.id)
            for doc in docs:
                doc_metadata = doc.get_doc_metadata()
                doc_metadata['file_id'] = new_file.id
                await db_api.create_dataset_file_document({
                    'doc_metadata': doc_metadata,
                    'content': doc.content,
                    'dataset_file_id': new_file.id,
                    'index_id': index.id,
                    'id': doc.id,
                })

            # Delete embeddings for old files
            await db_api.delete_embeddings(file_id=f.id)
            await db_api.delete_documents(file_id=f.id)
            # Delete old file
            await db_api.delete_file(f.id)
            # Delete file from old location
            await file_manager.delete_file(old_name)
            pbar.update(1)

        # Change workspace config to use the index
        ws_config = ws.get_config()
        if not ws_config.get('index'):
            ws_config['index'] = f'{dataset.name}/{index.name}'
            await db_api.update_workspace(ws, {'config': ws_config})
        logger.info(f'Finished migrating workspace {ws.name}')


if __name__ == '__main__':
    asyncio.run(main())
