import argparse
import asyncio
import base64
import io
import json
import mimetypes
import os.path
import re
import time
import Levenshtein

import jsondiff
import numpy as np
import pandas as pd
import pdf2image
import tqdm
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from ragas import EvaluationDataset, evaluate, RunConfig
from ragas.metrics import AnswerSimilarity

"""


"""

prompt = """
Extract all relevant data and text from an invoice document, ensuring accuracy, especially for tables and identifiers. 
Output the data in JSON format.

# Steps

1. **Identify Key Sections**:
- Locate the header section with invoice details such as invoice ID, date, and supplier details.
- Identify the table section containing line items with descriptions, quantities, unit prices, and total amounts.
- Find any additional sections containing terms, conditions, or notes.

2. **Extract Data**:
- Extract invoice ID, supplier information, and date from the header.
- Capture each line item in the table, ensuring to record item descriptions, quantities, unit costs, and total prices accurately.
- Collect any additional textual information from accompanying notes or terms.

3. **Verify Accuracy**:
- Cross-check the captured data against the original document for completeness and correctness.
- Ensure that IDs and numbers are transcribed without errors.

# Notes

- Pay extra attention to maintaining formatting integrity for tables and textual sections.
- Be aware of potential inconsistencies in the document formatting, especially with scanned documents.
- Handle multi-page invoices by ensuring continuity of data extraction across pages.
- Start by recognizing all the listed items in the table.

The information must be outputted in JSON format with the following structure:
```json
{
  "document_type": "Invoice",
  "invoice_number": "INV0000000001",
  "invoice_date": "2025-01-01",
  "total_pages": 1,
  "seller": {
    "name": "Sample Seller",
    "address": "123 Sample Street, Sample City, Sample Country",
    "tax_id": "XX123456789",
    "contact_email": "<EMAIL>",
    "customer_service": {
      "name": "Sample Customer Service",
      "phone": "************"
    }
  },
  "buyer": {
    "name": "Sample Buyer",
    "pharmacy": "Sample Pharmacy",
    "address": "456 Example Avenue, Example City, Example Country",
    "tax_id": "XX987654321"
  },
  "shipment": {
    "expedition_date": "2025-01-02",
    "site": "Sample Site",
    "shipping_method": "Courier",
    "tracking_number": "TRACK123456789",
    "delivery_date": "2025-01-03"
  },
  "items": [
    {
      "article_code": "1234567",
      "reference_code": "REF1234567",
      "supplier_reference": "SUPP-1234567",
      "description": "Sample Item 1",
      "quantity": 10,
      "unit_of_measure": "box",
      "batch_number": "BATCH12345",
      "expiration_date": "2026-01-01",
      "unit_price": 10.00,
      "discount_percentage": 0,
      "net_unit_price": 10.00,
      "tax_rate": 5.00,
      "total_ht": 100.00
    }
  ],
  "totals": {
    "base_ht": 190.00,
    "total_discount": 10.00,
    "additional_fees": 0.00,
    "total_tax": 9.50,
    "total_ttc": 199.50,
    "currency": "USD",
    "original_currency": "USD",
    "exchange_rate": 1.00
  },
  "payment_terms": {
    "due_date": "2025-02-01",
    "payment_date": "2025-02-01",
    "payment_period": "30 days",
    "payment_method": "Bank Transfer",
    "bank_details": {
      "iban": "***************************",
      "bic": "SAMPLEBICXXX"
    },
    "payment_status": "Pending",
    "late_fee_policy": {
      "fixed_fee": 50,
      "penalty_rate": "5% per month"
    },
    "early_payment_discount": "None"
  }
}
```

If document does not contain any of the above fields, leave them empty.
Start by reasoning about the structure of the document, number of items in the table, and the presence of additional sections.
"""
tiff_sig = '49492a00'
png_sig = '89504e47'
jpg_sig = 'ffd8ffe0'
pdf_sig = '********'
sigs = {
    '.tiff': tiff_sig,
    '.tif': tiff_sig,
    '.png': png_sig,
    '.jpg': jpg_sig,
    '.jpeg': jpg_sig,
}


def get_llm(llm_type: str, model: str) -> ChatOpenAI:
    kwargs = {
        'vllm': {
            'openai_api_key': 'EMPTY',
            'openai_api_base': os.getenv('VLLM_API_URL', 'http://localhost:8000/v1'),
        },
        'openai': {
            'openai_api_key': os.getenv('OPENAI_API_KEY'),
        },
        'ollama': {
            'openai_api_key': 'EMPTY',
            'openai_api_base': os.getenv('OLLAMA_API_URL', 'http://localhost:11434/v1'),
        }
    }
    llm = ChatOpenAI(
        model_name=model,
        temperature=0.7,
        max_tokens=16384,
        timeout=600,
        max_retries=10,
        **kwargs[llm_type]
    )
    return llm


def detect_extension(sig_4bytes: str) -> str | None:
    for ext, sig in sigs.items():
        if sig_4bytes == sig:
            return ext
    return None


def build_message(file_data):
    if isinstance(file_data, str):
        return {"type": "text", "text": file_data}

    file_data_bs = base64.b64encode(file_data)
    ext = detect_extension(file_data[:4].hex())

    mimetype = mimetypes.guess_type(f'a{ext}')[0]
    return {
        "type": "image_url", "image_url": {"url": f"data:{mimetype};base64,{file_data_bs.decode()}"}
    }


def parse_args():
    parser = argparse.ArgumentParser()
    # parser.add_argument('-k', '--key')
    # parser.add_argument('-u', '--url', default=API_URL)
    # parser.add_argument('-ws', '--workspace-id')
    parser.add_argument('--llm', default='vllm')
    parser.add_argument('--model', default='meta-llama/Llama-3.2-90B-Vision-Instruct')
    parser.add_argument('-d', '--dir', required=True, help='path to directory with files')
    parser.add_argument('-o', '--output', default='output.csv', help='path to output file')
    parser.add_argument('-gt', '--ground-truth', help='path to ground truth file')
    parser.add_argument('-v', '--verbose', action='store_true')
    parser.add_argument('--limit', type=int, help='Limit number of files to process')
    parser.add_argument('--offset', type=int, help='Offset number of files to process')
    parser.add_argument('--mode', default='process', choices=['process', 'evaluate'])
    parser.add_argument('--ragas-evaluate', action='store_true')
    parser.add_argument('--concurrency', default=2, type=int)

    return parser.parse_args()


async def process_files(files, llm, prompt, concurrency, verbose):
    sem = asyncio.Semaphore(concurrency)
    pbar = tqdm.tqdm(total=len(files))

    async def process_file(file, sem):
        async with sem:
            if file.endswith('.pdf'):
                imgs = pdf2image.convert_from_path(file, dpi=300)
                all_messages = [SystemMessage(content=prompt)]
                msg_content = []
                for img in imgs:
                    buf = io.BytesIO()
                    img.save(buf, format='JPEG')
                    data = buf.getvalue()
                    msg_content.append(build_message(data))
                all_messages.append(HumanMessage(content=msg_content))
            else:
                data = open(file, 'rb').read()
                message_content = [
                    {"type": "text", "text": prompt},
                    build_message(data),
                ]
                all_messages = [HumanMessage(content=message_content)]

            start_time = time.time()
            result = await llm.ainvoke(all_messages)
            process_time = time.time() - start_time

            if verbose:
                print(file)
                print(result.content)
                print()

            pbar.update(1)
            return {
                'file': os.path.basename(file),
                'content': result.content,
                'usage_metadata': json.dumps(result.usage_metadata),
                'process_time': process_time
            }

    tasks = [asyncio.create_task(process_file(file, sem)) for file in files]
    results = await asyncio.gather(*tasks)

    return results


def extract_json(text):
    match = re.search(r"({.*?})", text, re.DOTALL)
    if match:
        json_str = match.group(1).strip()
        # TODO: seems like it breaks special symbols including ó or á in translations
        # json_str = json_str.replace("\\", "\\\\")
        try:
            json_obj = json.loads(json_str)
            return json_obj
        except json.JSONDecodeError:
            json_str = text[text.index('{'):text.rindex('}')+1]
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                return None
    else:
        print("No JSON block found in the input text")
        return None


def compute_json_similarity(row):
    if row['json_output'] is None:
        return 0
    try:
        json_answer = extract_json(row['content'])
    except Exception as e:
        ch = str(e).index('char') + 5
        error_string = ''
        if ch != -1:
            epart = str(e)[ch:]
            idx = int(epart[:epart.index(')')])
            error_string = row['content'][idx-50:idx+50]
        print(f'Error on row {row.file} : {e}')
        if error_string:
            print(f'...\n{error_string}...\n')
        return 0

    json_gt = extract_json(row['ground_truth'])
    return jsondiff.similarity(json_answer, json_gt)


async def main():
    args = parse_args()
    llm = get_llm(args.llm, args.model)

    exts = ('.pdf', '.jpg', '.jpeg', '.png')
    files = sorted([os.path.join(args.dir, f) for f in os.listdir(args.dir) if f.endswith(exts)])
    if args.limit:
        files = files[:args.limit]

    if args.mode == 'process':
        results = await process_files(files, llm, prompt, args.concurrency, args.verbose)
        df = pd.DataFrame(results)
        df.to_csv(args.output, index=False)

        print(f'Took {df["process_time"].mean():.2f} seconds on average to process each file.')
        print()
        stats_output(df)

    elif args.mode == 'evaluate':
        # Evaluate files
        df = pd.read_csv(args.output)
        if args.ground_truth:
            gt = pd.read_csv(args.ground_truth)
            if 'ground_truth' in df.columns:
                df = df.drop(columns=['ground_truth'])
            df = pd.merge(df, gt, on='file', suffixes=('', '_gt'))

        if args.offset:
            df = df[args.offset:]
        if args.limit:
            df = df[:args.limit]
        stats_output(df.copy())

        df['json_output'] = df['content'].apply(extract_json)
        df['similarity'] = df.apply(compute_json_similarity, axis=1)
        print(f'JSON similarity mean: {df["similarity"].mean():.4f}')

        if args.ragas_evaluate:
            # Compute answer similarity with ragas
            json_df = df[df['json_output'].notnull()].copy()
            json_df['reference'] = json_df['ground_truth'].apply(lambda x: json.dumps(extract_json(x)))
            json_df['response'] = json_df['content'].apply(lambda x: json.dumps(extract_json(x)))

            json_df['user_input'] = 'Based on the invoice image, get the JSON data: [invoice image]'

            dataset = EvaluationDataset.from_list(
                json_df[['reference', 'response', 'user_input']].to_dict(orient='records')
            )
            metrics = [AnswerSimilarity()]
            embeddings = OpenAIEmbeddings()
            result = evaluate(
                dataset,
                metrics,
                llm=ChatOpenAI(model_name='gpt-4o-mini'),
                embeddings=embeddings,
                show_progress=True,
                run_config=RunConfig(max_workers=16),
            )
            for metric in metrics:
                print(f'{metric.name}: {np.array(result[metric.name]).mean():.4f}')
                json_df[metric.name] = np.array(result[metric.name])

            def compute_ratio(row):
                return Levenshtein.ratio(
                    row['reference'].replace(' ', '').replace('\n', ''),
                    row['response'].replace(' ', '').replace('\n', '')
                )

            json_df['levenshtein_ratio'] = json_df.apply(compute_ratio, axis=1)
            print(f'Levenshtein ratio mean: {json_df["levenshtein_ratio"].mean():.4f}')
            score = (
                json_df["levenshtein_ratio"].mean() + json_df["similarity"].mean()
            ) / 2 * (len(json_df) / len(df))
            print(f'Overall score: {score:.4f}')


def stats_output(df):
    def str_or_dict_key_fn(key):
        def str_or_dict_key(x):
            if isinstance(x, str):
                return json.loads(x.replace("'", '"'))[key]
            if isinstance(x, dict):
                return x[key]
        return str_or_dict_key
    df['total_tokens'] = df['usage_metadata'].apply(str_or_dict_key_fn('total_tokens'))
    df['output_tokens'] = df['usage_metadata'].apply(str_or_dict_key_fn('output_tokens'))

    df['output_len'] = df['content'].apply(len)
    df['json_output'] = df['content'].apply(extract_json)
    json_num = len(df[df['json_output'].notnull()])

    df['tokens_per_second'] = df['total_tokens'] / df['process_time']
    df['out_tokens_per_second'] = df['output_tokens'] / df['process_time']
    print(f"Mean answer length = {df['output_len'].mean()}")
    print(f'Mean process time = {df["process_time"].mean()}')
    print(f"Mean total tokens per second = {df['tokens_per_second'].mean()}")
    print(f"Mean output tokens per second = {df['out_tokens_per_second'].mean()}")
    print(f"JSON output exists = {json_num} out of {len(df)}; {json_num / len(df) * 100:.2f}%")
    print()


if __name__ == '__main__':
    asyncio.run(main())
