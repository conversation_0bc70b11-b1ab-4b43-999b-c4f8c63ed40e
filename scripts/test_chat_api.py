import argparse
import json
import mimetypes
import os.path
import re
import time
import requests


HEADERS = {}
API_URL = 'https://winpharma.hotline.kibernetika.io/api/v1'
detection_search_title = 'test-from-API'


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-k', '--key', required=True)
    parser.add_argument('-m', '--message', required=True)
    parser.add_argument('-t', '--title', default=detection_search_title)
    parser.add_argument('-u', '--url', default=API_URL)
    parser.add_argument('--llm', default='openai')
    parser.add_argument('-d', '--delete', action='store_true', help='Delete chat after completion')
    parser.add_argument('-q', '--quiet', action='store_true', help='Silent mode, only message output')

    return parser.parse_args()


def create_new_chat(title, config=None):
    resp = requests.post(
        f'{API_URL}/chats',
        headers=HEADERS,
        json={'title': title, 'config': config}
    )
    if resp.status_code >= 400:
        raise ValueError(resp.content)

    chat = resp.json()
    return chat


def create_message(chat_id, content):
    resp = requests.post(
        f'{API_URL}/chats/{chat_id}/messages',
        headers=HEADERS,
        json={'content': content},
    )
    if resp.status_code >= 400:
        raise ValueError(resp.content)
    message = resp.json()
    return message


def wait_for_completion(message, timeout=180):
    url = (
        f'{API_URL}/chats/'
        f'{message["chat_id"]}/messages/{message["id"]}'
    )
    completed = False
    start = time.time()
    while not completed:
        resp = requests.get(url, headers=HEADERS)
        if resp.status_code >= 400:
            raise ValueError(resp.content)

        message = resp.json()
        status = message["status"]
        completed = status in {'SUCCESS', 'ERROR', 'ERROR_REASON'}
        if time.time() - start > timeout:
            raise TimeoutError(f"Timed out waiting for the message id={message['id']}")
        time.sleep(1)
    return message


def delete_chat(message):
    url = (
        f'{API_URL}/chats/'
        f'{message["chat_id"]}'
    )
    resp = requests.delete(url, headers=HEADERS)
    if resp.status_code >= 400:
        raise ValueError(resp.content)


def main():
    args = parse_args()

    global API_KEY, API_URL, HEADERS
    API_KEY = args.key
    API_URL = args.url
    HEADERS = {'Authorization': f'Bearer {API_KEY}'}
    config = {'llm_type': args.llm}
    verbose = not args.quiet

    # Get detections list and search by title.
    # If you have a detection id - then proceed to send an image to that detection.
    if verbose:
        print('create a new chat')
    chat = create_new_chat(args.title, config)

    # Send an image to detection for recognition
    if verbose:
        print('send message')
    message = create_message(chat['id'], args.message)

    if verbose:
        print('wait')
    # Wait recognition process to complete
    completed_message = wait_for_completion(message)

    if completed_message['status'] in ['ERROR', 'ERROR_REASON']:
        print(completed_message['results'][-1]['content'])
        exit(1)
    else:
        output = completed_message['results'][-1]['content']
        context = completed_message['results'][-1]['context']
        # Just print the output
        for doc in context.get('source_documents', []):
            print(f'Source: {doc["metadata"]["source"]}, page {doc["metadata"].get("page")}')
            print(f'{doc["page_content"]}')
            print()

        print()
        print("Final answer:")
        print(output)

    if args.delete:
        delete_chat(completed_message)
        if verbose:
            print(f'chat id={completed_message["chat_id"]} deleted')


if __name__ == '__main__':
    main()
