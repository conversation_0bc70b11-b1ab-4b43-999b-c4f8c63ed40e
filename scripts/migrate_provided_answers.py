import argparse
import asyncio
import logging

import tqdm

from ira_chat.api import api
from ira_chat.config.shared_config import SharedConfig
from ira_chat.db import api as db_api, models
from ira_chat.services import dataset_manager

logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser()
    return parser.parse_args()


async def delete_old_embeddings(self: dataset_manager.DatasetManager, file_db: models.DatasetFile):
    indexes = await db_api.list_dataset_indexes(dataset_id=self.ds.id)
    for index in indexes:
        # if index.is_locked():
        #     continue
        embeddings = await db_api.list_dataset_file_embeddings(file_id=file_db.id, index_id=index.id)

        logger.info(f'Deleting {len(embeddings)} [index={index.name}] embeddings associated with {file_db.name}...')
        # if file_db.source_type == models.SourceType.CORRECTED_TXT:
        #     vs = await self._get_index_vectorstore(index, 'corrected')
        # else:
        vs = await self._get_index_vectorstore(index)
        await self.delete_index_embeddings(embeddings, index, delete_db=True, vs=vs)

        if file_db.source_type == models.SourceType.CORRECTED_TXT:
            # Delete from both corrected and original vectorstore
            vs = await self._get_index_vectorstore(index, 'corrected')
            await self.delete_index_embeddings(embeddings, index, delete_db=True, vs=vs)
    logger.info(f'Done deleting vectorstore embeddings associated with {file_db.name}.')


async def process_file_for_all_indexes(self, file: models.DatasetFile, reprocess=False, force_index_recreate=False):
    file = await self.process_file(file, reprocess=reprocess)

    indexes = await db_api.list_dataset_indexes(dataset_id=self.ds.id)
    for i, index in enumerate(indexes):
        # if index.is_locked():
        #     logger.info(f'Skipping index {index.name} as it is locked')
        #     continue

        await self.index_file(file, index, force_index_recreate=force_index_recreate)

    return file


async def main():
    await api.startup()
    # suppress logging from httpx
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('ira_chat.services.vectorstore').setLevel(logging.WARNING)
    logging.getLogger('ira_chat.files.local_manager').setLevel(logging.WARNING)
    logging.getLogger('ira_chat.services.llms.openai_llm').setLevel(logging.WARNING)

    args = parse_args()

    orgs = await db_api.list_orgs()
    org_map = {o.id: o for o in orgs}
    datasets, _ = await db_api.list_datasets()
    dataset_map = {d.id: d for d in datasets}
    files: list[models.DatasetFile] = await db_api.list_dataset_files(
        dataset_ids=[d.id for d in datasets], source_type=models.SourceType.CORRECTED_TXT
    )
    file_manager = SharedConfig().file_manager

    count = len(files)
    logger.info(f'Found {len(datasets)} datasets with {count} files')

    # Create dataset from each workspace having files
    manager_map = {d.id: dataset_manager.DatasetManager(org_map[d.org_id], d) for d in dataset_map.values()}

    for file in tqdm.tqdm(files):
        manager = manager_map[file.dataset_id]
        await delete_old_embeddings(manager, file)

        await process_file_for_all_indexes(manager, file)


if __name__ == '__main__':
    asyncio.run(main())
