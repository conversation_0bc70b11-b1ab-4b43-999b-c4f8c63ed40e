
import argparse
import asyncio

from ira_chat.client.api_client import APIClient


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-k', '--key')
    parser.add_argument('-u', '--url', default='https://winpharma.hotline.kibernetika.io')
    parser.add_argument('-n', '--dataset-name')
    parser.add_argument('-id', '--dataset-id')
    parser.add_argument('-o', '--output-dir', default='output', help='path to output directory')

    return parser.parse_args()


async def main():
    args = parse_args()
    # 1. Get api client
    client = APIClient(args.key, args.url)
    # 2. Get all files
    file_count = client.dataset_file_list(args.dataset_id, limit=1)['count']
    limit = 500
    pages = file_count // limit + 1
    # 3. gather file hashes and find duplicates
    hashes = set()
    names = set()
    for page in range(1, pages+1):
        files = client.dataset_file_list(args.dataset_id, limit=limit, page=page)['items']
        for file in files:
            if file['hash'] in hashes:
                print(f'Duplicate: {file["name"]}')
            else:
                hashes.add(file['hash'])

            if file['name'] in names:
                print(f'Duplicate name: {file["name"]}')
            else:
                names.add(file['name'])

    # 4. delete duplicates


if __name__ == '__main__':
    asyncio.run(main())
