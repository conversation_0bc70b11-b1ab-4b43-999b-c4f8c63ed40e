import argparse
import asyncio
import json
import operator
import os.path
import aiofiles
import tqdm
import time

import pandas as pd
import numpy as np
import tqdm

from ira_chat.client.api_client import APIClient

API_URL = 'https://winpharma.hotline.kibernetika.io'


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-k', '--key')
    parser.add_argument('-u', '--url', default=API_URL)
    parser.add_argument('-n', '--dataset-name')
    parser.add_argument('-id', '--dataset-id')
    parser.add_argument('-o', '--output-dir', default='output', help='path to output directory')
    parser.add_argument('-c', '--concurrency', default=16, type=int)
    parser.add_argument('--limit', type=int, help='Limit number of files to download')

    return parser.parse_args()


async def download_one(client: APIClient, dataset_id, output_dir, file_id, file_name, queue):
    loop = asyncio.get_event_loop()
    try:
        file_data = await loop.run_in_executor(None, client.dataset_file_download, dataset_id, file_id)
        output_path = os.path.join(output_dir, file_name)

        async with aiofiles.open(output_path, 'wb') as f:
            await f.write(file_data)
    except Exception as e:
        queue.put((file_name, str(e)))
    else:
        await queue.put((file_name, None))


async def download_files(client: APIClient, dataset_id, files: list, output_dir, queue):
    for file in files:
        await download_one(client, dataset_id, output_dir, file['id'], file['name'], queue)


async def select_dataset(client: APIClient, dataset_name: str = None, dataset_id: str = None):
    if dataset_id is None and dataset_name is None:
        raise ValueError('Either --dataset-id or --dataset-name must be provided')

    if dataset_id is None:
        matched_datasets = await client.adataset_list(q=dataset_name)

        if not matched_datasets['items']:
            raise ValueError(f'Dataset with name "{dataset_name}" not found')
        if len(matched_datasets['items']) > 1:
            raise ValueError(f'Multiple datasets found with name "{dataset_name}", please provide --dataset-id')
        dataset_id = matched_datasets['items'][0]['id']

    return dataset_id


async def main():
    args = parse_args()
    client = APIClient(args.key, args.url)

    dataset_id = args.dataset_id
    dataset_name = args.dataset_name
    output_dir = args.output_dir
    concurrency = args.concurrency
    limit = args.limit
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    dataset_id = await select_dataset(client, dataset_name, dataset_id)

    files = client.dataset_file_list(dataset_id, limit=1_000_000)['items']
    if limit:
        files = files[:limit]

    chunks = np.array_split(files, concurrency)
    tasks = []
    queue = asyncio.Queue()
    for chunk in chunks:
        tasks.append(
            asyncio.create_task(download_files(client, dataset_id, chunk.tolist(), output_dir, queue))
        )

    for _ in tqdm.tqdm(files):
        file_name, err = await queue.get()
        if err:
            print(f'Error downloading {file_name}: {err}')

    await asyncio.gather(*tasks)
    print(f'Files saved to {output_dir}/')


if __name__ == '__main__':
    asyncio.run(main())
