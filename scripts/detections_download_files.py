import argparse
import asyncio
import io
import json
import os.path
import zipfile
from concurrent.futures import ThreadPoolExecutor

import tqdm

from ira_chat.client import api_client
from ira_chat.utils import json_utils, utils


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-id', '--detection-id', required=True)
    parser.add_argument('-k', '--key', required=True)
    parser.add_argument('-ws', '--workspace-id')
    parser.add_argument('-u', '--url', default='https://winpharma.hotline.kibernetika.io')
    parser.add_argument('-o', '--output-dir', default='output')
    parser.add_argument('--limit', type=int, default=None, help='Limit number of files to download')
    parser.add_argument('--chunk-size', type=int, default=500, help='Chunk size for pagination')
    parser.add_argument('--json', action='store_true', help='Output json')
    parser.add_argument('--only-unique', action='store_true', help='Only unique files')
    parser.add_argument('--concurrency', type=int, default=8, help='Number of workers for parallel downloading')

    return parser.parse_args()


async def process_text_output(output, item_id, output_dir, json_output, progress):
    """Process text output from a detection item"""
    if json_output:
        output = json.dumps(json_utils.extract_json(output))
        if output == 'null':
            print(f'error in item {item_id}')
            progress.update(1)
            return None, 0
        file_name = f'{item_id}_output.json'
    else:
        file_name = f'{item_id}_output.txt'
    
    if not output:
        print(f'error in item {item_id}')
        progress.update(1)
        return None, 0
    
    file_path = os.path.join(output_dir, file_name)
    with open(file_path, 'w') as f:
        f.write(output)
    
    progress.update(1)
    return file_path, 0


async def process_pdf_from_zip(zip_data, output_dir, file_hashes=None):
    """Extract PDF files from a ZIP archive"""
    archive = zipfile.ZipFile(io.BytesIO(zip_data))
    pdf_list = [name for name in archive.namelist() if name.endswith('.pdf')]
    saved_files = []
    duplicates = 0
    
    for pdf in pdf_list:
        with archive.open(pdf) as zf:
            pdf_data = zf.read()
        
        if file_hashes is not None:
            pdf_hash = utils.hash_sha256(pdf_data)
            if pdf_hash in file_hashes:
                duplicates += 1
                continue
            file_hashes.add(pdf_hash)

        pdf_base = os.path.basename(pdf)
        pdf_path = os.path.join(output_dir, pdf_base)
        with open(pdf_path, 'wb') as f:
            f.write(pdf_data)
        saved_files.append(pdf_path)
    
    archive.close()
    return saved_files, duplicates


async def process_file_output(
    file_data, file_name, output_dir, 
    file_hash=None, 
    file_hashes=None, 
    progress=None
):
    """Process a file output from a detection item"""
    
    # Check for duplicates if hash is provided
    if file_hash is not None and file_hashes is not None and not file_name.endswith('.zip'):
        if file_hash in file_hashes:
            progress.update(1)
            return None, 1  # 1 means duplicate found
        file_hashes.add(file_hash)
    
    # Handle zip files
    if file_name.endswith('.zip'):
        # Extract pdf files
        saved_files, duplicates = await process_pdf_from_zip(file_data, output_dir, file_hashes)
        progress.update(1)
        return saved_files, duplicates
    else:
        # Save regular file
        file_path = os.path.join(output_dir, file_name)
        with open(file_path, 'wb') as f:
            f.write(file_data)
        progress.update(1)
        return file_path, 0


async def process_item(
    client, item, output_dir, progress, 
    only_unique=False, 
    json_output=False, 
    file_hashes=None
):
    """Process a single detection item asynchronously"""
    if len(item['results']) < 1:
        progress.update(1)
        return None, 0  # Skip items with no results
        
    last_result = item['results'][-1]
    output_file = last_result.get('output_file')
    
    if not output_file:
        # Process text output
        return await process_text_output(
            last_result['output'], 
            item["id"], 
            output_dir, 
            json_output, 
            progress
        )
    else:
        if not item['results'][-1]['output_file_id']:
            progress.update(1)
            return None, 0
            
        file_name = output_file['name']
        file_data = await client.adetection_file_download(output_file['id'])
        
        # Compute hash if needed
        file_hash = None
        if only_unique:
            file_hash = utils.hash_sha256(file_data)
        
        # Process the file
        return await process_file_output(
            file_data,
            file_name,
            output_dir,
            file_hash,
            file_hashes if only_unique else None,
            progress
        )


async def process_batch(client, items, output_dir, progress, semaphore, only_unique=False, json_output=False, file_hashes=None):
    """Process a batch of items concurrently with limited concurrency"""
    async def process_with_semaphore(item):
        async with semaphore:
            return await process_item(client, item, output_dir, progress, only_unique, json_output, file_hashes)
    
    # Create tasks with semaphore control
    tasks = []
    for item in items:
        tasks.append(process_with_semaphore(item))
    
    results = await asyncio.gather(*tasks)
    
    processed = 0
    duplicates = 0
    
    for result, duplicate in results:
        if result is not None:
            processed += 1
        duplicates += duplicate
    
    return processed, duplicates


async def main():
    args = parse_args()
    client = api_client.APIClient(args.key, args.url)
    if args.workspace_id:
        client.set_workspace(ws_id=args.workspace_id)
    output_dir = args.output_dir

    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)

    # Get initial count of items
    items = await client.adetection_item_list(args.detection_id, limit=1)
    chunk_size = args.chunk_size
    count = items['count']
    limit = args.limit if args.limit else count
    pages = count // chunk_size + 1
    
    # Shared state between workers
    file_hashes = set()
    
    progress = tqdm.tqdm(total=min(count, limit))
    
    # Create a semaphore to limit concurrency
    semaphore = asyncio.Semaphore(args.concurrency)
    
    # Process all items in batches
    total_processed = 0
    total_duplicates = 0
    
    for page in range(1, pages+1):
        items = await client.adetection_item_list(args.detection_id, limit=chunk_size, page=page)
        
        batch_processed, batch_duplicates = await process_batch(
            client, 
            items['items'], 
            output_dir, 
            progress,
            semaphore,
            args.only_unique, 
            args.json, 
            file_hashes
        )
        
        total_processed += batch_processed
        total_duplicates += batch_duplicates
        
        if total_processed >= limit:
            break

    print(f'\nProcessed {total_processed} files.')
    if args.only_unique:
        print(f'Skipped {total_duplicates} duplicate files.')
        print(f'Found {len(file_hashes)} unique files.')


if __name__ == '__main__':
    asyncio.run(main())
