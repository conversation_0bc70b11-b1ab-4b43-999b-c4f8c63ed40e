import argparse
import asyncio
import logging

import tqdm
from sqlalchemy import select

from ira_chat.api import api
from ira_chat.api import common_utils
from ira_chat.db import api as db_api
from ira_chat.db import base
from ira_chat.db import models

logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser()
    return parser.parse_args()


@base.session_aware()
async def list_all_chat_message_outputs_grades(session=None):
    query = select(models.ChatMessageOutput).where(models.ChatMessageOutput.grade_selector.is_not(None))

    query = query.order_by(models.ChatMessageOutput.created_at)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def list_all_chat_message_outputs_rating(session=None):
    query = select(models.ChatMessageOutput).where(models.ChatMessageOutput.rating.is_not(None))

    query = query.order_by(models.ChatMessageOutput.created_at)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def list_all_provided_answers(session=None):
    query = select(models.ProvidedAnswer)
    res = await session.execute(query)

    return res.scalars().fetchall()


async def main():
    await api.startup()
    # suppress logging from httpx
    logging.getLogger('httpx').setLevel(logging.WARNING)

    args = parse_args()

    msg_outputs = await list_all_chat_message_outputs_grades()
    chats = await db_api.list_chats_all(ids=list(set({msg.chat_id for msg in msg_outputs})))
    chat_map = {c.id: c for c in chats}

    async with base.session_context():
        for msg_output in tqdm.tqdm(msg_outputs):
            metric_value = common_utils.grade_to_value(msg_output.grade_selector)
            chat = chat_map.get(msg_output.chat_id)
            if not chat:
                continue

            metric = {
                'workspace_id': chat.workspace_id,
                'app_id': chat.id,
                'app_type': models.APP_TYPE_CHAT,
                'object_id': msg_output.id,
                'type': models.METRIC_CORRECTNESS_GRADE,
                'value': metric_value,
                'created_at': msg_output.created_at,
                'updated_at': msg_output.updated_at,
            }
            await db_api.create_metric(metric)
            metric = {
                'workspace_id': chat.workspace_id,
                'app_id': chat.id,
                'app_type': models.APP_TYPE_CHAT,
                'object_id': msg_output.id,
                'type': models.METRIC_GRADE_EVENT,
                'value': 1,
                'created_at': msg_output.created_at,
                'updated_at': msg_output.updated_at,
            }
            await db_api.create_metric(metric)

    msg_outputs_rating = await list_all_chat_message_outputs_rating()
    chats = await db_api.list_chats_all(ids=list(set({msg.chat_id for msg in msg_outputs})))
    chat_map = {c.id: c for c in chats}

    async with base.session_context():
        for msg_output in tqdm.tqdm(msg_outputs_rating):
            metric_value = msg_output.rating
            chat = chat_map.get(msg_output.chat_id)
            if not chat:
                continue

            metric = {
                'workspace_id': chat.workspace_id,
                'app_id': chat.id,
                'app_type': models.APP_TYPE_CHAT,
                'object_id': msg_output.id,
                'type': models.METRIC_RATING_VALUE,
                'value': metric_value,
                'created_at': msg_output.created_at,
                'updated_at': msg_output.updated_at,
            }
            await db_api.create_metric(metric)
            metric = {
                'workspace_id': chat.workspace_id,
                'app_id': chat.id,
                'app_type': models.APP_TYPE_CHAT,
                'object_id': msg_output.id,
                'type': models.METRIC_RATING_EVENT,
                'value': 1,
                'created_at': msg_output.created_at,
                'updated_at': msg_output.updated_at,
            }
            await db_api.create_metric(metric)

    corrected_answers = await list_all_provided_answers()

    async with base.session_context():
        for provided_answer in tqdm.tqdm(corrected_answers):
            metric_value = 1

            metric = {
                'workspace_id': provided_answer.workspace_id,
                'app_id': provided_answer.app_id,
                'app_type': models.APP_TYPE_CHAT,
                'object_id': provided_answer.object_id,
                'type': models.METRIC_CORRECTED_ANSWER,
                'value': metric_value,
                'created_at': provided_answer.created_at,
                'updated_at': provided_answer.updated_at,
            }
            await db_api.create_metric(metric)


if __name__ == '__main__':
    asyncio.run(main())
