namespace=$1

if [ -z "$namespace" ]; then
  echo "Usage: $0 <namespace>"
  exit 1
fi

# Prepare config map to change storage class

set -e
echo "Working in namespace $namespace"
velero backup create $namespace --include-resources persistentvolumeclaims,persistentvolumes \
 --include-namespaces $namespace --or-selector 'app=qdrant or component=db'

echo "Wait for backup to be completed"
velero backup describe $namespace

exit 0

replicas=$(kuberlab -n $namespace get deploy ira-chat-api -o yaml | grep replicas | head -1 | awk '{print $2}')
db_replicas=$(kuberlab -n $namespace get deploy ira-chat-db -o yaml | grep replicas | head -1 | awk '{print $2}')
qdrant_replicas=$(kuberlab -n $namespace get sts qdrant -o yaml | grep replicas | head -1 | awk '{print $2}')

## scale down
kuberlab -n $namespace scale deploy/ira-chat-api --replicas=0
kuberlab -n $namespace scale deploy/ira-chat-db --replicas=0
kuberlab -n $namespace scale sts/qdrant --replicas=0

# Optional: change Reclaim policy of PVs to 'Retain' to prevent disk deletion
# Delete old PVs, PVCs
db_volume=$(kuberlab -n $namespace get pvc ira-chat-db -o template='{{.spec.volumeName}}')
qdrant_volume=$(kuberlab -n $namespace get pvc qdrant-storage-qdrant-0 -o template='{{.spec.volumeName}}')

kuberlab -n $namespace delete pvc ira-chat-db
kuberlab -n $namespace delete pvc qdrant-storage-qdrant-0
kuberlab delete pv $db_volume
kuberlab delete pv $qdrant_volume

sleep 2
## restore
velero restore create --include-resources persistentvolumeclaims,persistentvolumes --from-backup $namespace

echo "Wait restore to be completed"
exit 0

## scale up
kuberlab -n $namespace scale sts/qdrant --replicas=1
kuberlab -n $namespace scale deploy/ira-chat-db --replicas=1
kuberlab -n $namespace scale deploy/ira-chat-api --replicas=3
