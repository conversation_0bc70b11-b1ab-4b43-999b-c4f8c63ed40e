import argparse
import os
import shutil
import zipfile
import concurrent.futures
import multiprocessing
import tqdm

from ira_chat.services.detections import posology
from ira_chat.utils.http_utils import download_content_sync
from ira_chat.utils.utils import setup_logging


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('file', help="Input file or directory")
    parser.add_argument('-o', '--output', default='section_extract.zip', help="Output file or directory")
    parser.add_argument('--start-text', default=None, help="Override start text for section extraction")
    parser.add_argument('--end-text', default=None, help="Override end text for section extraction")
    parser.add_argument('--extract', action='store_true', help="Extract file from the archive")
    parser.add_argument('-c', '--concurrency', type=int, default=multiprocessing.cpu_count(), help="Number of workers for parallel processing")
    return parser.parse_args()


def _is_full_url(u):
    return u.startswith('https://') or u.startswith('http://')


def process_file(uri, out_path, start_text, end_text, extract_flag):
    # Read file (or download) content
    if _is_full_url(uri):
        data = download_content_sync(uri)
        url = uri
    else:
        with open(uri, 'rb') as f:
            data = f.read()
        url = None

    tempdir, path = posology.save_to_tmp_dir(data=data, name=os.path.basename(uri))
    _, ext = os.path.splitext(path)
    ext = ext if ext else '.html'
    # Generate a unique archive name for each thread
    unique_archive = f"{out_path}.zip" if extract_flag else out_path

    try:
        if ext.lower() == '.pdf':
            section_text = posology.extract_section_pymupdf4llm(
                path,
                unique_archive,
                start_text=start_text,
                end_text=end_text,
                from_line_start=True,
            )
        elif ext.lower() in ['.html', '.htm']:
            section_text = posology.extract_section_html(
                path,
                unique_archive,
                start_text=start_text,
                end_text=end_text,
                from_line_start=True,
                style_map={
                    '.AmmAnnexeTitre1': 'h2',
                    '.AmmAnnexeTitre2': 'h3',
                    '.AmmAnnexeTitre3': 'u,h4',
                    '.AmmAnnexeTitre4': 'u,i',
                    '.AmmCorpsTexteGras': 'b',
                },
                url=url,
            )
        else:
            raise ValueError(f"Unsupported file type: {ext}")
    finally:
        shutil.rmtree(tempdir)

    if extract_flag:
        base_path, _ = os.path.splitext(os.path.basename(path))
        md_in_zip = f'Posology_{base_path}.md'
        with zipfile.ZipFile(unique_archive, 'r') as zip_ref:
            md_data = zip_ref.read(md_in_zip)

        base_out_path, _ = os.path.splitext(out_path)
        out_path = f"{base_out_path}.md"
        with open(out_path, 'w') as f:
            f.write(md_data.decode())
    else:
        # with open(out_path, 'w') as f:
        #     f.write(section_text)
        pass
    
    print(f"Section extracted and saved to {out_path}")


def process_directory(input_dir, out_dir, start_text, end_text, extract_flag, max_concurrent_tasks):
    if not os.path.exists(out_dir):
        os.makedirs(out_dir)

    files = sorted(
        [f for f in os.listdir(input_dir) if os.path.isfile(os.path.join(input_dir, f))]
    )

    with concurrent.futures.ProcessPoolExecutor(max_workers=max_concurrent_tasks) as executor:
        progress = tqdm.tqdm(total=len(files))
        futures = []

        for filename in files:
            file_path = os.path.join(input_dir, filename)
            out_file = os.path.join(out_dir, filename)
            future = executor.submit(
                process_file, 
                file_path, 
                out_file, 
                start_text, 
                end_text, 
                extract_flag
            )
            futures.append(future)

        # Wait for all tasks to complete
        for future in concurrent.futures.as_completed(futures):
            future.result()
            progress.update(1)


def main():
    setup_logging()
    args = parse_args()
    default_start = r'\n[|#*_\s]*4.2[*_.\s]+Poso'
    default_end = r'\n[|#*_\s]*4.3[*_.\s]+Contr'
    start_text = args.start_text if args.start_text is not None else default_start
    end_text = args.end_text if args.end_text is not None else default_end

    if os.path.isdir(args.file):
        input_dir = args.file
        out_dir = args.output if not args.output.endswith('.zip') else "section_extract_dir"
        process_directory(input_dir, out_dir, start_text, end_text, args.extract, args.concurrency)
    else:
        # For single file processing
        process_file(
            args.file, args.output, start_text, end_text, args.extract
        )


if __name__ == '__main__':
    main()
