import argparse
import asyncio
import io
import logging
import os.path
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from dotenv import load_dotenv
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.messages import HumanMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, PromptTemplate
from starlette.datastructures import UploadFile

from ira_chat.services import detection
from ira_chat.services import llm as ira_llm
from ira_chat.services import vectorstore as vs, file_formats
from ira_chat.services.langchain_svc import copy_prepare_vectorstore, prepare_vectorstore
from ira_chat.utils import utils

logger = logging.getLogger(__name__)


main_prompt = """
Get the following information from the context:

Legal Corporate Documents
- Filed Certificate of Incorporation

Action by Incorporator - Appointing Initial Directors
- Action by Anonymous Written Consent of the Board of Directors
- Bylaws
- Form SS-4
- CP575 - Reply to SS-4 - Letter from IRS assigning Tax ID and reporting form  
- 147c Confirmation of Tax ID number

Personal Documents (required from Directors and major Shareholders)
- Passport
- Residency permit
- Verification of Address: Sublease, Utility Bill
- SSN Card

Corporate Data (from Filed Certificate of Incorporation)

- Company Name
- Legal entity type: Corporation or LLC (Limited Liability Company)
- State of registration 
- File Number
- Filed Date 

(from Stamp on Filed of Certificate of Incorporation)

- Registered office address
- Registered agent name
- Number of authorized shared
- Par value per share
- List of initial directors (from Action by Incorporator - Appointing Initial Directors)
  - Name
  - Address
- Incorporator
  - Name 
  - Address
- Corporate Officers (from Action by Anonymous Consent of the Board of Directors) 
  - President Name
  - Treasurer Name
  - Secretary Name
- Stock issuance (List per name) ((from Action by Anonymous Consent of the Board of Directors)
  - Name
  - Amount of Stock
  - Purchase price

- Board seats number (from Bylaws)
[from SS-4]
- Mailing Address
- Legal type: Corporation or LLC (Limited Liability Company)
  - LLC - how many members
- FEY (Closing m onth of accounting year)
- Principal activity
- Products or services sold
- Tax ID / EIN (Employer Identification Number)

---
- The output should be in json format enclosed within ```
- The output should have json fields mentioned above.
"""

document_prompt = """
Do 2 tasks on the image:  
- Describe the image.  
- Extract all the text in details thoroughly.
"""


# main_prompt = "What the stock issuance per name?"


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--collection', default='local-newOrg-IS-test')
    parser.add_argument('--model', default='gpt-4o')
    parser.add_argument('-v', '--vision-model', default='gpt-4o')
    parser.add_argument('--path')
    parser.add_argument('-m', '--mode', choices=['index', 'extract', 'clean'])

    return parser.parse_args()


def extract_document_data_llm(filename, file_data, llm_type, llm, prompt):
    _, ext = os.path.splitext(filename)
    to_recognition = ['.pdf', '.jpg', '.jpeg', '.png', '.tif', '.tiff']

    if ext.lower() in to_recognition:
        file_struct = UploadFile(io.BytesIO(file_data), size=len(file_data), filename=filename)
        file_datas, file_count = asyncio.run(detection.convert_file_image(file_struct, llm_type, auto_rotate=True))

        docs = []
        i = 0
        async for data, name in file_datas:
            message_content = [
                {"type": "text", "text": prompt},
                detection.build_message(data)
            ]

            messages = [HumanMessage(content=message_content)]
            output = llm.invoke(messages)
            logger.info(f'Recognized doc: {output.content}')
            sub_docs = file_formats.parse_as_text(output.content, filename, page=i + 1, total_pages=file_count)
            docs.extend(sub_docs)
            i += 1
    else:
        _, docs = file_formats.load_and_split_file(file_data, filename, additional_meta={'file_id': None})

    return docs


def get_llm_type(model_name: str) -> str:
    if model_name.startswith('gpt-'):
        return 'openai'
    elif model_name.startswith('claude-'):
        return 'anthropic'
    elif model_name.startswith('gemini-'):
        return 'google'
    else:
        raise ValueError(f'Unknown model name: {model_name}')


def main():
    load_dotenv()
    utils.setup_logging()
    args = parse_args()
    config = {
        'llm_type': get_llm_type(args.model),
        'openai': {
            'openai_api_key': os.getenv('OPENAI_API_KEY'),
        },
        'google': {
            'gemini_api_key': os.getenv('GEMINI_API_KEY'),
        },
        'anthropic': {
            'embedding_provider': 'openai',
            'anthropic_api_key': os.getenv('ANTHROPIC_API_KEY'),
        }
    }
    params = {
        'model_name': args.model,
        'vision_model': args.vision_model,
        'request_timeout': 120,
        'temperature': 0.2,
        'need_vision': True,
        'skip_callbacks': True,
    }
    embeddings = vs.get_embedding_model(config, None, None)
    vectorstore = vs.init_qdrant_langchain(embeddings, {})
    # Assign a collection (index) name
    vectorstore = copy_prepare_vectorstore(vectorstore, args.collection)
    init_llm = ira_llm.init_llm_class(config, params, None, None)
    llm = init_llm.init_llm(params)

    mode = args.mode
    if mode == 'index':
        path = args.path
        if not os.path.exists(path):
            raise FileNotFoundError(path)
        if os.path.isfile(path):
            files = [path]
        else:
            files = [os.path.join(path, f) for f in os.listdir(path)]

        for i, filename in enumerate(files):
            logger.info(f'Processing [{i + 1}/{len(files)}] {filename}')
            file_data = open(filename, 'rb').read()
            docs = extract_document_data_llm(
                os.path.basename(filename),
                file_data,
                config['llm_type'],
                llm,
                document_prompt,
            )
            ids = [utils.generate_unicode_uuid() for _ in docs]
            # Send ids because otherwise ids somehow have different values:
            # auto-generated (and returned) vs. inserted in vectorstore
            ids = vectorstore.add_documents(docs, ids=ids)
    elif mode == 'extract':
        kwargs = {
            'search_type': 'similarity',
            'search_kwargs': {'k': 64},
        }
        retriever = vectorstore.as_retriever(**kwargs)

        system_prompt = (
            "The context:"
            "\n\n{context}"
        )
        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", system_prompt),
                ("human", "{input}"),
            ]
        )

        # Can get JSON object right away
        # output_parser = output_parsers.JsonOutputParser()
        output_parser = None
        document_chain = create_stuff_documents_chain(
            llm,
            prompt,
            document_prompt=PromptTemplate.from_template("{page_content}"),
            output_parser=None
        )
        chain = create_retrieval_chain(retriever, document_chain)

        output = chain.invoke({"input": main_prompt})
        print(output['answer'])
    elif mode == 'clean':
        docs = vectorstore.similarity_search('', k=500)
        ids = [d.metadata['_id'] for d in docs]
        logger.info(f'Delete {len(ids)} document vectors')
        vectorstore.delete(ids)
        prepare_vectorstore(vectorstore, args.collection, index_recreate=True)


if __name__ == '__main__':
    main()
