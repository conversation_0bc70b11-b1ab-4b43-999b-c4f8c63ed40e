import argparse
import json

import pandas as pd
import pydantic
import tqdm
from langchain_core.prompts import PromptTemplate
from langchain_openai import OpenAIEmbeddings, ChatOpenAI

CATEGORIES = {
    'Orders': [
        'Order problem',
        'Order cancellation',
        'Order tracking',
    ],
    'Products': [
        'Other',
        'Stock availability',
        'Product return'
    ],
    'Support/Recall': [
        'Recall request',
        'Technical support'
    ],
    'Stock': [
        'Out of stock product'
    ],
    'Supplier/Wholesaler': [
        'Supplier problem',
        'Supplier stock',
        'Delivery times'
    ],
    # 'Other': ['Other']
}
prompt = PromptTemplate.from_template("""You are a data expert.
You are analysing user questions/reports and classifying them into one of the categories and subcategories.
If you can't tell what it is, put "Other" as category and subcategory.
Output should be in JSON format.
Try pick a reason why it was classified as such category (max 10-15 words).
The categories and their respectful subcategories are:
{categories}

User question/report:
--- 
{question}
---
""")


class Item(pydantic.BaseModel):
    category: str
    sub_category: str
    reason: str


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-f', '--file', required=True)
    parser.add_argument('-o', '--output', default='result.csv')
    parser.add_argument('-l', '--limit', default=-1, type=int)

    return parser.parse_args()


def format_categories(categories: dict[str, list]):
    lines = []
    for cat, subcat in categories.items():
        line = f'- {cat}: {json.dumps(subcat)}'
        lines.append(line)

    return '\n'.join(lines)


def read_file(path, csv_sep=','):
    if path.endswith('.xlsx'):
        data = pd.read_excel(path, header=None)
    elif path.endswith('.csv'):
        data = pd.read_csv(path, sep=csv_sep, header=None)
    else:
        raise ValueError(f'Format is not supported: {path}, supports only .csv and .xlsx')
    return data


def main():
    args = parse_args()
    data = read_file(args.file)
    limit = args.limit
    data_list = data[data.columns[0]].tolist()[:limit]

    embeddings = OpenAIEmbeddings()
    llm = ChatOpenAI(model_name='gpt-4o', temperature=0)
    llm = llm.with_structured_output(Item, method='json_mode')
    chain = prompt | llm

    results = []
    for q in tqdm.tqdm(data_list):
        response = chain.invoke({
            'categories': format_categories(CATEGORIES),
            'question': q,
        })
        results.append(response)

        pd.DataFrame(results).to_csv('temp.csv', index=False)

    result_df = pd.DataFrame(results)
    result_df.insert(0, 'question', data_list)

    result_df.to_csv(args.output, index=False)
    print(f"Saved to {args.output}.")


if __name__ == '__main__':
    main()
