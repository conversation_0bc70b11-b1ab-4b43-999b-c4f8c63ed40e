import argparse
import logging
import os

from llama_index.evaluation import DatasetGenerator, RelevancyEvaluator
from llama_index import (
    SimpleDirectoryReader,
    VectorStoreIndex,
    ServiceContext, PromptTemplate,
)
from llama_index.llms import OpenAI
from ira_chat.utils import utils
from llama_index.llms import openai_utils


GENERATION_PROMPT_FRENCH = """\
Les informations contextuelles sont ci-dessous.
---------------------
{context_str}
---------------------
Compte tenu des informations contextuelles et non des connaissances préalables, 
générer uniquement des questions basées sur la requête ci-dessous.
{query_str}
"""

QUESTION_GEN_QUERY_FRENCH = (
    "Vous êtes enseignant/professeur. Votre tâche consiste à configurer "
    "{num_questions_per_chunk} questions pour un quiz/examen à venir. "
    "Les questions doivent être de nature diverse dans tout le document."
    " Limitez les questions aux informations contextuelles fournies."
)


openai_utils.ALL_AVAILABLE_MODELS["gpt-4-1106-preview"] = 128000


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--dir', required=True)
    parser.add_argument('--evaluate', action='store_true')
    parser.add_argument('--questions', default='questions.txt')
    parser.add_argument('--answers', default='answers.txt')
    parser.add_argument('--limit', default=None, type=int)
    parser.add_argument('--offset', default=0, type=int)
    parser.add_argument('--language', default=None)
    parser.add_argument('--force-language', action='store_true')
    parser.add_argument('--model', default='gpt-4', choices=['gpt-4', 'gpt-3.5-turbo', 'gpt-4-1106-preview'])

    return parser.parse_args()


def main():
    args = parse_args()
    utils.setup_logging()
    logging.getLogger('openai').setLevel(logging.WARNING)

    reader = SimpleDirectoryReader(args.dir)
    documents = reader.load_data()
    q_exists = os.path.exists(args.questions)
    if not q_exists:
        logging.info('Generating questions...' + (f' (language={args.language})' if args.language else ''))
        question_template = None
        gen_query = None
        if args.language and args.language.lower() == 'french':
            generation_prompt = GENERATION_PROMPT_FRENCH
            if args.force_language:
                generation_prompt += '\nRéponse en français:'
            question_template = PromptTemplate(generation_prompt)
            gen_query = QUESTION_GEN_QUERY_FRENCH

        data_generator = DatasetGenerator.from_documents(
            documents,
            text_question_template=question_template,
            question_gen_query=gen_query
        )
        eval_questions = data_generator.generate_questions_from_nodes()
        open(args.questions, 'w').write('\n'.join(eval_questions))
        logging.info(f'Questions are saved to {args.questions}.')
    else:
        logging.info(f'Read existing questions from {args.questions}')
        eval_questions = open(args.questions).read().split('\n')

    if args.evaluate:
        logging.info('Evaluate...')
        # gpt-4
        gpt4 = OpenAI(temperature=0, model=args.model, additional_kwargs={'timeout': 60})
        service_context_gpt4 = ServiceContext.from_defaults(llm=gpt4)
        evaluator_gpt4 = RelevancyEvaluator(service_context=service_context_gpt4)
        # create vector index
        vector_index = VectorStoreIndex.from_documents(
            documents, service_context=service_context_gpt4
        )
        query_engine = vector_index.as_query_engine()
        accuracy = 0
        offset = args.offset
        num_questions = args.limit or len(eval_questions) - offset
        answers = []
        logging.info(f"Will save all the answers into {args.answers} file.")
        for i, question in enumerate(eval_questions[offset:offset+num_questions]):
            if question.strip() == '':
                continue
            if args.force_language and args.language.lower() == 'french':
                question += '\nRéponse en français:'
            response_vector = query_engine.query(question)
            eval_result = evaluator_gpt4.evaluate_response(
                query=question, response=response_vector
            )
            print(f'{i+1+offset}. Question: {question}')
            print(f'{i+1+offset}. Eval result: {eval_result.feedback}')
            print(f'{i+1+offset}. Answer: {eval_result.response}\n')
            accuracy += 1 if eval_result.passing else 0
            answers.append(eval_result.response)

        print(f'Accuracy: {accuracy/len(eval_questions[:num_questions])}')

        with open(args.answers, 'w') as f:
            f.write('\n'.join(answers))


if __name__ == '__main__':
    main()



