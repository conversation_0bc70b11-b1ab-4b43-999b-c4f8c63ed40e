import asyncio
import pydantic
import pandas as pd
import tqdm
from langchain_openai import ChatOpenAI


prompt = """
Look at the question, analyze it and say a topic of that question. 
Mark the question need_clarify if it is not clear for the answer retrieval.
Output in JSON format reasoning, topic (must consist maximum of 2-3 words) and need_clarify:
{{
  "reasoning": "",
  "topic": "",
  "need_clarify": true
}}

Given question: {question}
"""


class Topic(pydantic.BaseModel):
    reasoning: str
    topic: str
    need_clarify: bool


async def main():
    question_df = pd.read_csv('wap.csv')
    questions = question_df['question'].tolist()
    llm = ChatOpenAI(model_name='gpt-4.1-mini', temperature=0.5)
    structured_llm = llm.with_structured_output(Topic, method='json_mode')
    pbar = tqdm.tqdm(total=len(questions))

    async def inner(question):
        all_messages = [('system', prompt.format(question=question))]
        t = await structured_llm.ainvoke(all_messages)
        pbar.update(1)
        return t

    topics = await asyncio.gather(*[inner(question) for question in questions])
    topics_list = []
    for i, topic in enumerate(topics):
        row = {
            'question': questions[i],
            'topic': topic.topic,
            'reasoning': topic.reasoning,
            'need_clarify': topic.need_clarify,
        }
        topics_list.append(row)

    topic_df = pd.DataFrame(topics_list)
    topic_df.to_csv('topics_wap.csv', index=False)


if __name__ == '__main__':
    asyncio.run(main())