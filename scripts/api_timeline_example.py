import argparse
import datetime
import json
import os.path
import random
import time

import requests


HEADERS = {}
API_URL = 'https://winpharma.hotline.kibernetika.io'


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-k', '--key')
    parser.add_argument('-u', '--url', default=API_URL)
    parser.add_argument('--llm', default='openai')
    parser.add_argument('--schema', required=True)
    parser.add_argument('--data1', required=True)
    parser.add_argument('--data2', required=True)
    parser.add_argument('--delete', action='store_true', help='Delete all after completion')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose mode')

    return parser.parse_args()


class APIClient:
    def __init__(self, api_key: str, api_url: str, verbose=False):
        self.base_url = api_url
        self.key = api_key
        self.headers = {'Authorization': f'Bearer {self.key}'}
        self.session = requests.Session()
        self.session.headers = self.headers

        self.ws = self.detect_workspace()
        self.ws_id = self.ws['id']
        if verbose:
            print(f'Working in workspace [id={self.ws_id}, name={self.ws["name"]}]')

    def detect_workspace(self):
        resp = self.session.get(self._url('/workspaces'))
        resp.raise_for_status()
        resp = resp.json()
        return resp['workspaces'][0]

    def _url(self, u):
        return os.path.join(self.base_url, 'api/v1', u.lstrip("/"))

    def _ws_url(self, u):
        return self._url(os.path.join(f'workspaces/{self.ws_id}', u.lstrip('/')))

    def auth_info(self):
        resp = self.session.get(self._url('/auth/info'))
        return resp.json()

    def create_timeline(self, title, data_schema):
        data = {
            'title': title,
            'data_schema': data_schema,
        }
        resp = self.session.post(self._ws_url(f'/timelines'), json=data)
        resp.raise_for_status()
        return resp.json()

    def get_timeline(self, id: int):
        resp = self.session.get(self._ws_url(f'/timelines/{id}'))
        resp.raise_for_status()
        return resp.json()

    def get_timeline_historical_result(self, id: int):
        resp = self.session.get(self._ws_url(f'/timelines/{id}/historical_result'))
        resp.raise_for_status()
        return resp.json()

    def delete_timeline(self, id: int):
        resp = self.session.delete(self._ws_url(f'/timelines/{id}'))
        resp.raise_for_status()
        return resp

    def create_point(self, timeline_id: int, title: str, date: str):
        data = {
            'title': title,
            'date': date,
        }
        resp = self.session.post(self._ws_url(f'/timelines/{timeline_id}/points'), json=data)
        resp.raise_for_status()
        return resp.json()

    def get_point(self, timeline_id: int, point_id: int):
        resp = self.session.get(self._ws_url(f'/timelines/{timeline_id}/points/{point_id}'))
        resp.raise_for_status()
        return resp.json()

    def add_point_change(self, timeline_id: int, point_id: int, data: dict, description: str = None):
        data = {
            'description': description,
            'data': data,
        }
        resp = self.session.post(self._ws_url(f'/timelines/{timeline_id}/points/{point_id}/changes'), json=data)
        resp.raise_for_status()
        return resp.json()

    def process_timeline(self, timeline_id: int):
        resp = self.session.post(self._ws_url(f'/timelines/{timeline_id}/process'))
        resp.raise_for_status()
        return resp.json()

    def wait_point_process(self, timeline_id: int, point_id: int, timeout: int = 120):
        point = self.get_point(timeline_id, point_id)
        time.sleep(1)
        t = time.time()
        while point['status']['status'] in ['PREPROCESSING', 'PROCESSING']:
            time.sleep(1)
            point = self.get_point(timeline_id, point_id)
            if time.time() - t > timeout:
                raise TimeoutError('Timeout exceeded while waiting for point processing')

        return point


def main():
    args = parse_args()
    verbose = args.verbose
    client = APIClient(args.key, args.url, verbose=True)

    schema = json.loads(open(args.schema).read())
    data1 = json.loads(open(args.data1).read())
    data2 = json.loads(open(args.data2).read())

    if verbose:
        print(f'Creating a new timeline "test-timeline"')
    timeline = client.create_timeline('test-timeline', schema)
    if verbose:
        print(f'New timeline id = {timeline["id"]}')
    random_date = datetime.datetime.now().date() - datetime.timedelta(days=random.randint(100, 200))
    next_date = random_date + datetime.timedelta(days=random.randint(30, 150))

    if verbose:
        print(f'Creating a new point "one" date={random_date.isoformat()}')
    point1 = client.create_point(timeline['id'], 'one', random_date.isoformat())

    if verbose:
        print(f'Creating a new point change')
    change_point1 = client.add_point_change(timeline['id'], point1['id'], data1, '1 change')

    if verbose:
        print(f'Creating a new point "two" date={next_date.isoformat()}')
    point2 = client.create_point(timeline['id'], 'two', next_date.isoformat())

    if verbose:
        print(f'Creating a new point change')
    change_point2 = client.add_point_change(timeline['id'], point2['id'], data2, '2 change')

    last_point = client.process_timeline(timeline['id'])
    if verbose:
        print(f'Waiting point {last_point["title"]} to complete processing')
    client.wait_point_process(timeline['id'], last_point['id'])

    if verbose:
        print('Done waiting.')

    print('Output 1:')
    print(json.dumps(client.get_point(timeline['id'], point1['id'])['output'], indent=2))

    print('Output 2:')
    print(json.dumps(client.get_point(timeline['id'], point2['id'])['output'], indent=2))

    print('Historical result:')
    print(json.dumps(client.get_timeline_historical_result(timeline['id']), indent=2))

    if args.delete:
        print(f'Cleanup, deleting timeline [id={timeline["id"]}]')
        client.delete_timeline(timeline['id'])


if __name__ == '__main__':
    main()
