import argparse
import json
import mimetypes
import os.path
import re
import time
import requests


HEADERS = {}
API_URL = 'https://winpharma.hotline.kibernetika.io/api/v1'
detection_search_title = 'testAPI'


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-d', '--detection', default=detection_search_title)
    parser.add_argument('-k', '--key')
    parser.add_argument('-f', '--file', required=True)
    parser.add_argument('-u', '--url', default=API_URL)
    parser.add_argument('--llm', default='openai')
    parser.add_argument('--delete', action='store_true', help='Delete detection item after completion')
    parser.add_argument('--json', action='store_true', help='Try to find and parse JSON string in an output')
    parser.add_argument('-q', '--quiet', action='store_true', help='Silent mode, only JSON output')

    return parser.parse_args()


def get_detection_by_title(title):
    resp = requests.get(
        f'{API_URL}/detections',
        headers=HEADERS
    )
    if resp.status_code >= 400:
        raise ValueError(resp.content)

    detections = resp.json()
    detections = [d for d in detections if d['title'] == title]
    if len(detections) < 1:
        raise ValueError(f'Detection for title {title} not found')

    detection = detections[0]
    return detection


def create_detection_item_send_image(detection_id, image_file, config=None):
    filename = os.path.basename(image_file)
    resp = requests.post(
        f'{API_URL}/detections/{detection_id}/items',
        headers=HEADERS,
        files={'file': (filename, open(image_file, 'rb'), mimetypes.guess_type(image_file)[0])},
        data={'config': json.dumps(config)},
    )
    if resp.status_code >= 400:
        raise ValueError(resp.content)
    detection_item = resp.json()
    return detection_item


def wait_for_completion(detection_item, timeout=120):
    url = (
        f'{API_URL}/detections/'
        f'{detection_item["detection_id"]}/items/{detection_item["id"]}'
    )
    completed = False
    start = time.time()
    while not completed:
        resp = requests.get(url, headers=HEADERS)
        if resp.status_code >= 400:
            raise ValueError(resp.content)

        detection_item = resp.json()
        status = detection_item["status"]
        completed = status in {'SUCCESS', 'ERROR'}
        if time.time() - start > timeout:
            raise TimeoutError(f"Timed out waiting for the detection item id={detection_item['id']}")
        time.sleep(1)
    return detection_item


def delete_detection_item(detection_item):
    url = (
        f'{API_URL}/detections/'
        f'{detection_item["detection_id"]}/items/{detection_item["id"]}'
    )
    resp = requests.delete(url, headers=HEADERS)
    if resp.status_code >= 400:
        raise ValueError(resp.content)


def main():
    args = parse_args()

    global API_KEY, API_URL, HEADERS
    API_KEY = args.key
    API_URL = args.url
    HEADERS = {'Authorization': f'Bearer {API_KEY}'}
    config = {'llm_type': args.llm}
    verbose = not args.quiet

    # Get detections list and search by title.
    # If you have a detection id - then proceed to send an image to that detection.
    if verbose:
        print('search detection')
    detection = get_detection_by_title(args.detection)

    # Send an image to detection for recognition
    if verbose:
        print('send image')
    detection_item = create_detection_item_send_image(detection['id'], args.file, config)

    if verbose:
        print('wait')
    # Wait recognition process to complete
    detection_item = wait_for_completion(detection_item)

    if detection_item['status'] == 'ERROR':
        print(detection_item['results'][0]['output'])
        exit(1)
    else:
        output = detection_item['results'][0]['output']
        # Just print the output
        # print(output)

        # Optionally extract/print formatted JSON with regexp
        if args.json:
            if verbose:
                print('look for JSON...')
            try_json = re.findall('{.*}', output, re.DOTALL)
            if not try_json:
                print('Error: JSON object not found, the output:')
                print(output)
            else:
                print(try_json[0])
        else:
            print(output)
        # Another way, just take string between first and last brace
        # print(output[output.find('{'):output.rfind('}')+1])

    if args.delete:
        delete_detection_item(detection_item)
        if verbose:
            print(f'detection item id={detection_item["id"]} deleted')


if __name__ == '__main__':
    main()
