import asyncio
import logging
import tqdm

import sqlalchemy as sa

from ira_chat.db import api as db_api
from ira_chat.db import base
from ira_chat.db import models
from ira_chat.utils import utils
from ira_chat.utils import metric_utils

logger = logging.getLogger(__name__)


async def main():
    async with base.session_context() as session:
        select_metrics = sa.select(
            models.Metric.object_id, models.Metric.app_id, models.Metric.workspace_id, models.Metric.app_type, models.Metric.org_id
        ).distinct().where(models.Metric.app_type == models.APP_TYPE_CHAT).where(models.Metric.type == models.METRIC_RESPONSE_TIME)
        select_metrics = select_metrics.where(models.Metric.object_id != '0').where(models.Metric.object_id.is_not(None))
        all_metrics = await session.execute(select_metrics)

        all_metrics = all_metrics.fetchall()

        for metric in tqdm.tqdm(all_metrics):
            object_id, app_id, workspace_id, app_type, org_id = metric
            if object_id == 'None':
                continue
            await metric_utils.squash_metrics(
                org_id, workspace_id, app_id, app_type, object_id,
                pick_metric=models.METRIC_RESPONSE_TIME,
            )


if __name__ == '__main__':
    utils.setup_logging()
    asyncio.run(main())
