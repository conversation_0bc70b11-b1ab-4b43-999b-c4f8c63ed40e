import pandas as pd


def main():
    # <PERSON>ript is used to refine winpharma chats report data
    # into a dataset with columns: thread_id, question, answer, question_by, answer_by
    data = pd.read_csv('report_006.csv')
    threads = data['thread_id'].unique()
    raw_dataset = []
    for thread_id in threads:
        thread_data = data[data['thread_id'] == thread_id]
        thread_data = thread_data.sort_values(by='PostingId').reset_index(drop=True)
        question = thread_data.iloc[0]['content_text']
        for i in range(1, len(thread_data)):
            message = thread_data.iloc[i]
            text = message['content_text']
            if message['Author'] != thread_data.iloc[i - 1]['Author']:
                raw_dataset.append({
                    'thread_id': thread_id,
                    'question': question,
                    'answer': text,
                    'question_by': thread_data.iloc[i - 1]['Author'],
                    'answer_by': message['Author'],
                })
                question = question + '\n\n' + text
            else:
                question = question + '\n\n' + text

    df_dataset = pd.DataFrame(raw_dataset)
    df_dataset.to_csv('refined_report_006.csv', index=False)


if __name__ == '__main__':
    main()
