import argparse
import json
from io import BytesIO

from langchain_core.outputs import ChatResult
from langchain_openai import ChatOpenAI

from ira_chat.services.llms.base import BatchLLM
from ira_chat.utils import utils


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-q', '--query', action='append')
    parser.add_argument('-m', '--mode', default='request', choices=['request', 'status'])
    parser.add_argument('-id', '--id')
    parser.add_argument('-d', '--delete', action='store_true')

    return parser.parse_args()


def main():
    utils.setup_logging()
    args = parse_args()
    mode = args.mode

    llm = ChatOpenAI(model_name='gpt-4o')
    batch_llm = BatchLLM(llm)

    if mode == 'request':
        batch = batch_llm.batch(args.query)
        print(f'New batch id: {batch.id}')
        print(f'New batch status: {batch.status}')
    else:
        status = batch_llm.status(args.id)

        print(f'Batch status: {status}')
        if status not in ["failed", "completed", "expired", "cancelled"]:
            return

        results = batch_llm.retrieve(args.id, cleanup_files=args.delete)
        [print(result) for result in results]


if __name__ == '__main__':
    main()
