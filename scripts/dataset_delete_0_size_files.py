
import argparse
import asyncio

from ira_chat.client.api_client import APIClient


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-k', '--key')
    parser.add_argument('-u', '--url', default='https://winpharma.hotline.kibernetika.io')
    parser.add_argument('-n', '--dataset-name')
    parser.add_argument('-id', '--dataset-id')

    return parser.parse_args()


async def main():
    args = parse_args()
    # 1. Get api client
    client = APIClient(args.key, args.url)
    limit = 10
    found = True
    while found:
        files = client.dataset_file_list(args.dataset_id, limit=limit, page=1, order='size', desc=False)['items']
        for file in files:
            if file['size'] > 0:
                found = False
                break
            print(f'Deleting {file["name"]}, size={file["size"]}')
            client.dataset_file_delete(args.dataset_id, file['id'])


if __name__ == '__main__':
    asyncio.run(main())
