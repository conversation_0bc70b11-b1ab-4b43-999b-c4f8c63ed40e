import argparse
import logging
import shutil

from ira_chat.services.detections import posology, barcode
from ira_chat.utils import utils


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('file')
    parser.add_argument('-o', '--output', default='section_extract.zip')
    parser.add_argument('-s', '--start', type=int)
    parser.add_argument('-e', '--end', type=int)
    return parser.parse_args()


def main():
    utils.setup_logging()
    args = parse_args()
    tempdir, path = posology.save_to_tmp_dir(args.file)
    try:
        text = barcode.extract_barcodes(
            path,
            args.output,
            image_dpi=324,
            impl='opencv',
            extract_all_images=True,
            page_start=args.start,
            page_end=args.end,
        )
    finally:
        shutil.rmtree(tempdir)

    print(text)
    # output_path = "section_extract.md"
    # with open(output_path, "w", encoding="utf-8") as f:
    #     f.write(section_4_2_text)

    # print(f"Section extracted and saved to {args.output}")


if __name__ == '__main__':
    main()
