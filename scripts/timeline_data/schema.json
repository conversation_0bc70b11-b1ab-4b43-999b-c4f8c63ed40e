{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"Legal Corporate Documents": {"type": "object", "properties": {"Filed Certificate of Incorporation": {"type": "object", "properties": {"Company Name": {"type": "string"}, "Legal entity type": {"type": "string"}, "State of registration": {"type": "string"}, "File Number": {"type": "string"}, "Filed Date": {"type": "string"}, "Registered office address": {"type": "string"}, "Registered agent name": {"type": "string"}, "Number of authorized shares": {"type": "integer"}, "Par value per share": {"type": "string"}}, "required": ["Company Name", "Legal entity type", "State of registration", "File Number", "Filed Date", "Registered office address", "Registered agent name", "Number of authorized shares", "Par value per share"]}, "Action by Incorporator - Appointing Initial Directors": {"type": "object", "properties": {"List of initial directors": {"type": "array", "items": {"type": "object", "properties": {"Name": {"type": "string"}, "Address": {"type": "string"}}, "required": ["Name", "Address"]}}, "Incorporator": {"type": "object", "properties": {"Name": {"type": "string"}, "Address": {"type": "string"}}, "required": ["Name", "Address"]}}, "required": ["List of initial directors", "Incorporator"]}, "Action by Anonymous Written Consent of the Board of Directors": {"type": "object", "properties": {"Corporate Officers": {"type": "object", "properties": {"President Name": {"type": "string"}, "Treasurer Name": {"type": "string"}, "Secretary Name": {"type": "string"}}, "required": ["President Name", "Treasurer Name", "Secretary Name"]}, "Stock issuance": {"type": "array", "items": {"type": "object", "properties": {"Name": {"type": "string"}, "Amount of Stock": {"type": "integer"}, "Purchase price": {"type": "string"}}, "required": ["Name", "Amount of Stock", "Purchase price"]}}}, "required": ["Corporate Officers", "Stock issuance"]}, "Bylaws": {"type": "object", "properties": {"Board seats number": {"type": "integer"}}, "required": ["Board seats number"]}, "Form SS-4": {"type": "object", "properties": {"Mailing Address": {"type": "string"}, "Legal type": {"type": "string"}, "FEY (Closing month of accounting year)": {"type": "string"}, "Principal activity": {"type": "string"}, "Products or services sold": {"type": "string"}, "Tax ID / EIN (Employer Identification Number)": {"type": "string"}}, "required": ["Mailing Address", "Legal type", "FEY (Closing month of accounting year)", "Principal activity", "Products or services sold", "Tax ID / EIN (Employer Identification Number)"]}, "CP575 - Reply to SS-4 - Letter from IRS assigning Tax ID and reporting form": {"type": "object"}, "147c Confirmation of Tax ID number": {"type": "object"}}, "required": ["Filed Certificate of Incorporation", "Action by Incorporator - Appointing Initial Directors", "Action by Anonymous Written Consent of the Board of Directors", "Bylaws", "Form SS-4", "CP575 - Reply to SS-4 - Letter from IRS assigning Tax ID and reporting form", "147c Confirmation of Tax ID number"]}, "Personal Documents (required from Directors and major Shareholders)": {"type": "object", "properties": {"Passport": {"type": "array", "items": {"type": "object", "properties": {"Name": {"type": "string"}, "Passport No.": {"type": "string"}, "Nationality": {"type": "string"}, "Date of birth": {"type": "string"}, "Date of issue": {"type": "string"}, "Date of expiry": {"type": "string"}}, "required": ["Name", "Passport No.", "Nationality", "Date of birth", "Date of issue", "Date of expiry"]}}, "Residency permit": {"type": "object"}, "Verification of Address": {"type": "object", "properties": {"Sublease": {"type": "object"}, "Utility Bill": {"type": "object"}}, "required": ["Sublease", "Utility Bill"]}, "SSN Card": {"type": "object"}}, "required": ["Passport", "Residency permit", "Verification of Address", "SSN Card"]}}, "required": ["Legal Corporate Documents", "Personal Documents (required from Directors and major Shareholders)"]}