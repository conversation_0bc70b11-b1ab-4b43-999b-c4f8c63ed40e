from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter
import os
import argparse


def parse_args():
    parser = argparse.ArgumentParser(description="Split PDF into multiple PDFs")
    parser.add_argument("input_pdf", type=str, help="Path to input PDF")
    parser.add_argument("-o", "--output-dir", type=str, help="Path to output directory")
    parser.add_argument("-p", "--pages", type=int, help="Number of pages per output PDF", default=10)
    return parser.parse_args()


def split_pdf(input_pdf, output_dir, pages_per_file=10, file_template='{input_pdf}_{start}_{end}.pdf'):
    reader = PdfReader(input_pdf)
    input_file_base = os.path.basename(input_pdf)
    input_file_no_ext = os.path.splitext(input_file_base)[0]
    total_pages = len(reader.pages)
    for start_page in range(0, total_pages, pages_per_file):
        output_pdf = PdfWriter()
        for i in range(start_page, min(start_page + pages_per_file, total_pages)):
            output_pdf.add_page(reader.pages[i])
        end = min(start_page + pages_per_file, total_pages)
        start = start_page
        output_pdf_path = os.path.join(
            output_dir, file_template.format(input_pdf=input_file_no_ext, start=start, end=end, output_dir=output_dir)
        )
        with open(output_pdf_path, "wb") as output_pdf_file:
            output_pdf.write(output_pdf_file)
        yield output_pdf_path


def main():
    args = parse_args()
    os.makedirs(args.output_dir, exist_ok=True)
    for output_pdf_path in split_pdf(args.input_pdf, args.output_dir, args.pages):
        print(f"Created: {output_pdf_path}")


if __name__ == "__main__":
    main()
