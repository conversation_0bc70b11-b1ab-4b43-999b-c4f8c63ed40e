import argparse
import asyncio
import datetime
import io
import logging

import pandas as pd

from ira_chat.client.api_client import APIClient

logger = logging.getLogger(__name__)

API_URL = 'https://winpharma.hotline.kibernetika.io'


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-k', '--key')
    parser.add_argument('-u', '--url', default=API_URL)
    parser.add_argument('-d', '--days', default=7, type=int, help='Number of days to grade in the past')

    return parser.parse_args()


async def main():
    args = parse_args()
    client = APIClient(args.key, args.url, is_workspace_token=True)

    end_date = datetime.datetime.now(datetime.UTC)
    start_date = end_date - datetime.timedelta(days=args.days)

    print(f'Loading history for {args.days} days back')
    history = await client.adownload_history(
        workspace_id=client.ws_id, include_metrics=False, include_context=False, include_urls=False, format='csv',
        start_date=start_date, end_date=end_date,
    )
    buf = io.BytesIO(history)
    df = pd.read_csv(buf)

    # Corrections
    selectors = [
        (df.tags == 'Not WAP') & (df.grade_selector == 'Excluded answer'),
        (df.tags == 'Not WAP') & (df.grade_selector == 'No answer'),
    ]
    corrected_rows = 0
    for selector in selectors:
        selected = df[selector]
        corrected_rows += len(selected)

        df.loc[selector, 'grade_selector'] = 'Correct answer'

    print(f'Corrected {corrected_rows} rows')

    if corrected_rows == 0:
        print('No rows to correct, skip uploading')
        return

    output_buf = io.StringIO()
    df.to_csv(output_buf, index=False)
    output_buf.seek(0)
    print('Uploading history')
    await client.aimport_history(client.ws_id, output_buf.getvalue().encode(), format='csv')
    print('Done')


if __name__ == '__main__':
    asyncio.run(main())
