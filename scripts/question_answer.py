import argparse
import asyncio
import json
import logging
import operator
import os.path
import time

import chardet
import pandas as pd
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from ragas import evaluate, EvaluationDataset, RunConfig
from ragas.metrics import answer_correctness, context_precision, context_recall

from ira_chat.client.api_client import APIClient

logger = logging.getLogger(__name__)

API_URL = 'https://winpharma.hotline.kibernetika.io'


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-k', '--key')
    parser.add_argument('-u', '--url', default=API_URL)
    parser.add_argument('-ws', '--workspace-id')
    parser.add_argument('--llm', default='openai')
    parser.add_argument('-q', '--questions', required=True, help='path to questions file (.txt, .csv, .xlsx)')
    parser.add_argument('--output', default='answers.csv', help='path to answers file (.csv, .xlsx)')
    parser.add_argument('--delete', action='store_true', help='Delete chat after completion')
    parser.add_argument('--chat-prefix', help='Prefix for chat name')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose mode')
    parser.add_argument('--csv-sep', default=',', help='CSV separator symbol')
    parser.add_argument('--q-column', default=None, help='Column name for questions')
    parser.add_argument('--ref-column', default=None, help='Column name for reference answer (ground truth)')
    parser.add_argument('-c', '--concurrency', default=4, type=int)
    parser.add_argument('--limit', type=int, help='Limit number of questions to answer')
    parser.add_argument('--mode', default='process', choices=['process', 'evaluate'])

    return parser.parse_args()


def single_message_chat(client: APIClient, chat_name: str, message: str, delete=True):
    chat = client.chat_create(chat_name)
    msg = client.message_create(chat['id'], message)
    result_id = msg['results'][-1]['id']
    msg = client.message_wait_for_completion(msg, result_id, timeout=360)
    if delete:
        client.chat_delete(msg['chat_id'])
    return msg


async def asingle_message_chat(client: APIClient, chat_name: str, message: str, delete=True):
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, single_message_chat, client, chat_name, message, delete)


async def worker(queue, result_q):
    i = 0
    while True:
        try:
            i, *args = await queue.get()
            print(f'Processing {i}')
            q = args[2]
            if q is None or not isinstance(q, str) or not q.strip():
                result = {'results': [{'content': ''}]}
            else:
                result = await asingle_message_chat(*args)
                # await asyncio.sleep(1); result = f'Q{i} {args[-1]}'

                # async with lock:
                #     with open('answers.txt', 'a') as f:
                #         f.write(result['results'][-1].get('content', 'N/A'))
                #         f.write('\n\n\n\n')

            print(f'Done {i}')
            await result_q.put((i, result))
        except Exception as e:
            print(i, args[2], e)
            logger.exception(f'Error processing question {i}: {str(e)}')
            await result_q.put((i, {'results': [{'content': f'ERROR: {str(e)}'}]}))
        finally:
            queue.task_done()


async def process_all(client, questions, concurrency: int, delete=True, chat_prefix='question'):
    chat_prefix = chat_prefix or 'question'
    loop = asyncio.get_event_loop()
    queue = asyncio.Queue()
    result_q = asyncio.Queue()
    workers = [loop.create_task(worker(queue, result_q)) for _ in range(concurrency)]
    for i, q in enumerate(questions):
        await queue.put((i, client, f'{chat_prefix}-{i}', q, delete))
        i += 1
    await queue.join()  # wait for all tasks to be processed
    # Stop workers
    for w in workers:
        w.cancel()
    await asyncio.gather(*workers, return_exceptions=True)

    # Gather results
    results = []
    for _ in questions:
        results.append(await result_q.get())
    results = sorted(results, key=operator.itemgetter(0))
    results = [r[1] for r in results]
    return results


async def main():
    args = parse_args()
    verbose = args.verbose
    client = APIClient(args.key, args.url, is_workspace_token=False, verbose=verbose)
    if args.workspace_id:
        client.set_workspace(args.workspace_id)
    else:
        ws = client.detect_workspace()
        client.set_workspace(ws['id'])

    q_file = args.questions
    output_file = args.output

    if q_file.endswith('.xlsx'):
        questions_data = pd.read_excel(q_file, header=None if not args.q_column else 0)
    elif q_file.endswith('.csv'):
        with open(q_file, 'rb') as f:
            encoding = chardet.detect(f.read())['encoding']
        questions_data = pd.read_csv(q_file, sep=args.csv_sep, header=None if not args.q_column else 0, encoding=encoding)
    else:
        raise ValueError(f'Format is not supported: {q_file}, supports only .csv and .xlsx')

    if args.limit:
        questions_data = questions_data[:args.limit]

    if args.q_column:
        questions = questions_data[args.q_column].tolist()
    else:
        questions = questions_data[questions_data.columns[0]].tolist()

    # replace all _x000D_ with \n
    questions = [str(q).replace('_x000D_', '\n') for q in questions]

    mode = args.mode
    t = time.time()
    if mode == "process":
        results = await process_all(client, questions, args.concurrency, delete=args.delete, chat_prefix=args.chat_prefix)
        # Write raw in case of errors
        with open(os.path.basename(output_file) + '_raw.json', 'w') as f:
            f.write(json.dumps(results, indent=2))

        print(f'Took {time.time() - t:.1f}s to answer {len(questions)} questions')

        answer_data = pd.DataFrame({
            'question': [questions[i] for i, r in enumerate(results)],
            'answer': [r['results'][-1]['content'] for r in results],
            'ground_truth': questions_data[args.ref_column][:len(results)] if args.ref_column else None,
            'context': [
                json.dumps(r['results'][-1]['context']['source_documents']) if r['results'][-1].get('context')
                else [] for r in results
            ],
        })
        if output_file.endswith('.xlsx'):
            answer_data.to_excel(output_file, index=False)
        elif output_file.endswith('.csv'):
            answer_data.to_csv(output_file, index=False)
        else:
            raise ValueError(f'Format is not supported: {output_file}, supports only .csv and .xlsx')

        print(f'Answers saved to {output_file}')
    elif mode == "evaluate":
        if not args.ref_column:
            raise ValueError('Reference column is required for evaluation')
        if output_file.endswith('.xlsx'):
            answer_data = pd.read_excel(output_file)
        elif output_file.endswith('.csv'):
            answer_data = pd.read_csv(output_file, sep=args.csv_sep)
        else:
            raise ValueError(f'Format is not supported: {output_file}, supports only .csv and .xlsx')

        if args.limit:
            answer_data = answer_data[:args.limit]

        answer_data['reference'] = questions_data[args.ref_column]

        metrics_to_evaluate = [
            # answer_relevancy,
            answer_correctness,
            # answer_similarity,
            context_precision,
            context_recall,
            # ContextUtilization(),
        ]
        df = answer_data.rename(
            columns={
                'question': 'user_input',
                'answer': 'response',
                'context': 'retrieved_contexts',
            }
        )
        # Preprocess retrieved_contexts
        df['retrieved_contexts'] = df['retrieved_contexts'].apply(json.loads)
        df.loc[:, 'retrieved_contexts'] = df['retrieved_contexts'].apply(
            lambda x: [d['page_content'] for d in x]
        )
        ragas_dataset = EvaluationDataset.from_pandas(
            df[['user_input', 'response', 'reference', 'retrieved_contexts']]
        )
        llm = ChatOpenAI(model="gpt-4.1-mini", temperature=0.5)
        embeddings = OpenAIEmbeddings()

        results = await evaluate(
            ragas_dataset,
            metrics=metrics_to_evaluate,
            llm=llm,
            embeddings=embeddings,
            run_config=RunConfig(max_workers=16),
        )
        print(f'Took {time.time() - t:.1f}s to evaluate {len(questions)} questions')
        print(results)
        scores_df = results.to_pandas()

        output_path = os.path.splitext(output_file)[0] + '_metrics.csv'
        scores_df.to_csv(output_path, index=False)
        print(f'Metrics saved to {output_path}')


if __name__ == '__main__':
    asyncio.run(main())
