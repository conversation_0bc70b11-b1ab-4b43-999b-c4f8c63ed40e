from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime
from enum import Enum


class CompanyType(Enum):
    LLC = "LLC"
    C_CORP = "C Corp"
    S_CORP = "S Corp"


class State(Enum):
    AL = "Alabama"
    AK = "Alaska"
    AZ = "Arizona"
    AR = "Arkansas"
    CA = "California"
    CO = "Colorado"
    CT = "Connecticut"
    DE = "Delaware"
    DC = "District Of Columbia"
    FL = "Florida"
    GA = "Georgia"
    HI = "Hawaii"
    ID = "Idaho"
    IL = "Illinois"
    IN = "Indiana"
    IA = "Iowa"
    KS = "Kansas"
    KY = "Kentucky"
    LA = "Louisiana"
    ME = "Maine"
    MD = "Maryland"
    MA = "Massachusetts"
    MI = "Michigan"
    MN = "Minnesota"
    MS = "Mississippi"
    MO = "Missouri"
    MT = "Montana"
    NE = "Nebraska"
    NV = "Nevada"
    NH = "New Hampshire"
    NJ = "New Jersey"
    NM = "New Mexico"
    NY = "New York"
    NC = "North Carolina"
    ND = "North Dakota"
    OH = "Ohio"
    OK = "Oklahoma"
    OR = "Oregon"
    PA = "Pennsylvania"
    RI = "Rhode Island"
    SC = "South Carolina"
    SD = "South Dakota"
    TN = "Tennessee"
    TX = "Texas"
    UT = "Utah"
    VT = "Vermont"
    VA = "Virginia"
    WA = "Washington"
    WV = "West Virginia"
    WI = "Wisconsin"
    WY = "Wyoming"


class CorporateOfficer(Enum):
    PRESIDENT = "President"
    TREASURER = "Treasurer"
    SECRETARY = "Secretary"


class AccountingMethod(Enum):
    CASH = "Cash"
    ACCRUAL = "Accrual"


class PersonalDocuments(BaseModel):
    id: int = Field(..., primary_key=True, description="Unique identifier for the personal document")
    person_id: int = Field(..., description="Unique identifier for the person")
    document_name: Optional[str] = Field(None, description="Name of the personal document")
    document_date: Optional[datetime] = Field(None, description="Date of the personal document")
    document_type: Optional[str] = Field(None, description="Type of the personal document")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")


class Person(BaseModel):
    id: int = Field(..., primary_key=True, description="Unique identifier for the person")
    name: Optional[str] = Field(None, description="Name of the person")
    documents: Optional[list[PersonalDocuments]] = Field(None, description="List of personal documents")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")


class CorporateDocuments(BaseModel):
    id: int = Field(..., primary_key=True, description="Unique identifier for the corporate document")
    company_id: int = Field(..., description="Unique identifier for the company")
    document_name: Optional[str] = Field(None, description="Name of the corporate document")
    document_date: Optional[datetime] = Field(None, description="Date of the corporate document")
    document_type: Optional[str] = Field(None, description="Type of the corporate document")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")


class Company(BaseModel):
    id: int = Field(..., primary_key=True, description="Unique identifier for the company")
    company_name: str = Field(..., description="Name of the company")
    incorporation_state: State = Field(..., description="US State the company is registerd in")
    other_state_registration: Optional[bool] = Field(..., description="Is the company registered in another state")
    file_date: datetime.date = Field(..., description="FILED date for the company incorporation certificate")
    file_number: int = Field(..., description="File Number for the company registration certificvate")
    company_type: CompanyType = Field(..., description="Type of the company (e.g. LLC, C Corp, S Corp.)")
    incorporator_name: Optional[str] = Field(None, description="Name of the company incorporator")
    incorporator_address: Optional[str] = Field(None, description="Address of the company incorporator")
    total_authorized_shares: Optional[int] = Field(None,
                                                   description="Total number of shares authorized by the company")  # from certificate of incorporation
    total_issued_shares: Optional[int] = Field(None,
                                               description="Total number of shares issued by the company")  # excluding SAFE/Options - need clarification
    shares_par_value: Optional[float] = Field(None,
                                              description="Par Value of the shares authorized to be issued")  # from certificate of incorporation
    board_seats: Optional[int] = Field(None, description="Number of board seats")
    us_mailing_address: Optional[str] = Field(None, description="US mailing address of the company")
    us_phone_number: Optional[str] = Field(None, description="US phone number of the company")
    FEY: Optional[datetime.date] = Field(None, description="Financial End of the Year date for the company")
    principle_activity: Optional[str] = Field(None,
                                              description="Principle activity of the company")  # type of business/description - based on type of business get NAICS Code from https://www.naics.com/search/
    product: Optional[str] = Field(None, description="Product produced and sold by the company")
    EIN: Optional[str] = Field(None, description="Tax ID or EIN of the company")
    corporate_documents: Optional[list[CorporateDocuments]] = Field(None,
                                                                    description="List of corporate documents related to the company")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")


class Subsidiary(BaseModel):
    id: int = Field(..., primary_key=True, description="Unique identifier for the subsidiary")
    company_id: int = Field(..., description="Unique identifier for the company")
    name: str = Field(..., description="Name of the subsidiary")
    jurisdiction: str = Field(..., description="Jurisdiction subsidiary is working in")
    legal_entity_type: CompanyType = Field(..., description="Type of legal entity of the subsidiary")
    FEY: Optional[datetime.date] = Field(None, description="Financial End of the Year date for the subsidiary")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")


class StockData(BaseModel):
    id: int = Field(..., primary_key=True, description="Unique identifier for the stock data")
    company_id: int = Field(..., description="Unique identifier for the company")
    shares_amount: Optional[int] = Field(None, description="Number of shares of the company")
    shares_type: Optional[str] = Field(None, description="Type of shares of the company")
    shares_price: Optional[int] = Field(None, description="Price of the shares of the company")
    consideration: Optional[str] = Field(None,
                                         description="Consideration for the shares of the company in the current position")
    vesting_schedule: Optional[str] = Field(None,
                                            description="Vesting schedule for the shares of the company in the current position")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")


class Director(Person):
    id: int = Field(..., primary_key=True, description="Unique identifier for the director")
    company_id: int = Field(..., description="Unique identifier for the company")
    name: str = Field(..., description="Name of the director")
    phone_number: Optional[str] = Field(None, description="Phone number of the director")
    start_date: Optional[datetime] = Field(None,
                                           description="Date the director started serving on the board of directors of the company")
    end_date: Optional[datetime] = Field(None,
                                         description="Date the director stopped serving on teh board of directors of the company")
    stock_positions: Optional[list[StockData]] = Field(None,
                                                       description="List of stock positions in the company held by the director")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")


class Officer(Person):
    id: int = Field(..., primary_key=True, description="Unique identifier for the officer")
    company_id: int = Field(..., description="Unique identifier for the company")
    name: str = Field(..., description="Name of the officer")
    address: Optional[str] = Field(None, description="Address of the officer")
    phone_number: Optional[str] = Field(None, description="Phone number of the officer")
    officer_title: str = Field(..., description="Title of the officer")
    start_date: Optional[datetime] = Field(None,
                                           description="Date the person started serving as an officer of the company")
    end_date: Optional[datetime] = Field(None,
                                         description="Date the officer stopped serving as an officer of the company")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")


class Shareholder(Person):
    id: int = Field(..., primary_key=True, description="Unique identifier for the shareholder")
    company_id: int = Field(..., description="Unique identifier for the company")
    citizenship: Optional[str] = Field(None, description="Citizenship of the shareholder")
    address_of_resedence: Optional[str] = Field(None,
                                                description="Address of Residence")  # residence permit or/and other proof of residence
    ownersip_percentage: Optional[float] = Field(None,
                                                 description="Percentage of ownership of the company by the shareholder")  # from the shareholder agreement
    stock_positions: Optional[list[StockData]] = Field(None,
                                                       description="List of stock positions in the company held by the director")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")


class BankAccount(BaseModel):
    id: int = Field(..., primary_key=True, description="Unique identifier for the bank account")
    company_id: int = Field(..., description="Unique identifier for the company")
    bank_name: str = Field(..., description="Name of the bank")
    account_number: str = Field(..., description="Account number")
    routing_number: str = Field(..., description="Routing number")  # ABA for ACH withdrowal
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")
