import argparse
import io

from pydub import AudioSegment
from openai import OpenAI


def parse_args():
    parser = argparse.ArgumentParser(description='Transcribe audio file')
    parser.add_argument('audio_file', type=str, help='Path to audio file')
    parser.add_argument('-o', '--output', type=str, help='Path to output file', default='output.txt')
    parser.add_argument('-s', '--segment-seconds', type=int, help='Segment length in seconds', default=300)
    parser.add_argument('--translate', action='store_true', help='Translate the audio', default=False)
    return parser.parse_args()


def transcribe_audio(client: OpenAI, audio_stream):
    transcript = client.audio.transcriptions.create(
        file=audio_stream,
        model="whisper-1",
        response_format="verbose_json",
        timestamp_granularities=["word"]
    )
    return transcript


def translate_audio(client: OpenAI, audio_stream):
    translation = client.audio.translations.create(
        file=audio_stream,
        model="whisper-1",
        response_format="verbose_json",
        # timestamp_granularities=["word"]
    )
    return translation


def main():
    args = parse_args()

    audio = AudioSegment.from_mp3(args.audio_file)
    client = OpenAI()
    full_transcript = ''

    for i in range(0, len(audio), args.segment_seconds * 1000):
        audio_segment = audio[i:i + args.segment_seconds * 1000]
        segment_stream = io.BytesIO()
        audio_segment.export(segment_stream, format='mp3')
        print(f'Transcribing segment {i // 1000} - {(i + args.segment_seconds * 1000) // 1000}')

        segment_stream.name = 'a.mp3'
        if args.translate:
            transcript = translate_audio(client, segment_stream)
        else:
            transcript = transcribe_audio(client, segment_stream)
        full_transcript += transcript.text + '\n'
        print(f'Language = {transcript.language}')

    with open(args.output, 'w') as f:
        f.write(full_transcript)
    print(f'Transcript saved to {args.output}')


if __name__ == '__main__':
    main()
