import argparse
import asyncio
import tqdm

from ira_chat.config.shared_config import SharedConfig
from ira_chat.db import base
from ira_chat.db import api as db_api
from ira_chat.api import extractor as extractor_api
from ira_chat.services import vectorstore, langchain_svc
from ira_chat.services.dataset_manager import index_namespace_suffix, get_storage_dataset_file_name
from ira_chat.services.detection import get_extractor_file_path
from ira_chat.services.vectorstore import get_index_namespace
from ira_chat.utils import utils


def parse_args():
    parser = argparse.ArgumentParser(description='Migrate extractors to datasets')
    return parser.parse_args()


async def main():
    args = parse_args()
    workspaces = await db_api.list_workspaces()
    orgs = await db_api.list_orgs(ids=[ws.org_id for ws in workspaces])
    org_map = {org.id: org for org in orgs}
    workspace_map = {ws.id: ws for ws in workspaces}
    workspace_org_map = {ws.id: org_map[ws.org_id] for ws in workspaces}

    file_manager = SharedConfig().file_manager

    extractors, count = await db_api.list_extractors()
    for extractor in tqdm.tqdm(extractors):
        ws = workspace_map[extractor.workspace_id]
        config = extractor.get_config()
        if config.get('index'):
            continue
        dataset, index = await extractor_api.auto_create_dataset_index(
            f'Dataset for {extractor.title}', extractor.title, config.get('llm_type', 'openai'),
            workspace_org_map[extractor.workspace_id].id, extractor.owner_id,
        )
        config['index'] = f'{dataset.name}/{index.name}'
        extractor = await db_api.update_extractor(extractor, {'config': config})

        vs = vectorstore.init_vectorstore('', org_map[ws.org_id].get_config(), ws.id)
        qdrant_client = vs.client

        # Copy qdrant collection itself for each index:
        old_collection_name = get_index_namespace(index_namespace_suffix(org_map[ws.org_id], ws, f'extractor-{extractor.id}'))
        new_collection_name = get_index_namespace(index_namespace_suffix(org_map[ws.org_id], dataset, index.name))
        exists = qdrant_client.collection_exists(old_collection_name)
        exists_new = qdrant_client.collection_exists(new_collection_name)
        if not exists_new:
            langchain_svc.prepare_vectorstore(vs, new_collection_name, True)

        if exists:
            old_vs = langchain_svc.copy_prepare_vectorstore(vs, old_collection_name)
            size, vector_count = langchain_svc.get_vectorstore_size_vector_count(old_vs)
            await db_api.update_dataset_index(index, {'total_size': size, 'vector_count': vector_count})

        offset = None
        while True and exists:
            points, offset = qdrant_client.scroll(
                old_collection_name, limit=2000, with_payload=True, with_vectors=True, offset=offset,
            )
            print(f'Uploading {len(points)} points')

            if points:
                qdrant_client.upload_points(new_collection_name, points, batch_size=500)

            if not offset:
                break
        # Delete old qdrant index
        qdrant_client.delete_collection(old_collection_name)

        extractor_files = await db_api.list_extractor_files(extractor_id=extractor.id)

        # Update dataset file count and size
        await db_api.update_dataset(dataset, {
            'total_size': sum([f.size for f in extractor_files]),
            'file_count': len(extractor_files),
        })
        await db_api.update_dataset_index(index, {'file_count': len(extractor_files)})

        # Migrate extractor files
        for extractor_file in extractor_files:
            ds_file = await db_api.create_dataset_file({
                'name': extractor_file.name,
                'size': extractor_file.size,
                'status': extractor_file.status,
                'owner_id': extractor_file.owner_id,
                'dataset_id': dataset.id,
                'org_id': ws.org_id,
                'hash': extractor_file.hash,
                'created_at': extractor_file.created_at,
                'updated_at': extractor_file.updated_at,
                'meta': extractor_file.meta,

            })

            old_file_path = get_extractor_file_path(
                workspace_org_map[extractor.workspace_id].name,
                workspace_map[extractor.workspace_id].name,
                extractor_file,
            )
            new_file_path = get_storage_dataset_file_name(org_map[ws.org_id].name, ws.name, ds_file)
            try:
                await file_manager.copy_file(old_file_path, new_file_path)
            except Exception as e:
                print(f'Failed to copy file {old_file_path} to {new_file_path}: {e}')

            # Re-create file embeddings for files
            embeddings = await db_api.list_embeddings(extractor_file_id=extractor_file.id)
            new_embeddings = []
            for emb in embeddings:
                new_embeddings.append({'dataset_file_id': ds_file.id, 'id': emb.id, 'index_id': index.id})
            await db_api.create_dataset_file_embeddings(new_embeddings)

            # Delete old extractor file
            await db_api.delete_embeddings(extractor_file_id=extractor_file.id)
            await db_api.delete_extractor_file(extractor_file.id)
            await file_manager.delete_file(old_file_path)

        await db_api.delete_extractor(extractor.id)


if __name__ == '__main__':
    asyncio.run(main())
