import argparse


def parse_args():
    parser = argparse.ArgumentParser()
    # Add input file
    parser.add_argument('file')
    # Add output file
    parser.add_argument('--md-source')
    parser.add_argument('--img-dir', default='img_dir')
    parser.add_argument('-o', '--output', default='output.md')

    return parser.parse_args()


def main():
    args = parse_args()
    md_text = open(args.file).read()
    md_source = open(args.md_source).read()
    line_nums = []
    img_tags = []
    search = 'unable'
    for i, line in enumerate(md_text.split('\n')):
        if search in line.lower():
            line_nums.append(i)

    for i, line_num in enumerate(line_nums):
        locality_search = 5
        img_start = -1
        while img_start == -1 and locality_search < 20:
            locality_text = '\n'.join(md_text.split('\n')[line_num-locality_search:line_num])
            source_index = md_source.find(locality_text)
            img_start = md_source.find('![', source_index, source_index+len(locality_text)+1000)
            locality_search += 1
        # find image md tag in source
        img_end = md_source.find(')', img_start)
        img_tag = md_source[img_start:img_end+1]
        img_name = img_tag.split('(')[1].split(')')[0]
        img_tags.append(img_tag)
        print(f'{i}. Image name: {img_name}')

    md_text_lines = md_text.split('\n')
    for line_num, img_tag in zip(line_nums, img_tags):
        md_text_lines[line_num] = img_tag

    with open(args.output, 'w') as f:
        f.write('\n'.join(md_text_lines))

    print(f"Output saved to {args.output}")


if __name__ == '__main__':
    main()
