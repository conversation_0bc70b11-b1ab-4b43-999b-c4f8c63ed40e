{"title": "prescription", "description": null, "type": "prescription", "config": {"prompts": {"system_prompt": "Extract all fields manually. \n- Detect and recognize the handwritten medicament if present and write in the unknown field.\n- If there is a double line square with a nuber inside present at the bottom of the document, recognize the number.\n- The output should be in json format.\n- The output should have these json fields:\n- everything other field should be placed in separate \"unknown\" section \n- if prescription is divided into sections labeled \"AFFECTION EXONERANTE\" and \"MALADIES INTERCURRENTES\" put the section name in the \"BiZone\" field\n- if there is any additional text generated after the json formatting put it in the extra field\n- Do not output anything outside of JSON\n\n\"description\": \"\",\n\"medic\": {\"RPPS\": \"\", \"AM\": \"\", \"Nom\": \"\", \"Prenom\": \"\",\"Tel\": \"\",\"Title\": \"\",\"Speciality\": \"\",\"Address\": \"\",},\n\"date\": \"\",\n\"patient\": {\"NomPrenom\": \"\",\"Sex\": \"\",\"Born\": \"\",\"Age\": \"\",\"Weight\": \"\",\"Creatinine\": \"\",\"Height\": \"\",\"Square\": \"\"},\n\"prescription\": [{\"Medicament\": \"\", \"Poso\": \"\",\"Voie\":\"\",\"BiZone\":\"\"}],\n\"double_line_square\": \"\",\n\"unknown\": [{\"field}:\"\"}],\n\"extra\":\"\"\n"}}}