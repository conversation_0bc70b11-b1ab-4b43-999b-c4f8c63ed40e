FROM python:3.12-slim-bookworm

WORKDIR /app

RUN apt update && apt install -y git mime-support build-essential \
  libglib2.0-0 libglib2.0-data libsm6 libgl1 tesseract-ocr poppler-utils libzbar0  && \
  apt-get clean && \
  rm -rf /var/lib/apt/lists/*
COPY requirements.txt /app/requirements.txt

RUN pip install --no-cache-dir -r /app/requirements.txt

COPY ira_chat /app/ira_chat
COPY migrations /app/migrations
COPY alembic.ini /app/alembic.ini
COPY patches /app/patches

RUN python_major=$(python --version | awk '{print $2}' | cut -d . -f 1-2) && \
 patch -N /usr/local/lib/python$python_major/site-packages/langchain_community/document_loaders/parsers/pdf.py /app/patches/pymupdf.patch

ENV PORT=8083
ENV ASYNCIO_LOOP=uvloop
EXPOSE $PORT
CMD ["bash", "-c", "uvicorn ira_chat.main:app --workers 1 --host 0.0.0.0 --port $PORT --no-access-log --loop $ASYNCIO_LOOP"]
