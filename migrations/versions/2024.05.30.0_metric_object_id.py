"""metric object id

Revision ID: 2024.05.30.0
Revises: 2024.05.29.1
Create Date: 2024-05-30 15:52:49.732811

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.05.30.0'
down_revision: Union[str, None] = '2024.05.29.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE metrics ALTER COLUMN object_id TYPE varchar(36)")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE metrics ALTER COLUMN object_id TYPE integer")
    # ### end Alembic commands ###
