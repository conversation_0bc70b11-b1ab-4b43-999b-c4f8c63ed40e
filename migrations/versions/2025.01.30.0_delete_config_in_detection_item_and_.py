"""delete config in detection item and output

Revision ID: 2025.01.30.0
Revises: 2024.12.30.1
Create Date: 2025-01-30 13:54:00.628777

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.01.30.0'
down_revision: Union[str, None] = '2024.12.30.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('detection_item_outputs', 'config')
    op.drop_column('detection_items', 'config')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('detection_items', sa.Column('config', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('detection_item_outputs', sa.Column('config', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
