"""update metrics for chat outputs

Revision ID: 2024.05.07.1
Revises: 2024.05.07.0
Create Date: 2024-05-07 16:41:01.848116

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.05.07.1'
down_revision: Union[str, None] = '2024.05.07.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "UPDATE metrics SET object_id = c.id FROM chat_message_outputs c "
        "WHERE metrics.app_type = 'chat' AND metrics.object_id = c.chat_message_id;"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
