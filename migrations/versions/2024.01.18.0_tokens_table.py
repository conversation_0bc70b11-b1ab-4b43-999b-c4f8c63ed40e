"""Tokens table

Revision ID: 2024.01.18.0
Revises: 2024.01.11.0
Create Date: 2024-01-18 12:11:00.367724

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.01.18.0'
down_revision: Union[str, None] = '2024.01.11.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tokens',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('object_id', sa.BigInteger(), nullable=True),
    sa.Column('object_type', sa.String(length=30), nullable=True),
    sa.Column('permissions', sa.BigInteger(), nullable=True),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('expiry_time', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tokens_object_id'), 'tokens', ['object_id'], unique=False)
    op.create_index(op.f('ix_tokens_object_type'), 'tokens', ['object_type'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_tokens_object_type'), table_name='tokens')
    op.drop_index(op.f('ix_tokens_object_id'), table_name='tokens')
    op.drop_table('tokens')
    # ### end Alembic commands ###
