"""message tag index

Revision ID: 2025.05.07.1
Revises: 2025.05.07.0
Create Date: 2025-05-07 14:23:21.615284

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.05.07.1'
down_revision: Union[str, None] = '2025.05.07.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_chat_messages_tags', 'chat_messages', ['tags'], unique=False, postgresql_using='gin', postgresql_ops={'tags': 'jsonb_path_ops'})
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_chat_messages_tags', table_name='chat_messages', postgresql_using='gin', postgresql_ops={'tags': 'jsonb_path_ops'})
    # ### end Alembic commands ###
