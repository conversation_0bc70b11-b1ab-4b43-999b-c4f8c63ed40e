"""dataset config

Revision ID: 2024.12.27.0
Revises: 2024.12.20.0
Create Date: 2024-12-27 13:17:10.391849

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2024.12.27.0'
down_revision: Union[str, None] = '2024.12.20.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('datasets', sa.Column('config', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.execute("UPDATE datasets SET config = '{}'")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('datasets', 'config')
    # ### end Alembic commands ###
