"""metrics tag

Revision ID: 2025.04.24.0
Revises: 2025.04.23.0
Create Date: 2025-04-24 14:36:24.898117

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.24.0'
down_revision: Union[str, None] = '2025.04.23.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('metrics', sa.Column('tag', sa.String(length=20), nullable=True))
    op.create_index(op.f('ix_metrics_tag'), 'metrics', ['tag'], unique=False)

    conn = op.get_bind()
    messages_with_tags = conn.execute(sa.text(
        "SELECT m.id as message_id, o.id as result_id, m.chat_id, tags FROM chat_messages m "
        "JOIN chats on chats.workspace_id=44 and chats.id=m.chat_id "
        "RIGHT JOIN chat_message_outputs o ON o.chat_message_id = m.id WHERE tags is not NULL;"
    ))
    messages_with_tags = messages_with_tags.fetchall()
    messages_with_tags = [{'message_id': o[0], 'result_id': o[1], 'chat_id': o[2], 'tags': o[3]} for o in messages_with_tags]
    for message in messages_with_tags:
        tags = message['tags']
        if not tags:
            continue
        tag = tags[0]
        # Update metric with tag
        conn.execute(sa.text(
            "UPDATE metrics SET tag = :tag WHERE app_id = :chat_id AND object_id = :result_id;"
        ), {
            'tag': tag,
            'chat_id': message['chat_id'],
            'result_id': str(message['result_id']),
            'app_type': 'chat'
        })

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_metrics_tag'), table_name='metrics')
    op.drop_column('metrics', 'tag')
    # ### end Alembic commands ###
