"""token org permissions

Revision ID: 2024.11.05.0
Revises: 2024.10.30.1
Create Date: 2024-11-05 15:09:47.970659

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.11.05.0'
down_revision: Union[str, None] = '2024.10.30.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tokens', sa.Column('org_permissions', sa.BigInteger(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tokens', 'org_permissions')
    # ### end Alembic commands ###
