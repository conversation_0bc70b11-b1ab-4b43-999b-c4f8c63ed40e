"""timeline point date

Revision ID: 2024.06.25.0
Revises: 2024.06.22.0
Create Date: 2024-06-25 10:05:56.840296

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import func

# revision identifiers, used by Alembic.
revision: str = '2024.06.25.0'
down_revision: Union[str, None] = '2024.06.22.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('timeline_points', sa.Column('date', sa.Date(), nullable=False, server_default=func.current_date()))
    op.create_index(op.f('ix_timeline_points_date'), 'timeline_points', ['date'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_timeline_points_date'), table_name='timeline_points')
    op.drop_column('timeline_points', 'date')
    # ### end Alembic commands ###
