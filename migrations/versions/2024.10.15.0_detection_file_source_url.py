"""detection file source url

Revision ID: 2024.10.15.0
Revises: 2024.10.14.1
Create Date: 2024-10-15 13:50:07.954991

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.10.15.0'
down_revision: Union[str, None] = '2024.10.14.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('detection_item_files', sa.Column('source_url', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('detection_item_files', 'source_url')
    # ### end Alembic commands ###
