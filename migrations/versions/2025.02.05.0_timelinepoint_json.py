"""timelinepoint json

Revision ID: 2025.02.05.0
Revises: 2025.02.04.1
Create Date: 2025-02-05 12:47:42.928637

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.02.05.0'
down_revision: Union[str, None] = '2025.02.04.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'timeline_points',
        'status',
        existing_type=sa.VARCHAR(length=511),
        type_=postgresql.JSON(astext_type=sa.Text()),
        existing_nullable=True,
        postgresql_using='status::json'
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'timeline_points',
        'status',
        existing_type=postgresql.JSON(astext_type=sa.Text()),
        type_=sa.VARCHAR(length=511),
        existing_nullable=True
    )
    # ### end Alembic commands ###
