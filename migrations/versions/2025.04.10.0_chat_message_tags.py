"""chat message tags

Revision ID: 2025.04.10.0
Revises: 2025.04.04.0
Create Date: 2025-04-10 22:12:11.641538

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.04.10.0'
down_revision: Union[str, None] = '2025.04.04.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_messages', sa.Column('tags', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.create_index(op.f('ix_metrics_org_id'), 'metrics', ['org_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_metrics_org_id'), table_name='metrics')
    op.drop_column('chat_messages', 'tags')
    # ### end Alembic commands ###
