"""message grade selector

Revision ID: 2024.12.30.1
Revises: 2024.12.30.0
Create Date: 2024-12-30 14:56:51.123992

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.12.30.1'
down_revision: Union[str, None] = '2024.12.30.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_message_outputs', sa.Column('grade_selector', sa.String(length=63), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_message_outputs', 'grade_selector')
    # ### end Alembic commands ###
