"""add detection file status

Revision ID: 2024.02.29.0
Revises: 2024.02.26.0
Create Date: 2024-02-29 12:18:55.499650

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.02.29.0'
down_revision: Union[str, None] = '2024.02.26.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('detection_item_files', sa.Column('status', sa.String(length=20), nullable=True))
    op.execute("UPDATE detection_item_files set status = 'CREATED'")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('detection_item_files', 'status')
    # ### end Alembic commands ###
