"""extractor tables

Revision ID: 2024.05.27.0
Revises: 2024.05.07.1
Create Date: 2024-05-27 19:24:08.133546

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.05.27.0'
down_revision: Union[str, None] = '2024.05.07.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('extractor_files',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('extractor_id', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('size', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=511), nullable=True),
    sa.Column('owner_id', sa.<PERSON>Integer(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_extractor_files_extractor_id'), 'extractor_files', ['extractor_id'], unique=False)
    op.create_index(op.f('ix_extractor_files_workspace_id'), 'extractor_files', ['workspace_id'], unique=False)
    op.create_table('extractor_item_outputs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('output', sa.Text(), nullable=True),
    sa.Column('rating', sa.Integer(), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.Column('status', sa.Text(), nullable=True),
    sa.Column('workspace_id', sa.Integer(), nullable=True),
    sa.Column('extractor_id', sa.Integer(), nullable=True),
    sa.Column('extractor_item_id', sa.Integer(), nullable=True),
    sa.Column('config', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_extractor_item_outputs_created_at'), 'extractor_item_outputs', ['created_at'], unique=False)
    op.create_index(op.f('ix_extractor_item_outputs_extractor_id'), 'extractor_item_outputs', ['extractor_id'], unique=False)
    op.create_index(op.f('ix_extractor_item_outputs_extractor_item_id'), 'extractor_item_outputs', ['extractor_item_id'], unique=False)
    op.create_index(op.f('ix_extractor_item_outputs_workspace_id'), 'extractor_item_outputs', ['workspace_id'], unique=False)
    op.create_table('extractor_items',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('input', sa.Text(), nullable=True),
    sa.Column('status', sa.Text(), nullable=True),
    sa.Column('owner_id', sa.BigInteger(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=True),
    sa.Column('extractor_id', sa.Integer(), nullable=True),
    sa.Column('config', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_extractor_items_created_at'), 'extractor_items', ['created_at'], unique=False)
    op.create_index(op.f('ix_extractor_items_extractor_id'), 'extractor_items', ['extractor_id'], unique=False)
    op.create_index(op.f('ix_extractor_items_owner_id'), 'extractor_items', ['owner_id'], unique=False)
    op.create_index(op.f('ix_extractor_items_workspace_id'), 'extractor_items', ['workspace_id'], unique=False)
    op.create_table('extractors',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=True),
    sa.Column('owner_id', sa.BigInteger(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('config', sa.Text(), nullable=True),
    sa.Column('schema', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_extractors_created_at'), 'extractors', ['created_at'], unique=False)
    op.create_index(op.f('ix_extractors_owner_id'), 'extractors', ['owner_id'], unique=False)
    op.create_index(op.f('ix_extractors_workspace_id'), 'extractors', ['workspace_id'], unique=False)
    op.add_column('file_embeddings', sa.Column('extractor_file_id', sa.String(36), nullable=True))
    op.create_index(op.f('ix_file_embeddings_extractor_file_id'), 'file_embeddings', ['extractor_file_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_file_embeddings_extractor_file_id'), table_name='file_embeddings')
    op.drop_column('file_embeddings', 'extractor_file_id')
    op.drop_index(op.f('ix_extractors_workspace_id'), table_name='extractors')
    op.drop_index(op.f('ix_extractors_owner_id'), table_name='extractors')
    op.drop_index(op.f('ix_extractors_created_at'), table_name='extractors')
    op.drop_table('extractors')
    op.drop_index(op.f('ix_extractor_items_workspace_id'), table_name='extractor_items')
    op.drop_index(op.f('ix_extractor_items_owner_id'), table_name='extractor_items')
    op.drop_index(op.f('ix_extractor_items_extractor_id'), table_name='extractor_items')
    op.drop_index(op.f('ix_extractor_items_created_at'), table_name='extractor_items')
    op.drop_table('extractor_items')
    op.drop_index(op.f('ix_extractor_item_outputs_workspace_id'), table_name='extractor_item_outputs')
    op.drop_index(op.f('ix_extractor_item_outputs_extractor_item_id'), table_name='extractor_item_outputs')
    op.drop_index(op.f('ix_extractor_item_outputs_extractor_id'), table_name='extractor_item_outputs')
    op.drop_index(op.f('ix_extractor_item_outputs_created_at'), table_name='extractor_item_outputs')
    op.drop_table('extractor_item_outputs')
    op.drop_index(op.f('ix_extractor_files_workspace_id'), table_name='extractor_files')
    op.drop_index(op.f('ix_extractor_files_extractor_id'), table_name='extractor_files')
    op.drop_table('extractor_files')
    # ### end Alembic commands ###
