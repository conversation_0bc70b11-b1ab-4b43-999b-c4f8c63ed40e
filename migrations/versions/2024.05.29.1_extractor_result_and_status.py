"""extractor result and status

Revision ID: 2024.05.29.1
Revises: 2024.05.29.0
Create Date: 2024-05-29 17:09:03.714009

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.05.29.1'
down_revision: Union[str, None] = '2024.05.29.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('extractor_results', sa.Column('owner_id', sa.BigInteger(), nullable=False))
    op.create_index(op.f('ix_extractor_results_owner_id'), 'extractor_results', ['owner_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_extractor_results_owner_id'), table_name='extractor_results')
    op.drop_column('extractor_results', 'owner_id')
    # ### end Alembic commands ###
