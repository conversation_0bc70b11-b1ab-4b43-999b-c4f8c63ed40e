"""dataset scope index

Revision ID: 2024.10.28.1
Revises: 2024.10.28.0
Create Date: 2024-10-28 16:07:15.770019

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.10.28.1'
down_revision: Union[str, None] = '2024.10.28.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_datasets_scope'), 'datasets', ['scope'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_datasets_scope'), table_name='datasets')
    # ### end Alembic commands ###
