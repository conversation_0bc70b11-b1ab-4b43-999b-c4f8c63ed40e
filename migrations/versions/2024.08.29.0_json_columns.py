"""json columns

Revision ID: 2024.08.29.0
Revises: 2024.08.28.0
Create Date: 2024-08-29 14:07:54.055150

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.08.29.0'
down_revision: Union[str, None] = '2024.08.28.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('ALTER TABLE chat_message_outputs ALTER COLUMN context TYPE JSON USING context::json')
    op.execute('ALTER TABLE chat_messages DROP COLUMN IF EXISTS context')
    op.execute('ALTER TABLE chat_messages DROP COLUMN IF EXISTS note')
    op.execute('ALTER TABLE chat_messages DROP COLUMN IF EXISTS rating')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_messages', sa.Column('rating', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('chat_messages', sa.Column('note', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('chat_messages', sa.Column('context', sa.TEXT(), autoincrement=False, nullable=True))
    op.alter_column('chat_message_outputs', 'context',
               existing_type=sa.JSON(none_as_null=True),
               type_=sa.TEXT(),
               existing_nullable=True)
    # ### end Alembic commands ###
