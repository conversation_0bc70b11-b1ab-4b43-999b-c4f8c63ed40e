"""detection item config json

Revision ID: 2024.10.08.0
Revises: 2024.09.30.0
Create Date: 2024-10-08 14:14:27.225317

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '2024.10.08.0'
down_revision: Union[str, None] = '2024.09.30.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'detection_items',
        'config',
        existing_type=sa.TEXT(),
        type_=sa.JSON(),
        existing_nullable=True,
        postgresql_using='config::json'
    )
    op.alter_column(
        'detections',
        'config',
        existing_type=sa.TEXT(),
        type_=sa.JSON(),
        existing_nullable=True,
        postgresql_using='config::json'
    )
    op.alter_column(
        'detection_item_outputs',
        'config',
        existing_type=sa.TEXT(),
        type_=sa.JSON(),
        existing_nullable=True,
        postgresql_using='config::json'
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'detections',
        'config',
        existing_type=sa.JSON(),
        type_=sa.TEXT(),
        existing_nullable=True)
    op.alter_column(
        'detection_items',
        'config',
        existing_type=sa.JSON(),
        type_=sa.TEXT(),
        existing_nullable=True
    )
    op.alter_column(
        'detection_item_output',
        'config',
        existing_type=sa.JSON(),
        type_=sa.TEXT(),
        existing_nullable=True
    )
    # ### end Alembic commands ###
