"""drop config in timeline point

Revision ID: 2024.06.22.0
Revises: 2024.06.17.1
Create Date: 2024-06-22 14:31:58.060699

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.06.22.0'
down_revision: Union[str, None] = '2024.06.17.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('timeline_points', 'config')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('timeline_points', sa.Column('config', sa.TEXT(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
