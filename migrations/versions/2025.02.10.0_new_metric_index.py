"""new metric index

Revision ID: 2025.02.10.0
Revises: 2025.02.05.0
Create Date: 2025-02-10 01:01:18.834498

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.10.0'
down_revision: Union[str, None] = '2025.02.05.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("CREATE INDEX idx_metrics_ws_type_updated_at_app_id ON metrics (workspace_id, type, updated_at, app_id);")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_metrics_ws_type_updated_at_app_id', table_name='metrics')
    # ### end Alembic commands ###
