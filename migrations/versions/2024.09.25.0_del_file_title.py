"""del file title

Revision ID: 2024.09.25.0
Revises: 2024.09.11.0
Create Date: 2024-09-25 15:23:25.038211

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.09.25.0'
down_revision: Union[str, None] = '2024.09.11.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.drop_index('indexname', table_name='files')
    op.execute("DROP INDEX IF EXISTS indexname")
    op.drop_column('files', 'title')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('files', sa.Column('title', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    #op.create_index('indexname', 'files', ['name'], unique=False)
    # ### end Alembic commands ###
