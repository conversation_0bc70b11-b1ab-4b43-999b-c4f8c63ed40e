"""extractor file hash

Revision ID: 2024.07.12.0
Revises: 2024.07.03.0
Create Date: 2024-07-12 16:38:56.457877

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.07.12.0'
down_revision: Union[str, None] = '2024.07.03.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('extractor_files', sa.Column('hash', sa.String(), nullable=True))
    op.create_index(op.f('ix_extractor_files_hash'), 'extractor_files', ['hash'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_extractor_files_hash'), table_name='extractor_files')
    op.drop_column('extractor_files', 'hash')
    # ### end Alembic commands ###
