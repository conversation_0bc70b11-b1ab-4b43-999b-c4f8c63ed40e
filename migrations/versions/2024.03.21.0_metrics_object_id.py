"""metrics object id

Revision ID: 2024.03.21.0
Revises: 2024.03.13.0
Create Date: 2024-03-21 10:33:28.696113

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.03.21.0'
down_revision: Union[str, None] = '2024.03.13.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('metrics', sa.Column('object_id', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_metrics_object_id'), 'metrics', ['object_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_metrics_object_id'), table_name='metrics')
    op.drop_column('metrics', 'object_id')
    # ### end Alembic commands ###
