"""provided answers

Revision ID: 2024.04.23.0
Revises: 2024.04.22.0
Create Date: 2024-04-23 13:35:05.787544

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.04.23.0'
down_revision: Union[str, None] = '2024.04.22.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('provided_answers',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=True),
    sa.Column('app_id', sa.Integer(), nullable=True),
    sa.Column('object_id', sa.Integer(), nullable=False),
    sa.Column('question', sa.Text(), nullable=True),
    sa.Column('answer', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_provided_answers_app_id'), 'provided_answers', ['app_id'], unique=False)
    op.create_index(op.f('ix_provided_answers_object_id'), 'provided_answers', ['object_id'], unique=False)
    op.create_index(op.f('ix_provided_answers_workspace_id'), 'provided_answers', ['workspace_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_provided_answers_workspace_id'), table_name='provided_answers')
    op.drop_index(op.f('ix_provided_answers_object_id'), table_name='provided_answers')
    op.drop_index(op.f('ix_provided_answers_app_id'), table_name='provided_answers')
    op.drop_table('provided_answers')
    # ### end Alembic commands ###
