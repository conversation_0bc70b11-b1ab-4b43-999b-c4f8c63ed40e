"""metrics_app_type_id

Revision ID: 2024.02.26.0
Revises: 2024.02.22.0
Create Date: 2024-02-26 14:05:06.466878

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.02.26.0'
down_revision: Union[str, None] = '2024.02.22.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('metrics', sa.Column('app_type', sa.String(length=30), nullable=True))
    op.alter_column(
        'metrics', 'chat_id',
        new_column_name='app_id'
    )
    op.drop_index('ix_metrics_chat_id', table_name='metrics')
    op.create_index(op.f('ix_metrics_app_id'), 'metrics', ['app_id'], unique=False)
    op.create_index(op.f('ix_metrics_app_type'), 'metrics', ['app_type'], unique=False)
    op.execute("UPDATE metrics SET app_type = 'chat'")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_metrics_app_type'), table_name='metrics')
    op.drop_index(op.f('ix_metrics_app_id'), table_name='metrics')
    op.create_index('ix_metrics_chat_id', 'metrics', ['chat_id'], unique=False)
    op.alter_column(
        'metrics', 'app_id',
        new_column_name='chat_id'
    )
    op.drop_column('metrics', 'app_type')
    # ### end Alembic commands ###
