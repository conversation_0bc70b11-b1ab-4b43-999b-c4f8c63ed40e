"""compute cost metric

Revision ID: 2024.03.27.0
Revises: 2024.03.25.0
Create Date: 2024-03-27 16:29:15.055587

"""
import collections
import datetime
import json
from typing import Sequence, Union, List

from alembic import op
import sqlalchemy as sa
from langchain_core.messages import BaseMessage
from langchain_core.outputs import Generation
from sqlalchemy import orm

from ira_chat.db import models
from ira_chat.services import llm

# revision identifiers, used by Alembic.
revision: str = '2024.03.27.0'
down_revision: Union[str, None] = '2024.03.25.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    metric_table = sa.table(
        'metrics',
        # sa.column('id', sa.Integer(), primary_key=True),
        sa.Column('value', sa.Double()),
        sa.Column('type', sa.String(20)),
        sa.Column('workspace_id', sa.Integer()),
        sa.Column('app_type', sa.String(20)),
        sa.Column('app_id', sa.Integer()),
        sa.Column('object_id', sa.Integer()),
        sa.Column('extra', sa.Text()),
        sa.Column('created_at', sa.DateTime(timezone=True)),
        sa.Column('updated_at', sa.DateTime(timezone=True)),
    )

    fields = ['type', 'workspace_id', 'app_id', 'extra', 'created_at', 'updated_at', 'value', 'app_type', 'object_id']
    res = conn.execute(sa.text(f"SELECT {', '.join(fields)} FROM metrics where type in ('input_tokens', 'output_tokens')"))
    results = res.fetchall()
    metrics = [dict(zip(fields, tp)) for tp in results]
    to_insert = []
    llms = llm.llms
    model_to_type = {}
    for type_, init_llm in llms.items():
        if not hasattr(init_llm, 'prices_per_model'):
            continue
        model_to_type.update({m: type_ for m in init_llm.prices_per_model()})

    grouped_metrics = collections.defaultdict(dict)
    for metric in metrics:
        if metric['type'] == 'input_tokens':
            metric['input_tokens'] = metric['value']
        else:
            metric['output_tokens'] = metric['value']
        date = metric['created_at'].replace(microsecond=0)
        grouped_metrics[date].update(metric)

    for metric in grouped_metrics.values():
        extra = metric['extra']
        if not extra:
            continue
        model_name = json.loads(extra).get('model_name')
        if not model_name:
            continue
        item = {
            'created_at': metric['created_at'],
            'updated_at': metric['updated_at'],
            'app_type': metric['app_type'],
            'object_id': metric['object_id'],
            'app_id': metric['app_id'],
            'workspace_id': metric['workspace_id'],
            'type': models.METRIC_COST_DOLLARS,
            'extra': extra,
        }
        input_tokens = metric.get('input_tokens')
        output_tokens = metric.get('output_tokens')
        if not input_tokens or not output_tokens:
            continue
        type_ = model_to_type.get(model_name)
        if not type_:
            continue
        messages: List[List[BaseMessage]] = [[BaseMessage(content='a' * int(input_tokens) * 4, type='system')]]
        output: List[Generation] = [Generation(text='a' * int(output_tokens) * 4)]

        cost = llms[type_].compute_cost(messages, output, input_tokens, output_tokens, model_name)
        cost = round(cost, 10)
        item['value'] = cost
        to_insert.append(item)

    op.bulk_insert(metric_table, to_insert)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
