"""ws access type

Revision ID: 2024.04.22.0
Revises: 2024.04.18.0
Create Date: 2024-04-22 10:33:13.214778

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.04.22.0'
down_revision: Union[str, None] = '2024.04.18.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('ALTER TABLE workspaces RENAME COLUMN type TO access_type')
    op.execute("UPDATE workspaces SET access_type = 'members'")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # ### end Alembic commands ###
    pass
