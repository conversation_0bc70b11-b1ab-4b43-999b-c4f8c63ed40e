"""encrypt config

Revision ID: 2024.03.13.0
Revises: 2024.03.06.0
Create Date: 2024-03-13 17:52:33.750713

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

from ira_chat.utils import blowfish_utils


# revision identifiers, used by Alembic.
revision: str = '2024.03.13.0'
down_revision: Union[str, None] = '2024.03.06.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    res = conn.execute(sa.text("SELECT id, config FROM organizations"))
    results = res.fetchall()
    orgs = [{'id': o[0], 'config': o[1]} for o in results]
    for org in orgs:
        try:
            blowfish_utils.decrypt_string(org['config'])
        except:
            # Not encoded, encode
            config_encrypted = blowfish_utils.encrypt_string(org['config'])
            # Save
            conn.execute(
                sa.text("UPDATE organizations SET config = :config WHERE id = :id"),
                parameters={'config': config_encrypted, 'id': org['id']}
            )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
