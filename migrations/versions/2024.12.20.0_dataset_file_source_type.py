"""dataset file source type

Revision ID: 2024.12.20.0
Revises: 2024.12.06.0
Create Date: 2024-12-20 12:23:47.858291

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.12.20.0'
down_revision: Union[str, None] = '2024.12.06.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dataset_files', sa.Column('source_type', sa.String(length=40), nullable=True))
    op.create_index(op.f('ix_dataset_files_source_type'), 'dataset_files', ['source_type'], unique=False)
    op.execute("UPDATE dataset_files SET source_type = 'file'")
    op.execute("UPDATE dataset_files SET source_type = 'corrected_file_txt' WHERE name LIKE 'provided_answer%'")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_dataset_files_source_type'), table_name='dataset_files')
    op.drop_column('dataset_files', 'source_type')
    # ### end Alembic commands ###
