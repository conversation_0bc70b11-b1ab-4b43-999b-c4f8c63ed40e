"""output_file_id

Revision ID: 2024.06.17.1
Revises: 2024.06.17.0
Create Date: 2024-06-17 14:36:06.436995

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.06.17.1'
down_revision: Union[str, None] = '2024.06.17.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('detection_item_outputs', sa.Column('output_file_id', sa.String(length=36), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('detection_item_outputs', 'output_file_id')
    # ### end Alembic commands ###
