"""metric date index

Revision ID: 2024.03.04.0
Revises: 2024.02.29.0
Create Date: 2024-03-04 10:34:07.378696

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.03.04.0'
down_revision: Union[str, None] = '2024.02.29.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_metrics_updated_at'), 'metrics', ['updated_at'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_metrics_updated_at'), table_name='metrics')
    # ### end Alembic commands ###
