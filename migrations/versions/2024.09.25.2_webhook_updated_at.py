"""webhook updated at

Revision ID: 2024.09.25.2
Revises: 2024.09.25.1
Create Date: 2024-09-25 16:03:11.428861

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.09.25.2'
down_revision: Union[str, None] = '2024.09.25.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organization_webhooks', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organization_webhooks', 'updated_at')
    # ### end Alembic commands ###
