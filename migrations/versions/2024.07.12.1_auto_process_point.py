"""auto process point


Revision ID: 2024.07.12.1
Revises: 2024.07.12.0
Create Date: 2024-07-12 18:13:09.108275

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.07.12.1'
down_revision: Union[str, None] = '2024.07.12.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('timeline_points', sa.Column('auto_process', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('timeline_points', 'auto_process')
    # ### end Alembic commands ###
