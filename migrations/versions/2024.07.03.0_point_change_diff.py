"""point change diff

Revision ID: 2024.07.03.0
Revises: 2024.07.02.0
Create Date: 2024-07-03 17:25:04.127424

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.07.03.0'
down_revision: Union[str, None] = '2024.07.02.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('timeline_point_changes', sa.Column('diff', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('timeline_point_changes', 'diff')
    # ### end Alembic commands ###
