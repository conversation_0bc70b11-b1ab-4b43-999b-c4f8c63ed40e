"""add vector count

Revision ID: 2024.11.06.0
Revises: 2024.11.05.0
Create Date: 2024-11-06 11:10:52.043206

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.11.06.0'
down_revision: Union[str, None] = '2024.11.05.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dataset_indexes', sa.Column('vector_count', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('dataset_indexes', 'vector_count')
    # ### end Alembic commands ###
