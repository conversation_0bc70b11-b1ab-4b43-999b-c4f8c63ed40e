"""metric index

Revision ID: 2024.10.09.1
Revises: 2024.10.09.0
Create Date: 2024-10-09 17:45:37.399592

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.10.09.1'
down_revision: Union[str, None] = '2024.10.09.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_metrics_ws_app_type_app_id_object', 'metrics', ['workspace_id', 'app_type', 'app_id', 'object_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_metrics_ws_app_type_app_id_object', table_name='metrics')
    # ### end Alembic commands ###
