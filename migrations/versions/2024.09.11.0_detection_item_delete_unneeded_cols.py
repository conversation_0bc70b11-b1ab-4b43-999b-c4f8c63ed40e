"""detection item delete unneeded cols

Revision ID: 2024.09.11.0
Revises: 2024.09.02.0
Create Date: 2024-09-11 13:12:34.056164

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.09.11.0'
down_revision: Union[str, None] = '2024.09.02.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('detection_items', 'output')
    op.drop_column('detection_items', 'note')
    op.drop_column('detection_items', 'rating')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('detection_items', sa.Column('rating', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('detection_items', sa.Column('note', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('detection_items', sa.Column('output', sa.TEXT(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
