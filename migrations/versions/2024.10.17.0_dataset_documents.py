"""dataset documents

Revision ID: 2024.10.17.0
Revises: 2024.10.16.0
Create Date: 2024-10-17 11:40:44.703113

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.10.17.0'
down_revision: Union[str, None] = '2024.10.16.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('dataset_file_documents',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('dataset_file_id', sa.String(length=36), nullable=True),
    sa.Column('index_id', sa.String(length=36), nullable=True),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('doc_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_dataset_file_documents_dataset_file_id'), 'dataset_file_documents', ['dataset_file_id'], unique=False)
    op.create_index(op.f('ix_dataset_file_documents_index_id'), 'dataset_file_documents', ['index_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_dataset_file_documents_index_id'), table_name='dataset_file_documents')
    op.drop_index(op.f('ix_dataset_file_documents_dataset_file_id'), table_name='dataset_file_documents')
    op.drop_table('dataset_file_documents')
    # ### end Alembic commands ###
