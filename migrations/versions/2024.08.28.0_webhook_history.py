"""webhook history

Revision ID: 2024.08.28.0
Revises: 2024.08.27.0
Create Date: 2024-08-28 17:47:06.111600

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.08.28.0'
down_revision: Union[str, None] = '2024.08.27.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('organization_webhook_items',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('webhook_id', sa.Integer(), nullable=False),
    sa.Column('headers', sa.Text(), nullable=True),
    sa.Column('payload', sa.Text(), nullable=True),
    sa.Column('status_code', sa.Integer(), nullable=True),
    sa.Column('response_headers', sa.Text(), nullable=True),
    sa.Column('response', sa.Text(), nullable=True),
    sa.Column('error', sa.Text(), nullable=True),
    sa.Column('sent_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_organization_webhook_items_webhook_id'), 'organization_webhook_items', ['webhook_id'], unique=False)
    op.add_column('organization_webhooks', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organization_webhooks', 'created_at')
    op.drop_index(op.f('ix_organization_webhook_items_webhook_id'), table_name='organization_webhook_items')
    op.drop_table('organization_webhook_items')
    # ### end Alembic commands ###
