"""metric tag index

Revision ID: 2025.04.24.1
Revises: 2025.04.24.0
Create Date: 2025-04-24 17:02:35.848933

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.24.1'
down_revision: Union[str, None] = '2025.04.24.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "CREATE INDEX IF NOT EXISTS ix_metrics_tag_null_filtered"
        " ON metrics(workspace_id, updated_at, type, value) WHERE tag IS NULL;"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("DROP INDEX ix_metrics_tag_null_filtered;")
    # ### end Alembic commands ###
