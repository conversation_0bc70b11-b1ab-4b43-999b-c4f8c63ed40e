"""point status

Revision ID: 2024.07.01.0
Revises: 2024.06.26.0
Create Date: 2024-07-01 12:54:11.438412

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.07.01.0'
down_revision: Union[str, None] = '2024.06.26.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('timeline_points', sa.Column('status', sa.String(length=511), nullable=True))
    op.execute('UPDATE timeline_points set status = \'{"status": "SUCCESS", "message": null}\'')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('timeline_points', 'status')
    # ### end Alembic commands ###
