"""increase status len

Revision ID: 2024.04.18.0
Revises: 2024.04.17.0
Create Date: 2024-04-18 10:15:36.270821

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.04.18.0'
down_revision: Union[str, None] = '2024.04.17.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'files', 'status', type_= sa.VARCHAR(length=511)
    )
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
