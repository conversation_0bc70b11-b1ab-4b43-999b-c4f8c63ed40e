"""compute extractor file hashes

Revision ID: 2024.07.15.0
Revises: 2024.07.12.1
Create Date: 2024-07-15 11:44:39.294173

"""
import hashlib
from typing import Sequence, Union
import asyncio

from alembic import op
import sqlalchemy as sa

from ira_chat.config.shared_config import SharedConfig
from ira_chat.db import models
from ira_chat.services.detection import get_extractor_file_path

# revision identifiers, used by Alembic.
revision: str = '2024.07.15.0'
down_revision: Union[str, None] = '2024.07.12.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('ALTER TABLE extractor_files ALTER COLUMN hash type varchar(64);')

    conn = op.get_bind()
    res = conn.execute(sa.text("SELECT id, name, extractor_id, workspace_id FROM extractor_files where hash is NULL;"))
    results = res.fetchall()
    files = [{'id': o[0], 'name': o[1], 'extractor_id': o[2], 'workspace_id': o[3]} for o in results]

    res = conn.execute(sa.text("SELECT id, name, org_id FROM workspaces"))
    results = res.fetchall()
    workspaces = [{'id': o[0], 'name': o[1], 'org_id': o[2]} for o in results]

    res = conn.execute(sa.text("SELECT id, name FROM organizations"))
    results = res.fetchall()
    orgs = [{'id': o[0], 'name': o[1]} for o in results]

    ws_map = {ws['id']: ws for ws in workspaces}
    org_map = {org['id']: org for org in orgs}

    not_found = []
    for file in files:
        ws = ws_map[file['workspace_id']]
        org = org_map[ws['org_id']]
        file_db = models.ExtractorFile(workspace_id=ws['id'], name=file['name'], id=file['id'])
        file_path = get_extractor_file_path(org['name'], ws['name'], file_db)

        loop = asyncio.get_event_loop()
        try:
            if loop.is_running():
                import threading

                def run_async(coro):  # coro is a couroutine, see example
                    if not _thr.is_alive():
                        _thr.start()
                    future = asyncio.run_coroutine_threadsafe(coro, _loop)
                    return future.result()

                _loop = asyncio.new_event_loop()
                _thr = threading.Thread(target=_loop.run_forever, name="Async Runner", daemon=True)
                res = run_async(SharedConfig().file_manager.read_file(file_path))
                data = run_async(SharedConfig().file_manager.get_data(res))
            else:
                res = loop.run_until_complete(SharedConfig().file_manager.read_file(file_path))
                data = loop.run_until_complete(SharedConfig().file_manager.get_data(res))
        except Exception as e:
            print(str(e))
            not_found.append(file)
        # data = asyncio.run()
        sha = hashlib.sha256(data).hexdigest()
        op.execute(f"UPDATE extractor_files SET hash = '{sha}' WHERE id = '{file['id']}'")

    print("Not found files:")
    print(not_found)
    print('ids:')
    _ = [print(f['id']) for f in not_found]
    in_op = "'" + "', '".join([f['id'] for f in not_found]) + "'"
    print(f"DELETE FROM extractor_files WHERE id IN ({in_op});")


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
