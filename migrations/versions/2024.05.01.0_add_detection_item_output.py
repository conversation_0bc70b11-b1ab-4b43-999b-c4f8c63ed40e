"""add detection item output

Revision ID: 2024.05.01.0
Revises: 2024.04.23.1
Create Date: 2024-05-01 15:57:10.990723

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.05.01.0'
down_revision: Union[str, None] = '2024.04.23.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('detection_item_outputs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('output', sa.Text(), nullable=True),
    sa.Column('rating', sa.Integer(), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.Column('status', sa.Text(), nullable=True),
    sa.Column('workspace_id', sa.Integer(), nullable=True),
    sa.Column('detection_id', sa.Integer(), nullable=True),
    sa.Column('detection_item_id', sa.Integer(), nullable=True),
    sa.Column('config', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_detection_item_outputs_created_at'), 'detection_item_outputs', ['created_at'], unique=False)
    op.create_index(op.f('ix_detection_item_outputs_detection_id'), 'detection_item_outputs', ['detection_id'], unique=False)
    op.create_index(op.f('ix_detection_item_outputs_detection_item_id'), 'detection_item_outputs', ['detection_item_id'], unique=False)
    op.create_index(op.f('ix_detection_item_outputs_workspace_id'), 'detection_item_outputs', ['workspace_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_detection_item_outputs_workspace_id'), table_name='detection_item_outputs')
    op.drop_index(op.f('ix_detection_item_outputs_detection_item_id'), table_name='detection_item_outputs')
    op.drop_index(op.f('ix_detection_item_outputs_detection_id'), table_name='detection_item_outputs')
    op.drop_index(op.f('ix_detection_item_outputs_created_at'), table_name='detection_item_outputs')
    op.drop_table('detection_item_outputs')
    # ### end Alembic commands ###
