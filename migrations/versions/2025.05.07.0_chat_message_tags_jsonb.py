"""chat_message tags jsonb

Revision ID: 2025.05.07.0
Revises: 2025.04.27.0
Create Date: 2025-05-07 14:07:05.869796

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.05.07.0'
down_revision: Union[str, None] = '2025.04.27.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'chat_messages', 'tags',
        existing_type=postgresql.JSON(astext_type=sa.Text()),
        type_=postgresql.JSONB(astext_type=sa.Text()),
        postgresql_using='tags::jsonb',
        existing_nullable=True
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'chat_messages', 'tags',
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        type_=postgresql.JSON(astext_type=sa.Text()),
        postgresql_using='tags::json',
        existing_nullable=True
    )
    # ### end Alembic commands ###
