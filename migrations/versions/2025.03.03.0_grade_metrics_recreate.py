"""grade metrics recreate

Revision ID: 2025.03.03.0
Revises: 2025.02.10.0
Create Date: 2025-03-03 13:57:25.894084

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

from ira_chat.db import models

# revision identifiers, used by Alembic.
revision: str = '2025.03.03.0'
down_revision: Union[str, None] = '2025.02.10.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    grade_selector_map = {
        models.GradeSelector.CORRECT_ANSWER: models.METRIC_CORRECT_ANSWER,
        models.GradeSelector.INCORRECT_ANSWER: models.METRIC_INCORRECT_ANSWER,
        models.GradeSelector.PARTIAL_ANSWER: models.METRIC_PARTIAL_ANSWER,
    }
    conn = op.get_bind()
    res = conn.execute(sa.text(
        "SELECT chat_message_outputs.id, chat_id, chats.workspace_id, grade_selector, chat_message_outputs.updated_at "
        "FROM chat_message_outputs JOIN chats on chats.id = chat_message_outputs.chat_id where grade_selector in "
        f"{tuple(list(grade_selector_map.keys()))};"
    ))
    results = res.fetchall()
    graded_results = [
        {'id': o[0], 'chat_id': o[1], 'workspace_id': o[2], 'grade_selector': o[3], 'updated_at': o[4]}
        for o in results
    ]

    for result in graded_results:
        grade_selector_msg = result['grade_selector']
        grade_selector = grade_selector_map[grade_selector_msg]
        app_id = result['chat_id']
        workspace_id = result['workspace_id']
        result_id = result['id']
        res = conn.execute(sa.text(
            "SELECT id, app_id as chat_id, type, updated_at from metrics "
            "WHERE app_type = 'chat' and type = :type_ and app_id = :app_id "
            "and workspace_id = :workspace_id AND object_id = :object_id;"), {
                'type_': grade_selector,
                'app_id': app_id,
                'workspace_id': workspace_id,
                'object_id': str(result_id)
            })
        metrics_raw = res.fetchall()
        metrics = [
            {'id': o[0], 'chat_id': o[1], 'type': o[2], 'updated_at': o[3]}
            for o in metrics_raw
        ]
        if not metrics:
            conn.execute(
                sa.text(
                    "INSERT INTO metrics (workspace_id, app_id, app_type, object_id, type, value, created_at, updated_at) "
                    "VALUES (:workspace_id, :app_id, 'chat', :object_id, :type_, :value, :updated_at, :updated_at);"
                ), {
                    'workspace_id': workspace_id,
                    'app_id': app_id,
                    'object_id': str(result_id),
                    'type_': grade_selector,
                    'value': 1,
                    'updated_at': result['updated_at']
                }
            )
        else:
            conn.execute(
                sa.text(
                    "UPDATE metrics SET value = 1, updated_at = :updated_at "
                    "WHERE id = :id;"
                ), {
                    'id': metrics[0]['id'],
                    'updated_at': result['updated_at']
                }
            )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
