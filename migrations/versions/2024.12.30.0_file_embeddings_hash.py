"""file embeddings hash

Revision ID: 2024.12.30.0
Revises: 2024.12.27.0
Create Date: 2024-12-30 10:11:36.171209

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.12.30.0'
down_revision: Union[str, None] = '2024.12.27.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dataset_file_embeddings', sa.Column('dataset_file_hash', sa.String(length=64), nullable=True))
    # Take dataset_file_hash from dataset_files by id
    op.execute('UPDATE dataset_file_embeddings AS dfe SET dataset_file_hash = df.hash FROM dataset_files AS df WHERE dfe.dataset_file_id = df.id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('dataset_file_embeddings', 'dataset_file_hash')
    # ### end Alembic commands ###
