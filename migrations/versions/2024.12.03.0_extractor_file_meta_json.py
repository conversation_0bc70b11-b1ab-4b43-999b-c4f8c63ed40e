"""extractor file meta json

Revision ID: 2024.12.03.0
Revises: 2024.11.23.0
Create Date: 2024-12-03 12:47:55.178938

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2024.12.03.0'
down_revision: Union[str, None] = '2024.11.23.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dataset_files', sa.Column('meta', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.alter_column('extractor_files', 'meta',
               existing_type=sa.TEXT(),
               type_=postgresql.JSON(astext_type=sa.Text()),
               existing_nullable=True,
               postgresql_using='meta::json')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('extractor_files', 'meta',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.drop_column('dataset_files', 'meta')
    # ### end Alembic commands ###
