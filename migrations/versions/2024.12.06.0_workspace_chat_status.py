"""workspace chat status

Revision ID: 2024.12.06.0
Revises: 2024.12.03.0
Create Date: 2024-12-06 11:24:58.166390

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2024.12.06.0'
down_revision: Union[str, None] = '2024.12.03.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chats', sa.Column('status', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column('workspaces', sa.Column('status', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.execute("UPDATE chats SET status = '{\"status\": \"SUCCESS\"}'")
    op.execute("UPDATE workspaces SET status = '{\"status\": \"SUCCESS\"}'")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('workspaces', 'status')
    op.drop_column('chats', 'status')
    # ### end Alembic commands ###
