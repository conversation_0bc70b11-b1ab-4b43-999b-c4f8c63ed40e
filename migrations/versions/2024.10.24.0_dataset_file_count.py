"""dataset file count

Revision ID: 2024.10.24.0
Revises: 2024.10.23.1
Create Date: 2024-10-24 11:58:29.190022

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.10.24.0'
down_revision: Union[str, None] = '2024.10.23.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('datasets', sa.Column('file_count', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('datasets', 'file_count')
    # ### end Alembic commands ###
