"""populate chat outputs

Revision ID: 2024.05.06.1
Revises: 2024.05.06.0
Create Date: 2024-05-06 16:54:16.910750

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.05.06.1'
down_revision: Union[str, None] = '2024.05.06.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "INSERT INTO chat_message_outputs "
        "(chat_id, chat_message_id, content, owner_id, role, status, context, rating, note, created_at, updated_at) "
        "SELECT chat_id, id - 1, content, owner_id, role, status, context, rating, note, created_at, updated_at "
        "FROM chat_messages where role != 'user'"
    )
    op.execute("DELETE FROM chat_messages where role != 'user'")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
