"""index llm type embedding provider


Revision ID: 2024.10.23.0
Revises: 2024.10.17.0
Create Date: 2024-10-23 10:45:41.409116

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.10.23.0'
down_revision: Union[str, None] = '2024.10.17.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'dataset_indexes',
        'llm_type',
        new_column_name='embedding_provider',
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'dataset_indexes',
        column_name='embedding_provider',
        new_column_name='llm_type',
    )
    # ### end Alembic commands ###
