"""metrics value double

Revision ID: 2024.03.25.0
Revises: 2024.03.21.0
Create Date: 2024-03-25 13:30:17.474885

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.03.25.0'
down_revision: Union[str, None] = '2024.03.21.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("alter table metrics alter column value type double precision;")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
