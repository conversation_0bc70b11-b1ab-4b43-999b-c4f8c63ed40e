"""org webhooks

Revision ID: 2024.08.27.0
Revises: 2024.07.23.0
Create Date: 2024-08-27 13:28:30.375209

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.08.27.0'
down_revision: Union[str, None] = '2024.07.23.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('organization_webhooks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('key', sa.String(length=36), nullable=False),
    sa.Column('org_id', sa.Integer(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('url', sa.Text(), nullable=True),
    sa.Column('headers', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_organization_webhooks_org_id'), 'organization_webhooks', ['org_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_organization_webhooks_org_id'), table_name='organization_webhooks')
    op.drop_table('organization_webhooks')
    # ### end Alembic commands ###
