"""update existing metric object_id

Revision ID: 2024.05.02.0
Revises: 2024.05.01.1
Create Date: 2024-05-02 12:14:59.939683

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.05.02.0'
down_revision: Union[str, None] = '2024.05.01.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "UPDATE metrics SET object_id = d.id FROM detection_item_outputs d "
        "WHERE metrics.app_type = 'detection' AND metrics.object_id = d.detection_item_id"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
