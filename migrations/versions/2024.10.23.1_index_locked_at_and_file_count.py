"""index locked at and file_count

Revision ID: 2024.10.23.1
Revises: 2024.10.23.0
Create Date: 2024-10-23 11:54:07.200428

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.10.23.1'
down_revision: Union[str, None] = '2024.10.23.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dataset_indexes', sa.Column('file_count', sa.Integer(), nullable=True))
    op.add_column('dataset_indexes', sa.Column('locked_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('dataset_indexes', 'locked_at')
    op.drop_column('dataset_indexes', 'file_count')
    # ### end Alembic commands ###
