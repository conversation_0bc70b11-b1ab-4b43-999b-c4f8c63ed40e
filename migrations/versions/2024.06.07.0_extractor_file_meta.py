"""extractor file meta

Revision ID: 2024.06.07.0
Revises: 2024.06.03.0
Create Date: 2024-06-07 16:00:00.068695

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.06.07.0'
down_revision: Union[str, None] = '2024.06.03.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('extractor_files', sa.Column('meta', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('extractor_files', 'meta')
    # ### end Alembic commands ###
