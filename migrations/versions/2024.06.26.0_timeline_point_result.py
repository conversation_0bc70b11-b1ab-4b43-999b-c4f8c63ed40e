"""timeline point result

Revision ID: 2024.06.26.0
Revises: 2024.06.25.0
Create Date: 2024-06-26 15:47:54.129506

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.06.26.0'
down_revision: Union[str, None] = '2024.06.25.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('timeline_point_results',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('output', sa.Text(), nullable=True),
    sa.Column('rating', sa.Integer(), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.Column('status', sa.Text(), nullable=True),
    sa.Column('owner_id', sa.<PERSON>(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=True),
    sa.Column('timeline_id', sa.Integer(), nullable=True),
    sa.Column('point_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_timeline_point_results_created_at'), 'timeline_point_results', ['created_at'], unique=False)
    op.create_index(op.f('ix_timeline_point_results_owner_id'), 'timeline_point_results', ['owner_id'], unique=False)
    op.create_index(op.f('ix_timeline_point_results_point_id'), 'timeline_point_results', ['point_id'], unique=False)
    op.create_index(op.f('ix_timeline_point_results_timeline_id'), 'timeline_point_results', ['timeline_id'], unique=False)
    op.create_index(op.f('ix_timeline_point_results_workspace_id'), 'timeline_point_results', ['workspace_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_timeline_point_results_workspace_id'), table_name='timeline_point_results')
    op.drop_index(op.f('ix_timeline_point_results_timeline_id'), table_name='timeline_point_results')
    op.drop_index(op.f('ix_timeline_point_results_point_id'), table_name='timeline_point_results')
    op.drop_index(op.f('ix_timeline_point_results_owner_id'), table_name='timeline_point_results')
    op.drop_index(op.f('ix_timeline_point_results_created_at'), table_name='timeline_point_results')
    op.drop_table('timeline_point_results')
    # ### end Alembic commands ###
