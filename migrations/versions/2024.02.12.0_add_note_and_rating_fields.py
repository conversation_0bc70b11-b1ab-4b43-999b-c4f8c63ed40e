"""Add note and rating fields

Revision ID: 2024.02.12.0
Revises: 2024.01.26.1
Create Date: 2024-02-12 12:33:20.013550

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.02.12.0'
down_revision: Union[str, None] = '2024.01.26.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_detection_item_files_detection_item_id'), 'detection_item_files', ['detection_item_id'], unique=False)
    op.create_index(op.f('ix_detection_item_files_workspace_id'), 'detection_item_files', ['workspace_id'], unique=False)
    op.add_column('detection_items', sa.Column('rating', sa.Integer(), nullable=True))
    op.add_column('detection_items', sa.Column('note', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('detection_items', 'note')
    op.drop_column('detection_items', 'rating')
    op.drop_index(op.f('ix_detection_item_files_workspace_id'), table_name='detection_item_files')
    op.drop_index(op.f('ix_detection_item_files_detection_item_id'), table_name='detection_item_files')
    # ### end Alembic commands ###
