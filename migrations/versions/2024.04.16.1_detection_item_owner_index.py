"""detection item owner index

Revision ID: 2024.04.16.1
Revises: 2024.04.16.0
Create Date: 2024-04-16 17:38:39.762219

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.04.16.1'
down_revision: Union[str, None] = '2024.04.16.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_detection_items_owner_id'), 'detection_items', ['owner_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_detection_items_owner_id'), table_name='detection_items')
    # ### end Alembic commands ###
