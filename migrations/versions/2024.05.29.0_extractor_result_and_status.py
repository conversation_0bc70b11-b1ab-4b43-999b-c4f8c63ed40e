"""extractor result and status

Revision ID: 2024.05.29.0
Revises: 2024.05.27.0
Create Date: 2024-05-29 16:11:58.696251

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.05.29.0'
down_revision: Union[str, None] = '2024.05.27.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('extractor_results',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('output', sa.Text(), nullable=True),
    sa.Column('rating', sa.Integer(), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.Column('status', sa.Text(), nullable=True),
    sa.Column('workspace_id', sa.Integer(), nullable=True),
    sa.Column('extractor_id', sa.Integer(), nullable=True),
    sa.Column('config', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_extractor_results_created_at'), 'extractor_results', ['created_at'], unique=False)
    op.create_index(op.f('ix_extractor_results_extractor_id'), 'extractor_results', ['extractor_id'], unique=False)
    op.create_index(op.f('ix_extractor_results_workspace_id'), 'extractor_results', ['workspace_id'], unique=False)
    op.add_column('extractors', sa.Column('status', sa.String(length=255), nullable=True))
    op.add_column('extractors', sa.Column('has_updates', sa.Boolean(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('extractors', 'has_updates')
    op.drop_column('extractors', 'status')
    op.drop_index(op.f('ix_extractor_results_workspace_id'), table_name='extractor_results')
    op.drop_index(op.f('ix_extractor_results_extractor_id'), table_name='extractor_results')
    op.drop_index(op.f('ix_extractor_results_created_at'), table_name='extractor_results')
    op.drop_table('extractor_results')
    # ### end Alembic commands ###
