"""Add owner_id to tokens

Revision ID: 2024.01.18.1
Revises: 2024.01.18.0
Create Date: 2024-01-18 16:01:21.752872

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.01.18.1'
down_revision: Union[str, None] = '2024.01.18.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tokens', sa.Column('owner_id', sa.BigInteger(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tokens', 'owner_id')
    # ### end Alembic commands ###
