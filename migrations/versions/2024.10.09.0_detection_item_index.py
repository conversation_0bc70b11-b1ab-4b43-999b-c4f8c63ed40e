"""detection item index

Revision ID: 2024.10.09.0
Revises: 2024.10.08.0
Create Date: 2024-10-09 16:17:28.751325

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.10.09.0'
down_revision: Union[str, None] = '2024.10.08.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_detection_item_ws_det_id', 'detection_items', ['workspace_id', 'detection_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_detection_item_ws_det_id', table_name='detection_items')
    # ### end Alembic commands ###
