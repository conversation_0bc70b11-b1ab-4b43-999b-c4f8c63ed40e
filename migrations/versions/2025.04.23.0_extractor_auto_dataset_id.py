"""extractor auto dataset id

Revision ID: 2025.04.23.0
Revises: 2025.04.10.0
Create Date: 2025-04-23 17:20:21.476273

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.23.0'
down_revision: Union[str, None] = '2025.04.10.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('extractors', sa.Column('auto_dataset_id', sa.String(length=36), nullable=True))
    conn = op.get_bind()
    res = conn.execute(sa.text("SELECT id, config FROM extractors"))
    results = res.fetchall()
    extractors = [{'id': o[0], 'config': o[1]} for o in results]
    # Get all datasets
    res = conn.execute(sa.text("SELECT id, name FROM datasets"))
    results = res.fetchall()
    datasets = [{'id': o[0], 'name': o[1]} for o in results]
    dataset_map = {d['name']: d['id'] for d in datasets}
    for ext in extractors:
        # Get dataset name
        dataset_name = (ext['config'].get('index') or '').split('/')[0]
        if dataset_name:
            dataset_id = dataset_map.get(dataset_name)
            if dataset_id:
                conn.execute(sa.text(f"UPDATE extractors SET auto_dataset_id = '{dataset_id}' WHERE id = {ext['id']}"))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('extractors', 'auto_dataset_id')
    # ### end Alembic commands ###
