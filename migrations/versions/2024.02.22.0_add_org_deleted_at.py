"""add org deleted at

Revision ID: 2024.02.22.0
Revises: 2024.02.12.0
Create Date: 2024-02-22 11:55:38.587300

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.02.22.0'
down_revision: Union[str, None] = '2024.02.12.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organizations', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organizations', 'deleted_at')
    # ### end Alembic commands ###
