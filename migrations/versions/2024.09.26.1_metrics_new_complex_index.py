"""metrics new complex index

Revision ID: 2024.09.26.1
Revises: 2024.09.26.0
Create Date: 2024-09-26 16:18:39.668406

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.09.26.1'
down_revision: Union[str, None] = '2024.09.26.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('CREATE INDEX IF NOT EXISTS idx_metrics_workspace_app ON metrics(workspace_id, app_type, app_id, id DESC);')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('DROP INDEX IF EXISTS idx_metrics_workspace_app;')
    # ### end Alembic commands ###
