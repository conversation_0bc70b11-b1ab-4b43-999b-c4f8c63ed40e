"""metrics org id

Revision ID: 2025.04.04.0
Revises: 2025.03.07.0
Create Date: 2025-04-04 15:24:00.472496

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.04.0'
down_revision: Union[str, None] = '2025.03.07.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('metrics', sa.Column('org_id', sa.Integer(), nullable=True))
    op.execute("UPDATE metrics SET org_id = ws.org_id FROM workspaces ws WHERE ws.id = metrics.workspace_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
