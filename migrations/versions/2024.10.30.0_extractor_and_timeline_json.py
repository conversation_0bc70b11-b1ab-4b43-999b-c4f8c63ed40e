"""extractor and timeline json

Revision ID: 2024.10.30.0
Revises: 2024.10.28.1
Create Date: 2024-10-30 14:44:51.989143

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '2024.10.30.0'
down_revision: Union[str, None] = '2024.10.28.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'extractor_files',
        'status',
        existing_type=sa.VARCHAR(length=511),
        type_=sa.JSON(),
        existing_nullable=True,
        postgresql_using='status::json'
    )
    op.alter_column(
        'extractor_item_outputs',
        'config',
        existing_type=sa.TEXT(),
        type_=sa.JSON(),
        existing_nullable=True,
        postgresql_using='config::json'
    )
    op.alter_column(
        'extractor_items',
        'config',
        existing_type=sa.TEXT(),
        type_=sa.JSON(),
        existing_nullable=True,
        postgresql_using='config::json'
    )
    op.alter_column(
        'extractor_results',
        'status',
        existing_type=sa.TEXT(),
        type_=sa.JSON(),
        existing_nullable=True,
        postgresql_using='status::json'
    )
    op.alter_column(
        'extractor_results',
        'config',
        existing_type=sa.TEXT(),
        type_=sa.JSON(),
        existing_nullable=True,
        postgresql_using='config::json'
    )
    op.alter_column(
        'extractors',
        'status',
        existing_type=sa.VARCHAR(length=511),
        type_=sa.JSON(),
        existing_nullable=True,
        postgresql_using='status::json'
    )
    op.alter_column(
        'extractors',
        'config',
        existing_type=sa.TEXT(),
        type_=sa.JSON(),
        existing_nullable=True,
        postgresql_using='config::json'
    )
    op.alter_column(
        'timelines',
        'status',
        existing_type=sa.VARCHAR(length=511),
        type_=sa.JSON(),
        existing_nullable=True,
        postgresql_using='status::json'
    )
    op.alter_column(
        'timelines',
        'config',
        existing_type=sa.TEXT(),
        type_=sa.JSON(),
        existing_nullable=True,
        postgresql_using='config::json'
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('timelines', 'config',
                    existing_type=sa.JSON(),
                    type_=sa.TEXT(),
                    existing_nullable=True)
    op.alter_column('timelines', 'status',
                    existing_type=sa.JSON(),
                    type_=sa.VARCHAR(length=511),
                    existing_nullable=True)
    op.alter_column('extractors', 'config',
                    existing_type=sa.JSON(),
                    type_=sa.TEXT(),
                    existing_nullable=True)
    op.alter_column('extractors', 'status',
                    existing_type=sa.JSON(),
                    type_=sa.VARCHAR(length=511),
                    existing_nullable=True)
    op.alter_column('extractor_results', 'config',
                    existing_type=sa.JSON(),
                    type_=sa.TEXT(),
                    existing_nullable=True)
    op.alter_column('extractor_results', 'status',
                    existing_type=sa.JSON(),
                    type_=sa.TEXT(),
                    existing_nullable=True)
    op.alter_column('extractor_items', 'config',
                    existing_type=sa.JSON(),
                    type_=sa.TEXT(),
                    existing_nullable=True)
    op.alter_column('extractor_item_outputs', 'config',
                    existing_type=sa.JSON(),
                    type_=sa.TEXT(),
                    existing_nullable=True)
    op.alter_column('extractor_files', 'status',
                    existing_type=sa.JSON(),
                    type_=sa.VARCHAR(length=511),
                    existing_nullable=True)
    # ### end Alembic commands ###
