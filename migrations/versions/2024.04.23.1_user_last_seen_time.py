"""user last seen time

Revision ID: 2024.04.23.1
Revises: 2024.04.23.0
Create Date: 2024-04-23 16:53:04.930777

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.04.23.1'
down_revision: Union[str, None] = '2024.04.23.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('last_seen_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('users', sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('users', sa.Column('last_activity_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'last_activity_at')
    op.drop_column('users', 'last_login_at')
    op.drop_column('users', 'last_seen_at')
    # ### end Alembic commands ###
