"""dataset indexes

Revision ID: 2024.10.14.1
Revises: 2024.10.14.0
Create Date: 2024-10-14 15:24:13.215762

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.10.14.1'
down_revision: Union[str, None] = '2024.10.14.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('dataset_indexes_name_key', 'dataset_indexes', type_='unique')
    op.create_index('idx_dataset_indexes_dataset_name', 'dataset_indexes', ['dataset_id', 'name'], unique=True)
    op.drop_constraint('datasets_name_key', 'datasets', type_='unique')
    op.create_index('idx_datasets_org_name', 'datasets', ['org_id', 'name'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_datasets_org_name', table_name='datasets')
    op.create_unique_constraint('datasets_name_key', 'datasets', ['name'])
    op.drop_index('idx_dataset_indexes_dataset_name', table_name='dataset_indexes')
    op.create_unique_constraint('dataset_indexes_name_key', 'dataset_indexes', ['name'])
    # ### end Alembic commands ###
