"""metrics new index

Revision ID: 2025.03.07.0
Revises: 2025.03.03.0
Create Date: 2025-03-07 12:03:41.963318

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.03.07.0'
down_revision: Union[str, None] = '2025.03.03.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('CREATE INDEX IF NOT EXISTS idx_metrics_workspace_updated_type ON metrics (workspace_id, updated_at, type) INCLUDE (value);')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('DROP INDEX IF EXISTS idx_metrics_workspace_updated_type;')
    # ### end Alembic commands ###
