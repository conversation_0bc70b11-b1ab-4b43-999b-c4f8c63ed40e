"""alembic upgrade

Revision ID: 2024.07.23.0
Revises: 2024.07.15.0
Create Date: 2024-07-23 12:31:50.956424

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '2024.07.23.0'
down_revision: Union[str, None] = '2024.07.15.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('metrics', 'app_type',
                    existing_type=sa.VARCHAR(length=30),
                    type_=sa.String(length=20),
                    existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('metrics', 'app_type',
                    existing_type=sa.String(length=20),
                    type_=sa.VARCHAR(length=30),
                    existing_nullable=True)
    # ### end Alembic commands ###
