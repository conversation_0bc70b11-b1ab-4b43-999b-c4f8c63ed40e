"""detection metrics

Revision ID: 2025.05.08.0
Revises: 2025.05.07.1
Create Date: 2025-05-08 18:15:42.810564

"""
import json
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.05.08.0'
down_revision: Union[str, None] = '2025.05.07.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # op.add_column('detections', sa.Column('metrics', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.execute("ALTER TABLE detections ADD COLUMN IF NOT EXISTS metrics JSON")
    conn = op.get_bind()
    sql = sa.text("""SELECT m.id as id, m.app_id as app_id, m.object_id as object_id, m.type as type, m.value as value, m.updated_at as updated_at 
FROM metrics m 
JOIN (
    SELECT app_id, MAX(id) AS max_id
    FROM metrics
    WHERE app_type = 'detection'
    GROUP BY app_id
) subquery ON m.app_id = subquery.app_id AND m.id = subquery.max_id;""")
    result = conn.execute(sql)
    last_activity_by_app_id = {}
    for row in result.fetchall():
        last_activity_by_app_id[row[1]] = row[-1].strftime('%Y-%m-%d %H:%M:%S')

    sql2 = sa.text("""SELECT detection_id, count(*) as count from detection_items group by detection_id""")
    item_counts_by_app_id = {}
    result = conn.execute(sql2)
    for row in result.fetchall():
        item_counts_by_app_id[row[0]] = row[1]

    updated = 0
    detection_ids = conn.execute(sa.text("SELECT id FROM detections")).fetchall()
    for app_id in detection_ids:
        app_id = app_id[0]
        metrics = {
            "last_activity": last_activity_by_app_id.get(app_id, '2025-01-01 00:00:00'),
            "detection_item_count": item_counts_by_app_id.get(app_id, 0),
        }
        conn.execute(sa.text(f"UPDATE detections SET metrics = '{json.dumps(metrics)}' WHERE id = {app_id}"))
        updated += 1

    print(f"Updated {updated} detections")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('detections', 'metrics')
    # ### end Alembic commands ###
