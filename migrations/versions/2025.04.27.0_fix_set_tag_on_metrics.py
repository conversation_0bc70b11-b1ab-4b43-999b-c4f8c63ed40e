"""Fix set tag on metrics

Revision ID: 2025.04.27.0
Revises: 2025.04.24.1
Create Date: 2025-04-27 23:23:03.731055

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.27.0'
down_revision: Union[str, None] = '2025.04.24.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""
    WITH tags AS (
  SELECT m.id as message_id, o.id as result_id, m.chat_id, tags->>0 as tag FROM chat_messages m 
  JOIN chats ON chats.id=m.chat_id 
  RIGHT JOIN chat_message_outputs o ON o.chat_message_id = m.id WHERE tags is not NULL and tags::text != 'null'
) UPDATE metrics set tag = tags.tag FROM tags 
  WHERE metrics.app_id = tags.chat_id 
    AND metrics.object_id = cast(tags.result_id as varchar) 
    AND metrics.app_type = 'chat';
    """)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
