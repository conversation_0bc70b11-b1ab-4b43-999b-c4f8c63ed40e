"""add webhook config

Revision ID: 2024.09.25.1
Revises: 2024.09.25.0
Create Date: 2024-09-25 15:29:51.338193

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.09.25.1'
down_revision: Union[str, None] = '2024.09.25.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organization_webhooks', sa.Column('config', sa.JSON(), nullable=True))
    op.execute(
        """UPDATE organization_webhooks SET 
        config = '{"organization": {"all": true}, "workspace": {"all": true}, "dataset": {"all": true}}' """
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organization_webhooks', 'config')
    # ### end Alembic commands ###
