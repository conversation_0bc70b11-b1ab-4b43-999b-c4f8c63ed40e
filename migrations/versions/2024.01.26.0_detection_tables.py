"""detection tables

Revision ID: 2024.01.26.0
Revises: 2024.01.18.1
Create Date: 2024-01-26 11:45:59.546475

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.01.26.0'
down_revision: Union[str, None] = '2024.01.18.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('detection_item_files',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('detection_item_id', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('size', sa.Integer(), nullable=True),
    sa.Column('owner_id', sa.<PERSON>Integer(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_detection_item_files_detection_item_id'), 'detection_item_files', ['detection_item_id'], unique=False)
    op.create_index(op.f('ix_detection_item_files_workspace_id'), 'detection_item_files', ['workspace_id'], unique=False)
    op.create_table('detection_items',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('input', sa.Text(), nullable=True),
    sa.Column('output', sa.Text(), nullable=True),
    sa.Column('type', sa.String(length=30), nullable=True),
    sa.Column('status', sa.Text(), nullable=True),
    sa.Column('owner_id', sa.BigInteger(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=True),
    sa.Column('detection_id', sa.Integer(), nullable=True),
    sa.Column('config', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_detection_items_created_at'), 'detection_items', ['created_at'], unique=False)
    op.create_index(op.f('ix_detection_items_detection_id'), 'detection_items', ['detection_id'], unique=False)
    op.create_index(op.f('ix_detection_items_workspace_id'), 'detection_items', ['workspace_id'], unique=False)
    op.create_table('detections',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('type', sa.String(length=30), nullable=True),
    sa.Column('owner_id', sa.BigInteger(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=True),
    sa.Column('config', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_detections_created_at'), 'detections', ['created_at'], unique=False)
    op.create_index(op.f('ix_detections_workspace_id'), 'detections', ['workspace_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_detections_workspace_id'), table_name='detections')
    op.drop_index(op.f('ix_detections_created_at'), table_name='detections')
    op.drop_table('detections')
    op.drop_index(op.f('ix_detection_items_workspace_id'), table_name='detection_items')
    op.drop_index(op.f('ix_detection_items_detection_id'), table_name='detection_items')
    op.drop_index(op.f('ix_detection_items_created_at'), table_name='detection_items')
    op.drop_table('detection_items')
    op.drop_index(op.f('ix_detection_item_files_workspace_id'), table_name='detection_item_files')
    op.drop_index(op.f('ix_detection_item_files_detection_item_id'), table_name='detection_item_files')
    op.drop_table('detection_item_files')
    # ### end Alembic commands ###
