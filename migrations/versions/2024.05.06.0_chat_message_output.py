"""chat message output

Revision ID: 2024.05.06.0
Revises: 2024.05.02.0
Create Date: 2024-05-06 10:23:15.543296

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.05.06.0'
down_revision: Union[str, None] = '2024.05.02.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('chat_message_outputs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('chat_id', sa.Integer(), nullable=True),
    sa.Column('chat_message_id', sa.Integer(), nullable=True),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('owner_id', sa.<PERSON>ger(), nullable=False),
    sa.Column('role', sa.VARCHAR(length=10), nullable=True),
    sa.Column('status', sa.VARCHAR(length=20), nullable=True),
    sa.Column('context', sa.Text(), nullable=True),
    sa.Column('rating', sa.Integer(), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chat_message_outputs_chat_id'), 'chat_message_outputs', ['chat_id'], unique=False)
    op.create_index(op.f('ix_chat_message_outputs_chat_message_id'), 'chat_message_outputs', ['chat_message_id'], unique=False)
    op.create_index(op.f('ix_chat_message_outputs_created_at'), 'chat_message_outputs', ['created_at'], unique=False)
    op.create_index(op.f('ix_chat_message_outputs_status'), 'chat_message_outputs', ['status'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_chat_message_outputs_status'), table_name='chat_message_outputs')
    op.drop_index(op.f('ix_chat_message_outputs_created_at'), table_name='chat_message_outputs')
    op.drop_index(op.f('ix_chat_message_outputs_chat_message_id'), table_name='chat_message_outputs')
    op.drop_index(op.f('ix_chat_message_outputs_chat_id'), table_name='chat_message_outputs')
    op.drop_table('chat_message_outputs')
    # ### end Alembic commands ###
