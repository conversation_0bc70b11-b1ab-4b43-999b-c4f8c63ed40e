"""populate detection outputs

Revision ID: 2024.05.01.1
Revises: 2024.05.01.0
Create Date: 2024-05-01 16:11:39.967249

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.05.01.1'
down_revision: Union[str, None] = '2024.05.01.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "INSERT INTO detection_item_outputs "
        "(output, rating, note, status, workspace_id, detection_id, detection_item_id, config, created_at, updated_at) "
        "SELECT output, rating, note, status, workspace_id, detection_id, id, config, created_at, updated_at "
        "FROM detection_items"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
