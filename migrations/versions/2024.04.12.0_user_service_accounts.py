"""user service accounts

Revision ID: 2024.04.12.0
Revises: 2024.03.27.0
Create Date: 2024-04-12 17:37:28.250107

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.04.12.0'
down_revision: Union[str, None] = '2024.03.27.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_service_accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=True),
    sa.Column('service', sa.String(length=200), nullable=True),
    sa.Column('service_id', sa.String(length=200), nullable=True),
    sa.Column('service_name', sa.String(length=200), nullable=True),
    sa.Column('service_picture', sa.String(length=255), nullable=True),
    sa.Column('token', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_service_accounts_service'), 'user_service_accounts', ['service'], unique=False)
    op.create_index(op.f('ix_user_service_accounts_user_id'), 'user_service_accounts', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_service_accounts_user_id'), table_name='user_service_accounts')
    op.drop_index(op.f('ix_user_service_accounts_service'), table_name='user_service_accounts')
    op.drop_table('user_service_accounts')
    # ### end Alembic commands ###
