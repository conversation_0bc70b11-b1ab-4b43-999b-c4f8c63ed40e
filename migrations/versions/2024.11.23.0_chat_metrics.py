"""chat metrics

Revision ID: 2024.11.23.0
Revises: 2024.11.22.0
Create Date: 2024-11-23 12:10:24.356453

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from ira_chat.db import models
from ira_chat.db import base

# revision identifiers, used by Alembic.
revision: str = '2024.11.23.0'
down_revision: Union[str, None] = '2024.11.22.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chats', sa.Column('metrics', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    conn = op.get_bind()
    res = conn.execute(sa.text("SELECT id, title, workspace_id, owner_id, config, updated_at FROM chats;"))
    results = res.fetchall()
    chats = [{'id': o[0], 'title': o[1], 'workspace_id': o[2], 'owner_id': o[3], 'config': o[4], 'updated_at': o[5]} for o in results]

    res = conn.execute(sa.text("select app_id as chat_id, type, max(created_at) as last_activity from metrics where app_type = 'chat' and type = 'message' group by app_id, type;"))
    results = res.fetchall()
    last_activities = [{'chat_id': o[0], 'type': o[1], 'last_activity': o[2]} for o in results]
    last_activity_map = {o['chat_id']: o['last_activity'] for o in last_activities}

    res = conn.execute(sa.text(
        "select count(*) as message_count, chat_id from chat_messages group by chat_id;"
    ))
    results = res.fetchall()
    message_counts = [{'count': o[0], 'chat_id': o[1]} for o in results]
    message_count_map = {o['chat_id']: o['count'] for o in message_counts}

    for chat in chats:
        op.execute(
            models.Chat.__table__.update().where(models.Chat.id == chat['id']).values(
                metrics={
                    'message_count': message_count_map.get(chat['id'], 0),
                    'last_activity': base.date_to_string(last_activity_map.get(chat['id'], chat['updated_at']))
                }
            )
        )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chats', 'metrics')
    # ### end Alembic commands ###
