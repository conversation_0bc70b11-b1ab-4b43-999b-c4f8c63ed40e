"""timeline models

Revision ID: 2024.06.17.0
Revises: 2024.06.07.0
Create Date: 2024-06-17 11:24:51.146258

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.06.17.0'
down_revision: Union[str, None] = '2024.06.07.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('timeline_points',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=True),
    sa.Column('timeline_id', sa.Integer(), nullable=True),
    sa.Column('extractor_id', sa.Integer(), nullable=True),
    sa.<PERSON>umn('owner_id', sa.<PERSON>(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('config', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_timeline_points_created_at'), 'timeline_points', ['created_at'], unique=False)
    op.create_index(op.f('ix_timeline_points_extractor_id'), 'timeline_points', ['extractor_id'], unique=False)
    op.create_index(op.f('ix_timeline_points_owner_id'), 'timeline_points', ['owner_id'], unique=False)
    op.create_index(op.f('ix_timeline_points_timeline_id'), 'timeline_points', ['timeline_id'], unique=False)
    op.create_index(op.f('ix_timeline_points_workspace_id'), 'timeline_points', ['workspace_id'], unique=False)
    op.create_table('timelines',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=True),
    sa.Column('owner_id', sa.BigInteger(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=511), nullable=True),
    sa.Column('has_updates', sa.Boolean(), nullable=True),
    sa.Column('config', sa.Text(), nullable=True),
    sa.Column('data_schema', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_timelines_created_at'), 'timelines', ['created_at'], unique=False)
    op.create_index(op.f('ix_timelines_owner_id'), 'timelines', ['owner_id'], unique=False)
    op.create_index(op.f('ix_timelines_workspace_id'), 'timelines', ['workspace_id'], unique=False)
    op.add_column('extractors', sa.Column('hidden', sa.Boolean(), nullable=True))
    op.execute('UPDATE extractors set hidden = false')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('extractors', 'hidden')
    op.drop_index(op.f('ix_timelines_workspace_id'), table_name='timelines')
    op.drop_index(op.f('ix_timelines_owner_id'), table_name='timelines')
    op.drop_index(op.f('ix_timelines_created_at'), table_name='timelines')
    op.drop_table('timelines')
    op.drop_index(op.f('ix_timeline_points_workspace_id'), table_name='timeline_points')
    op.drop_index(op.f('ix_timeline_points_timeline_id'), table_name='timeline_points')
    op.drop_index(op.f('ix_timeline_points_owner_id'), table_name='timeline_points')
    op.drop_index(op.f('ix_timeline_points_extractor_id'), table_name='timeline_points')
    op.drop_index(op.f('ix_timeline_points_created_at'), table_name='timeline_points')
    op.drop_table('timeline_points')
    # ### end Alembic commands ###
