"""dataset file hash

Revision ID: 2024.10.30.1
Revises: 2024.10.30.0
Create Date: 2024-10-30 15:03:52.491165

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.10.30.1'
down_revision: Union[str, None] = '2024.10.30.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dataset_files', sa.Column('hash', sa.String(length=64), nullable=True))
    op.create_index(op.f('ix_dataset_files_hash'), 'dataset_files', ['hash'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_dataset_files_hash'), table_name='dataset_files')
    op.drop_column('dataset_files', 'hash')
    # ### end Alembic commands ###
