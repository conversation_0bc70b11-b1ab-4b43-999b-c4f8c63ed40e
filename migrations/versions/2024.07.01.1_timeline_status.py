"""timeline status

Revision ID: 2024.07.01.1
Revises: 2024.07.01.0
Create Date: 2024-07-01 15:07:30.113643

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.07.01.1'
down_revision: Union[str, None] = '2024.07.01.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('UPDATE timelines set status = format(\'{"status": "%s", "message": null}\', status)')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
