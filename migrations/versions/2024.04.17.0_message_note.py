"""message note

Revision ID: 2024.04.17.0
Revises: 2024.04.16.1
Create Date: 2024-04-17 11:25:22.583211

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.04.17.0'
down_revision: Union[str, None] = '2024.04.16.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_messages', sa.Column('note', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_messages', 'note')
    # ### end Alembic commands ###
