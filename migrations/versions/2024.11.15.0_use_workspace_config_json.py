"""use workspace config json

Revision ID: 2024.11.15.0
Revises: 2024.11.11.0
Create Date: 2024-11-15 12:31:13.563383

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.11.15.0'
down_revision: Union[str, None] = '2024.11.11.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'workspaces', 'config',
        existing_type=sa.TEXT(),
        type_=sa.JSON(),
        existing_nullable=True,
        postgresql_using="config::json",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'workspaces', 'config',
        existing_type=sa.JSON(),
        type_=sa.TEXT(),
        existing_nullable=True,
        postgresql_using="config::text"
    )
    # ### end Alembic commands ###
