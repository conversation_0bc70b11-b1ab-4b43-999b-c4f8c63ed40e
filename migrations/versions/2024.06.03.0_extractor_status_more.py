"""extractor status more

Revision ID: 2024.06.03.0
Revises: 2024.05.30.1
Create Date: 2024-06-03 18:47:56.371933

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.06.03.0'
down_revision: Union[str, None] = '2024.05.30.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE extractors ALTER COLUMN status TYPE varchar(511)")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
