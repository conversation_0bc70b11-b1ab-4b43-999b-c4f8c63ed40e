"""dataset type

Revision ID: 2024.10.28.0
Revises: 2024.10.24.0
Create Date: 2024-10-28 16:04:24.862693

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.10.28.0'
down_revision: Union[str, None] = '2024.10.24.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('datasets', sa.Column('scope', sa.String(length=20), nullable=True))
    op.execute("UPDATE datasets SET scope = 'general'")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('datasets', 'scope')
    # ### end Alembic commands ###
