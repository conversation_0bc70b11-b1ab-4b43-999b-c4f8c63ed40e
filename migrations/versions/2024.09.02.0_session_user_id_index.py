"""session user id index

Revision ID: 2024.09.02.0
Revises: 2024.08.29.0
Create Date: 2024-09-02 23:27:37.940521

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.09.02.0'
down_revision: Union[str, None] = '2024.08.29.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_sessions_user_id'), 'sessions', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_sessions_user_id'), table_name='sessions')
    # ### end Alembic commands ###
