"""message cache

Revision ID: 2025.06.18.0
Revises: 2025.05.12.0
Create Date: 2025-06-18 15:54:30.532781

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.06.18.0'
down_revision: Union[str, None] = '2025.05.12.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('chat_messages_cached',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('dataset_id', sa.String(length=36), nullable=True),
    sa.Column('org_id', sa.Integer(), nullable=True),
    sa.Column('hash', sa.String(length=64), nullable=True),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('output', sa.Text(), nullable=True),
    sa.Column('context', postgresql.JSON(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chat_messages_cached_created_at'), 'chat_messages_cached', ['created_at'], unique=False)
    op.create_index(op.f('ix_chat_messages_cached_dataset_id'), 'chat_messages_cached', ['dataset_id'], unique=False)
    op.create_index(op.f('ix_chat_messages_cached_hash'), 'chat_messages_cached', ['hash'], unique=False)
    op.create_index(op.f('ix_chat_messages_cached_org_id'), 'chat_messages_cached', ['org_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_chat_messages_cached_org_id'), table_name='chat_messages_cached')
    op.drop_index(op.f('ix_chat_messages_cached_hash'), table_name='chat_messages_cached')
    op.drop_index(op.f('ix_chat_messages_cached_dataset_id'), table_name='chat_messages_cached')
    op.drop_index(op.f('ix_chat_messages_cached_created_at'), table_name='chat_messages_cached')
    op.drop_table('chat_messages_cached')
    # ### end Alembic commands ###
