"""extractor item drop config

Revision ID: 2025.02.04.1
Revises: 2025.02.04.0
Create Date: 2025-02-04 17:08:24.871375

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.02.04.1'
down_revision: Union[str, None] = '2025.02.04.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('extractor_item_outputs', 'config')
    op.drop_column('extractor_items', 'config')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('extractor_items', sa.Column('config', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('extractor_item_outputs', sa.Column('config', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
