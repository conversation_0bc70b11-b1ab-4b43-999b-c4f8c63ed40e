"""metric extra json

Revision ID: 2024.09.30.0
Revises: 2024.09.26.1
Create Date: 2024-09-30 14:02:27.434654

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.09.30.0'
down_revision: Union[str, None] = '2024.09.26.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""UPDATE metrics SET extra = (extra #>> '{}')::json;""")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
