"""Create init migration

Revision ID: 2024.01.11.0
Revises: 
Create Date: 2024-01-11 11:54:32.349155

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.engine import reflection

# revision identifiers, used by Alembic.
revision: str = '2024.01.11.0'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    inspector = reflection.Inspector.from_engine(conn)
    tables = inspector.get_table_names()
    if 'sessions' in tables:
        # no need to init tables
        return

    op.create_table(
        'chat_messages',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chat_id', sa.Integer(), nullable=True),
        sa.Column('content', sa.Text(), nullable=True),
        sa.Column('context', sa.Text(), nullable=True),
        sa.Column('owner_id', sa.BigInteger(), nullable=False),
        sa.Column('role', sa.VARCHAR(length=10), nullable=True),
        sa.Column('status', sa.VARCHAR(length=20), nullable=True),
        sa.Column('rating', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chat_messages_chat_id'), 'chat_messages', ['chat_id'], unique=False)
    op.create_index(op.f('ix_chat_messages_created_at'), 'chat_messages', ['created_at'], unique=False)
    op.create_index(op.f('ix_chat_messages_status'), 'chat_messages', ['status'], unique=False)
    op.create_table(
        'chat_suggestions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chat_id', sa.Integer(), nullable=True),
        sa.Column('message_id', sa.Integer(), nullable=False),
        sa.Column('content', sa.Text(), nullable=True),
        sa.Column('status', sa.VARCHAR(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chat_suggestions_chat_id'), 'chat_suggestions', ['chat_id'], unique=False)
    op.create_index(op.f('ix_chat_suggestions_message_id'), 'chat_suggestions', ['message_id'], unique=False)
    op.create_table(
        'chats',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('title', sa.VARCHAR(length=255), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('owner_id', sa.BigInteger(), nullable=False),
        sa.Column('workspace_id', sa.Integer(), nullable=True),
        sa.Column('config', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chats_created_at'), 'chats', ['created_at'], unique=False)
    op.create_index(op.f('ix_chats_workspace_id'), 'chats', ['workspace_id'], unique=False)
    op.create_table(
        'file_embeddings',
        sa.Column('id', sa.String(length=60), nullable=False),
        sa.Column('file_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_file_embeddings_created_at'), 'file_embeddings', ['created_at'], unique=False)
    op.create_index(op.f('ix_file_embeddings_file_id'), 'file_embeddings', ['file_id'], unique=False)
    op.create_table(
        'files',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=True),
        sa.Column('size', sa.Integer(), nullable=True),
        sa.Column('status', sa.String(length=255), nullable=True),
        sa.Column('title', sa.String(length=255), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('owner_id', sa.BigInteger(), nullable=False),
        sa.Column('workspace_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_files_created_at'), 'files', ['created_at'], unique=False)
    op.create_index(op.f('ix_files_workspace_id'), 'files', ['workspace_id'], unique=False)
    op.create_table(
        'group_users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('workspace_id', sa.Integer(), nullable=True),
        sa.Column('user_id', sa.BigInteger(), nullable=True),
        sa.Column('user_login', sa.String(length=200), nullable=True),
        sa.Column('group_id', sa.Integer(), nullable=True),
        sa.Column('confirmed', sa.Boolean(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_group_users_group_id'), 'group_users', ['group_id'], unique=False)
    op.create_index(op.f('ix_group_users_user_id'), 'group_users', ['user_id'], unique=False)
    op.create_index(op.f('ix_group_users_workspace_id'), 'group_users', ['workspace_id'], unique=False)
    op.create_table(
        'groups',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('workspace_id', sa.Integer(), nullable=True),
        sa.Column('name', sa.String(length=63), nullable=True),
        sa.Column('permissions', sa.BigInteger(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_groups_workspace_id'), 'groups', ['workspace_id'], unique=False)
    op.create_table(
        'metrics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('value', sa.Float(), nullable=True),
        sa.Column('type', sa.String(length=20), nullable=True),
        sa.Column('workspace_id', sa.Integer(), nullable=True),
        sa.Column('chat_id', sa.Integer(), nullable=True),
        sa.Column('extra', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_metrics_chat_id'), 'metrics', ['chat_id'], unique=False)
    op.create_index(op.f('ix_metrics_type'), 'metrics', ['type'], unique=False)
    op.create_index(op.f('ix_metrics_workspace_id'), 'metrics', ['workspace_id'], unique=False)
    op.create_table(
        'organization_domains',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('domain', sa.String(length=200), nullable=False),
        sa.Column('org_id', sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('domain')
    )
    op.create_index(op.f('ix_organization_domains_org_id'), 'organization_domains', ['org_id'], unique=False)
    op.create_table(
        'organization_users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('org_id', sa.Integer(), nullable=True),
        sa.Column('user_id', sa.BigInteger(), nullable=True),
        sa.Column('user_login', sa.String(length=200), nullable=True),
        sa.Column('role_id', sa.Integer(), nullable=True),
        sa.Column('confirmed', sa.Boolean(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_organization_users_org_id'), 'organization_users', ['org_id'], unique=False)
    op.create_index(op.f('ix_organization_users_user_id'), 'organization_users', ['user_id'], unique=False)
    op.create_table(
        'organizations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=True),
        sa.Column('display_name', sa.String(length=200), nullable=True),
        sa.Column('description', sa.String(length=255), nullable=True),
        sa.Column('config', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_table(
        'sessions',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('ttl', sa.DateTime(timezone=True), nullable=True),
        sa.Column('admin', sa.Boolean(), nullable=True),
        sa.Column('user_id', sa.BigInteger(), nullable=False),
        sa.Column('extra', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_table(
        'user_confirms',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('user_id', sa.BigInteger(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('expiry_time', sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_table(
        'user_invites',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('from_user_id', sa.BigInteger(), nullable=True),
        sa.Column('to_user_id', sa.BigInteger(), nullable=True),
        sa.Column('to_user_login', sa.String(length=200), nullable=True),
        sa.Column('to_workspace_id', sa.Integer(), nullable=True),
        sa.Column('to_group_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('expiry_time', sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_table(
        'user_tokens',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.BigInteger(), nullable=True),
        sa.Column('token', sa.String(length=36), nullable=True),
        sa.Column('workspace_id', sa.Integer(), nullable=True),
        sa.Column('username', sa.String(length=200), nullable=True),
        sa.Column('description', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('expiry_time', sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_tokens_token'), 'user_tokens', ['token'], unique=False)
    op.create_index(op.f('ix_user_tokens_workspace_id'), 'user_tokens', ['workspace_id'], unique=False)
    op.create_table(
        'users',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=True),
        sa.Column('info', sa.Text(), nullable=True),
        sa.Column('login', sa.String(length=200), nullable=True),
        sa.Column('password', sa.String(length=200), nullable=True),
        sa.Column('confirmed', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_table(
        'workspaces',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('org_id', sa.Integer(), nullable=True),
        sa.Column('name', sa.String(length=200), nullable=True),
        sa.Column('display_name', sa.String(length=200), nullable=True),
        sa.Column('description', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('config', sa.Text(), nullable=True),
        sa.Column('type', sa.VARCHAR(length=30), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('workspaces')
    op.drop_table('users')
    op.drop_index(op.f('ix_user_tokens_workspace_id'), table_name='user_tokens')
    op.drop_index(op.f('ix_user_tokens_token'), table_name='user_tokens')
    op.drop_table('user_tokens')
    op.drop_table('user_invites')
    op.drop_table('user_confirms')
    op.drop_table('sessions')
    op.drop_table('organizations')
    op.drop_index(op.f('ix_organization_users_user_id'), table_name='organization_users')
    op.drop_index(op.f('ix_organization_users_org_id'), table_name='organization_users')
    op.drop_table('organization_users')
    op.drop_index(op.f('ix_organization_domains_org_id'), table_name='organization_domains')
    op.drop_table('organization_domains')
    op.drop_index(op.f('ix_metrics_workspace_id'), table_name='metrics')
    op.drop_index(op.f('ix_metrics_type'), table_name='metrics')
    op.drop_index(op.f('ix_metrics_chat_id'), table_name='metrics')
    op.drop_table('metrics')
    op.drop_index(op.f('ix_groups_workspace_id'), table_name='groups')
    op.drop_table('groups')
    op.drop_index(op.f('ix_group_users_workspace_id'), table_name='group_users')
    op.drop_index(op.f('ix_group_users_user_id'), table_name='group_users')
    op.drop_index(op.f('ix_group_users_group_id'), table_name='group_users')
    op.drop_table('group_users')
    op.drop_index(op.f('ix_files_workspace_id'), table_name='files')
    op.drop_index(op.f('ix_files_created_at'), table_name='files')
    op.drop_table('files')
    op.drop_index(op.f('ix_file_embeddings_file_id'), table_name='file_embeddings')
    op.drop_index(op.f('ix_file_embeddings_created_at'), table_name='file_embeddings')
    op.drop_table('file_embeddings')
    op.drop_index(op.f('ix_chats_workspace_id'), table_name='chats')
    op.drop_index(op.f('ix_chats_created_at'), table_name='chats')
    op.drop_table('chats')
    op.drop_index(op.f('ix_chat_suggestions_message_id'), table_name='chat_suggestions')
    op.drop_index(op.f('ix_chat_suggestions_chat_id'), table_name='chat_suggestions')
    op.drop_table('chat_suggestions')
    op.drop_index(op.f('ix_chat_messages_status'), table_name='chat_messages')
    op.drop_index(op.f('ix_chat_messages_created_at'), table_name='chat_messages')
    op.drop_index(op.f('ix_chat_messages_chat_id'), table_name='chat_messages')
    op.drop_table('chat_messages')
    # ### end Alembic commands ###
