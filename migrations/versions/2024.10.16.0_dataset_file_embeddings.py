"""dataset file embeddings

Revision ID: 2024.10.16.0
Revises: 2024.10.15.0
Create Date: 2024-10-16 13:31:34.879388

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.10.16.0'
down_revision: Union[str, None] = '2024.10.15.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('dataset_file_embeddings',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('dataset_file_id', sa.String(length=36), nullable=True),
    sa.Column('index_id', sa.String(length=36), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.<PERSON>KeyConstraint('id')
    )
    op.create_index(op.f('ix_dataset_file_embeddings_dataset_file_id'), 'dataset_file_embeddings', ['dataset_file_id'], unique=False)
    op.create_index(op.f('ix_dataset_file_embeddings_index_id'), 'dataset_file_embeddings', ['index_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_dataset_file_embeddings_index_id'), table_name='dataset_file_embeddings')
    op.drop_index(op.f('ix_dataset_file_embeddings_dataset_file_id'), table_name='dataset_file_embeddings')
    op.drop_table('dataset_file_embeddings')
    # ### end Alembic commands ###
