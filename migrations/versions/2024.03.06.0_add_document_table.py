"""add document table

Revision ID: 2024.03.06.0
Revises: 2024.03.04.0
Create Date: 2024-03-06 12:45:35.583931

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.03.06.0'
down_revision: Union[str, None] = '2024.03.04.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('documents',
    sa.Column('id', sa.String(length=60), nullable=False),
    sa.Column('file_id', sa.Integer(), nullable=True),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('doc_metadata', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_documents_file_id'), 'documents', ['file_id'], unique=False)
    op.drop_index('ix_file_embeddings_created_at', table_name='file_embeddings')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_file_embeddings_created_at', 'file_embeddings', ['created_at'], unique=False)
    op.drop_index(op.f('ix_documents_file_id'), table_name='documents')
    op.drop_table('documents')
    # ### end Alembic commands ###
