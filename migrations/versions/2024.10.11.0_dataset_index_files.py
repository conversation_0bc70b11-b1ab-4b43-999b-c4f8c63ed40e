"""dataset index files

Revision ID: 2024.10.11.0
Revises: 2024.10.09.1
Create Date: 2024-10-11 17:58:14.568034

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.10.11.0'
down_revision: Union[str, None] = '2024.10.09.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('dataset_files',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('size', sa.Integer(), nullable=True),
    sa.Column('status', sa.JSON(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('owner_id', sa.<PERSON>Integer(), nullable=False),
    sa.Column('dataset_id', sa.Integer(), nullable=True),
    sa.Column('org_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_dataset_files_created_at'), 'dataset_files', ['created_at'], unique=False)
    op.create_index(op.f('ix_dataset_files_dataset_id'), 'dataset_files', ['dataset_id'], unique=False)
    op.create_index(op.f('ix_dataset_files_org_id'), 'dataset_files', ['org_id'], unique=False)
    op.create_table('dataset_indexes',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('display_name', sa.String(length=255), nullable=True),
    sa.Column('total_size', sa.Integer(), nullable=True),
    sa.Column('status', sa.JSON(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('owner_id', sa.BigInteger(), nullable=False),
    sa.Column('org_id', sa.Integer(), nullable=True),
    sa.Column('dataset_id', sa.Integer(), nullable=True),
    sa.Column('llm_type', sa.String(length=255), nullable=True),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_dataset_indexes_created_at'), 'dataset_indexes', ['created_at'], unique=False)
    op.create_index(op.f('ix_dataset_indexes_dataset_id'), 'dataset_indexes', ['dataset_id'], unique=False)
    op.create_index(op.f('ix_dataset_indexes_org_id'), 'dataset_indexes', ['org_id'], unique=False)
    op.create_table('datasets',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('display_name', sa.String(length=255), nullable=True),
    sa.Column('total_size', sa.Integer(), nullable=True),
    sa.Column('status', sa.JSON(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('owner_id', sa.BigInteger(), nullable=False),
    sa.Column('org_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_datasets_created_at'), 'datasets', ['created_at'], unique=False)
    op.create_index(op.f('ix_datasets_org_id'), 'datasets', ['org_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_datasets_org_id'), table_name='datasets')
    op.drop_index(op.f('ix_datasets_created_at'), table_name='datasets')
    op.drop_table('datasets')
    op.drop_index(op.f('ix_dataset_indexes_org_id'), table_name='dataset_indexes')
    op.drop_index(op.f('ix_dataset_indexes_dataset_id'), table_name='dataset_indexes')
    op.drop_index(op.f('ix_dataset_indexes_created_at'), table_name='dataset_indexes')
    op.drop_table('dataset_indexes')
    op.drop_index(op.f('ix_dataset_files_org_id'), table_name='dataset_files')
    op.drop_index(op.f('ix_dataset_files_dataset_id'), table_name='dataset_files')
    op.drop_index(op.f('ix_dataset_files_created_at'), table_name='dataset_files')
    op.drop_table('dataset_files')
    # ### end Alembic commands ###
