"""point result cols

Revision ID: 2024.07.01.2
Revises: 2024.07.01.1
Create Date: 2024-07-01 17:09:00.295375

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2024.07.01.2'
down_revision: Union[str, None] = '2024.07.01.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('timeline_points', sa.Column('output', sa.Text(), nullable=True))
    op.add_column('timeline_points', sa.Column('rating', sa.Integer(), nullable=True))
    op.add_column('timeline_points', sa.Column('note', sa.Text(), nullable=True))

    op.execute(
        "UPDATE timeline_points SET output=results.output "
        "FROM (select distinct on(point_id) point_id, output, updated_at from timeline_point_results order by point_id, updated_at desc) AS results "
        "WHERE timeline_points.id = results.point_id;"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('timeline_points', 'note')
    op.drop_column('timeline_points', 'rating')
    op.drop_column('timeline_points', 'output')
    # ### end Alembic commands ###
