"""meta field for apps

Revision ID: 2025.05.12.0
Revises: 2025.05.08.0
Create Date: 2025-05-12 13:39:11.798735

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.05.12.0'
down_revision: Union[str, None] = '2025.05.08.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chats', sa.Column('meta', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column('detections', sa.Column('meta', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column('extractors', sa.Column('meta', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column('timelines', sa.Column('meta', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('timelines', 'meta')
    op.drop_column('extractors', 'meta')
    op.drop_column('detections', 'meta')
    op.drop_column('chats', 'meta')
    # ### end Alembic commands ###
