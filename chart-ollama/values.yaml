ollama:
  Image:
    Name: ollama/ollama
    Tag: 0.1.23
  data:
    enabled: true
    size: 100G
    storageClass: standard
  replicas: 1
  resources:
    limits:
      nvidia.com/gpu: 1
  envVars:
    - name: some
      value: 'var'

  defaultModels:
    - 'mistral'
    - 'llava:34b'

  nodeSelector:
     llm: "true"
#  tolerations: []
  tolerations:
  - key: llm
    value: "true"
    effect: NoExecute