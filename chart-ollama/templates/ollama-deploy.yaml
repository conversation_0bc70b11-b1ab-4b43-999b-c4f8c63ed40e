apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "ollama.fullname" . }}
  labels:
    {{- include "ollama.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.ollama.replicas }}
  selector:
    matchLabels:
      {{- include "ollama.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "ollama.selectorLabels" . | nindent 8 }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.ollama.Image.Name }}:{{ .Values.ollama.Image.Tag }}"
          imagePullPolicy: IfNotPresent
          env:
          {{- range .Values.ollama.envVars }}
            - name: {{ .name }}
              value: {{ .value | quote }}
          {{- end }}
          ports:
          - name: ollama
            containerPort: 11434
            protocol: TCP

          resources:
            {{- toYaml .Values.ollama.resources | nindent 12 }}
          volumeMounts:
            - name: ollama-storage
              mountPath: /root/.ollama
          {{- if .Values.ollama.defaultModels }}
          lifecycle:
            postStart:
              exec:
                command:
                - "/bin/bash"
                - "-c"
                - >
                  {{ range $model := .Values.ollama.defaultModels }}ollama pull {{ $model }}; {{ end }}
          {{- end }}
      {{- with .Values.ollama.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.ollama.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
      - name: ollama-storage
        {{- if .Values.ollama.data.enabled }}
        persistentVolumeClaim:
          claimName: ollama-storage
        {{ else }}
        emptyDir: {}
        {{ end }}