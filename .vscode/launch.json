{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "gpt-4o",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "args": [
        "--collection",
        "ProjectParla",
        "--model",
        "gpt-4o",
        "--vision-model",
        "gpt-4o",
        "--path", 
        "./scripts/test_files/1-ProjectParla",
        "--mode",
        "index"
      ],
      "console": "integratedTerminal",
      "justMyCode": false    
    },
    {
      "name": "gpt-4-preview",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "args": [
        "--collection",
        "ProjectParla",
        "--model",
        "gpt-4-1106-preview",
        "--vision-model",
        "gpt-4-vision-preview",
        "--path", 
        "./scripts/test_files/test",
        "--mode",
        "index"
      ],
      "console": "integratedTerminal",
      "justMyCode": false    
    },
    {
      "name": "gemini-1.5-flash",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "args": [
        "--collection",
        "ProjectParla",
        "--model",
        "gemini-1.5-flash-latest",
        "--vision-model",
        "gemini-1.5-flash-latest",
        "--path", 
        "./scripts/test_files/test",
        "--mode",
        "index"
      ],
      "console": "integratedTerminal",
      "justMyCode": false    
    },
    {
      "name": "gemini-1.5-pro",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "args": [
        "--collection",
        "ProjectParla",
        "--model",
        "gemini-1.5-pro-latest",
        "--vision-model",
        "gemini-1.5-pro-latest",
        "--path", 
        "./scripts/test_files/test",
        "--mode",
        "index"
      ],
      "console": "integratedTerminal",
      "justMyCode": false    
    },
    {
      "name": "claude-3-opus-20240229",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "args": [
        "--collection",
        "ProjectParla",
        "--model",
        "claude-3-opus-20240229",
        "--vision-model",
        "claude-3-opus-20240229",
        "--path", 
        "./scripts/test_files/test",
        "--mode",
        "index"
      ],
      "console": "integratedTerminal",
      "justMyCode": false    
    },
    {
      "name": "claude-3-sonnet-20240229",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "args": [
        "--collection",
        "ProjectParla",
        "--model",
        "claude-3-sonnet-20240229",
        "--vision-model",
        "claude-3-sonnet-20240229",
        "--path", 
        "./scripts/test_files/test",
        "--mode",
        "index"
      ],
      "console": "integratedTerminal",
      "justMyCode": false    
    },
    {
      "name": "claude-3-haiku-20240307",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "args": [
        "--collection",
        "ProjectParla",
        "--model",
        "claude-3-haiku-20240307",
        "--vision-model",
        "claude-3-haiku-20240307",
        "--path", 
        "./scripts/test_files/test",
        "--mode",
        "index"
      ],
      "console": "integratedTerminal",
      "justMyCode": false    
    },
    {
      "name": "Python: Current File",
      "type": "python",
      "request": "launch",
      "program": "${file}",
      "args": [
        "--dir",
        "./test_files",
        "--evaluate",
        "--language",
        "french",
        "--force-language",
        "--model",
        "gpt-4-1106-preview"
      ],
      "console": "integratedTerminal",
      "justMyCode": true
    }
  ]
}
