# ira-chat-api

## Prerequisites

- **Python**: Version 3.10+
- **PostgreSQL**: Available locally or remotely
  - [PostgreSQL Installation Guide (Ubuntu)](https://ubuntu.com/server/docs/databases-postgresql)
  - [PostgreSQL Installation Guide (DigitalOcean)](https://www.digitalocean.com/community/tutorials/how-to-install-postgresql-on-ubuntu-22-04-quickstart)
  - Create a database: `createdb ira` (ensure the `psql` command works)
- **Qdrant**: Vectorstore for storing embeddings
  - [Qdrant Docker Installation](https://qdrant.com/docs/installation/docker)

### Quick installation

For quick installation instructions, please refer to the **[Quick Setup Guide](#quick-setup)** section below.

### Option: Run PostgreSQL in Docker

#### Using Script

Run PostgreSQL with the supplied script (background mode):

```bash
./run_postgres.sh
```

To run it interactively and delete the container upon interrupt:

```bash
./run_postgres.sh -i
```

This will create a PostgreSQL DBMS with the default password `password`.

#### Using Docker Directly

```bash
docker run -p 5432:5432 --name postgres -e POSTGRES_PASSWORD=password -e POSTGRES_USER=postgres -e POSTGRES_DB=ira \
 -e PGDATA=/var/lib/postgresql/data/pgdata --rm -it -v $HOME/pgdata:/var/lib/postgresql/data/pgdata:z postgres:16.6
```

### Run Qdrant in Docker

```bash
docker run -p 6333:6333 -p 6334:6334 --name qdrant --rm -it -v $HOME/qdrant_storage:/qdrant/storage:z qdrant/qdrant:latest
```

## Initialize the Database

```bash
PGPASSWORD=password PGUSER=postgres createdb ira
```

## Install Requirements

Install all the requirements in a virtual environment of your choice (`conda`, `virtualenv`, or plain `pip`):

```bash
pip install -r requirements.txt
```

## Run Locally

### Configuring Environment Variables

Set the following environment variables:

```bash
# Database environment variables
export PGHOST=localhost
export PGUSER=postgres
export PGPASSWORD=password
export PGDATABASE=ira
# Admin Auth key
export AUTH_KEY="random-admin-key"
# Base URL
export BASE_URL=http://localhost:8083
# Default vectorstore type
export VECTORSTORE=qdrant
# Environment prefix to use the same storage/vectorstore in multiple environments
export ENV_PREFIX=local
# Encrypt key setting
export BLOWFISH_SALT=default-key
# Default LLM 
export LLM_MODEL_NAME=gpt-4o-mini
```

To simplify, you can keep all these variables in a file like `~/.irarc` and activate them with one command:

```bash
. ~/.irarc
```

### Run Server

```bash
python ira_chat/main.py
```

### Important: Before the first request

Before starting, create the first organization with a domain to access the API:

```bash
org_name=my-local-org
psql ira -c "INSERT INTO organizations (name, display_name, created_at, updated_at) values ('$org_name', '$org_name', now(), now())"
psql ira -c "INSERT INTO organization_domains (domain, org_id) values ('$org_name.localhost', (select id from organizations where name = '$org_name'))"
```

To access the organization locally with a domain in the form `xxx.localhost`, add this host to the `/etc/hosts` file:

```
127.0.0.1 my-local-org.localhost
```

### Run Server with Database Debug

```bash
DEBUG_DB=true python ira_chat/main.py
```

### Run Server with Prompt Debug

```bash
DEBUG_PROMPT=true python ira_chat/main.py
```

Now the API server is available at `http://localhost:8083` and the organization API is available at `http://my-local-org.localhost:8083`.

## Run Using Docker (Adjust Environment Variables)

```bash
docker run -it --rm -e PGHOST=************* -e PGUSER=postgres -e PGPASSWORD=password \
 -e PGDATABASE=ira kuberlab/ira-chat-api:latest
```

## Swagger Documentation

Swagger documentation is available at [http://localhost:8083/docs](http://localhost:8083/docs).

## Quick Setup

To speed up the process, you can combine some steps and use scripts:

1. **Run PostgreSQL and Initialize Database**
   ```bash
   ./run_postgres.sh && sleep 5 && PGPASSWORD=password createdb ira
   ```

2. **Install Requirements and Set Environment Variables**
   ```bash
   python -m venv venv && source venv/bin/activate && pip install -r requirements.txt
   echo 'export PGHOST=localhost
   export PGUSER=postgres
   export PGPASSWORD=password
   export PGDATABASE=ira
   export AUTH_KEY="random-admin-key"
   export BASE_URL=http://localhost:8083
   export VECTORSTORE=qdrant
   export ENV_PREFIX=local
   export BLOWFISH_SALT=default-key
   export LLM_MODEL_NAME=gpt-4o-mini' > ~/.irarc && . ~/.irarc
   ```

3. **Create Organization and Update Hosts File**
   ```bash
   org_name=my-local-org
   psql ira -c "INSERT INTO organizations (name, display_name, created_at, updated_at) values ('$org_name', '$org_name', now(), now())"
   psql ira -c "INSERT INTO organization_domains (domain, org_id) values ('$org_name.localhost', (select id from organizations where name = '$org_name'))"
   echo '127.0.0.1 my-local-org.localhost' | sudo tee -a /etc/hosts
   ```

4. **Run the Server**
   ```bash
   python ira_chat/main.py
   ```

Then the service should be running locally. The API server will be available at `http://localhost:8083`, 
and the organization API will be accessible at `http://my-local-org.localhost:8083`.

```bash
curl -v http://my-local-org.localhost:8083/api/v1/auth/info
```
