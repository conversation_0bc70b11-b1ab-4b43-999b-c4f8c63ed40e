import json
import re
import sys

import pandas as pd

csv_file = sys.argv[1]

df = pd.read_csv(csv_file)
df['total_tokens'] = df.usage_metadata.apply(lambda x: json.loads(x.replace("'", '"'))['total_tokens'])
df['output_tokens'] = df.usage_metadata.apply(lambda x: json.loads(x.replace("'", '"'))['output_tokens'])

df['output_len'] = df['content'].apply(len)
df['json_output_exists'] = df['content'].apply(lambda x: len(re.findall(r'\{.*}', x, re.DOTALL)))
json_num = len(df[df['json_output_exists'] > 0])

df['tokens_per_second'] = df['total_tokens'] / df['process_time']
df['out_tokens_per_second'] = df['output_tokens'] / df['process_time']
print(f"Mean answer length = {df['output_len'].mean()}")
print(f'Mean process time = {df["process_time"].mean()}')
print(f"Mean total tokens per second = {df['tokens_per_second'].mean()}")
print(f"Mean output tokens per second = {df['out_tokens_per_second'].mean()}")
print(f"JSON output exists = {json_num} out of {len(df)}; {json_num / len(df) * 100:.2f}%")
print()
#print(df['tokens_per_second'].describe())
