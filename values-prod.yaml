api:
  Image:
    Name: kuberlab/ira-chat-api
    Tag: 1.3.153
  baseUrl: https://hotline.kibernetika.io
  db:
    dbname: ira
    enabled: true
    host: ira-chat-db
    password: Privet1961sneg
    user: postgres
  dns:
  - hotline.kibernetika.io
  envPrefix: 'prod'
  envVars:
  - name: dummy
    value: value
  - name: VECTORSTORE
    value: qdrant
  - name: VECTORSTORE_ON_DISK
    value: "true"
  - name: LANGFUSE_SECRET_KEY1
    value: ******************************************
  - name: LANGFUSE_PUBLIC_KEY1
    value: pk-lf-6712fd7e-46d7-4e7e-a5e3-40681affb120
  redirectDomain: ''
  replicas: 6
credentials:
  admins: WzEsNCw1LDZdCg==
  smtp: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  google: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
crypto: jS2fKKFSlfco3mxLdfckd0ew
resources:
   limits:
     memory: 4Gi
   requests:
     memory: 1Gi
qdrant:
  nameOverride: "qdrant"
  fullnameOverride: "qdrant"
  resources:
    limits:
  #   cpu: 100m
      memory: 8Gi
    requests:
  #   cpu: 100m
      memory: 2Gi
  persistence:
    size: 50G
    storageClass: premium-rwo

  image:
    repository: docker.io/qdrant/qdrant
    pullPolicy: IfNotPresent
    tag: "v1.13.2"

  # api key for authentication at qdrant
  # false: no api key will be configured
  # true: an api key will be auto-generated
  # string: the given string will be set as an apikey
  apiKey: false

rabbitmq:
  auth:
    username: kibernetika
    password: Privet1961
    erlangCookie: kibernetika
  resourcesPreset: small
  replicaCount: 1
queueWorker:
  replicas: 2
  resources:
    limits:
      memory: 1500Mi
    requests:
      memory: 500Mi

persistence:
  db:
    size: 30G
    storageClass: premium-rwo
  files:
    size: 100G
    storageClass: standard
ui:
  Image:
    Name: kuberlab/ira-chat-ui
    Tag: 1.3.141
  replicas: 2
wildcard:
  dns: '*.hotline.kibernetika.io'
  enabled: true
  googleProject: lucid-timing-151005
  serviceAccountJson: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
