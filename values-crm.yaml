api:
  Image:
    Name: kuberlab/ira-chat-api
    Tag: latest
  baseUrl: https://crm-builder.kibernetika.io
  db:
    dbname: ira
    enabled: true
    host: ira-chat-db
    password: Privet1961sneg
    user: postgres
  dns:
  - crm-builder.kibernetika.io
  envPrefix: dev
  envVars:
  - name: dummy
    value: value2
  - name: VECTORSTORE
    value: qdrant
  - name: VECTORSTORE_ON_DISK
    value: "true"
  replicas: 3
credentials:
  admins: WzQsNSw2XQo=
  google: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  smtp: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
crypto: jS2fKKFSlfco3mxLdfckd0ew
persistence:
  db:
    size: 10G
    storageClass: standard
  files:
    size: 20G
    storageClass: standard
qdrant:
  apiKey: false
  fullnameOverride: qdrant
  image:
    pullPolicy: IfNotPresent
    repository: docker.io/qdrant/qdrant
    tag: v1.13.2
  nameOverride: qdrant
  persistence:
    size: 20G
  resources:
    limits:
      memory: 8Gi
    requests:
      memory: 1Gi
rabbitmq:
  auth:
    username: kibernetika
    password: kibernetika
    erlangCookie: kibernetika
  resourcesPreset: small
  replicaCount: 1
queueWorker:
  replicas: 1
  resources:
    limits:
      memory: 1500Mi
    requests:
      memory: 500Mi
ui:
  Image:
    Name: kuberlab/ira-chat-ui
    Tag: latest
  replicas: 1
wildcard:
  dns: '*.crm-builder.kibernetika.io'
  enabled: true
  googleProject: lucid-timing-151005
  serviceAccountJson: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

